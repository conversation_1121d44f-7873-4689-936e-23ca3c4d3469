#ifndef SYSTEM_MONITOR_H
#define SYSTEM_MONITOR_H

/**
 * @file system_monitor.h
 * @brief 系统监控器 - 系统性能和健康状态监控
 * @details 监控系统资源使用、性能指标和健康状态
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <esp_system.h>
#include <esp_heap_caps.h>

#include "config/config.h"
#include "config/constants.h"
#include "storage/data_structures.h"

/**
 * @brief 系统监控器类
 * @details 监控系统性能和健康状态
 */
class SystemMonitor {
private:
    // 监控状态
    bool isMonitoring;
    uint32_t monitoringInterval;
    uint32_t lastMonitorTime;
    
    // 性能指标
    struct PerformanceMetrics {
        uint32_t cpuUsagePercent;
        uint32_t memoryUsagePercent;
        uint32_t freeHeapBytes;
        uint32_t minFreeHeapBytes;
        uint32_t maxAllocHeapBytes;
        uint32_t psramUsagePercent;
        float cpuTemperature;
        uint32_t uptimeSeconds;
        uint32_t wifiSignalStrength;
        uint32_t taskCount;
        
        PerformanceMetrics() : cpuUsagePercent(0), memoryUsagePercent(0), 
                              freeHeapBytes(0), minFreeHeapBytes(0), 
                              maxAllocHeapBytes(0), psramUsagePercent(0),
                              cpuTemperature(0.0), uptimeSeconds(0),
                              wifiSignalStrength(0), taskCount(0) {}
    };
    
    PerformanceMetrics currentMetrics;
    PerformanceMetrics peakMetrics;
    
    // 健康状态
    struct HealthStatus {
        bool memoryHealthy;
        bool storageHealthy;
        bool wifiHealthy;
        bool temperatureHealthy;
        bool tasksHealthy;
        String lastError;
        uint32_t errorCount;
        
        HealthStatus() : memoryHealthy(true), storageHealthy(true),
                        wifiHealthy(true), temperatureHealthy(true),
                        tasksHealthy(true), errorCount(0) {}
    };
    
    HealthStatus healthStatus;
    
    // 历史数据
    std::vector<PerformanceMetrics> metricsHistory;
    uint32_t maxHistorySize;
    
    // 阈值配置
    uint32_t memoryWarningThreshold;
    uint32_t memoryErrorThreshold;
    float temperatureWarningThreshold;
    float temperatureErrorThreshold;
    int32_t wifiSignalWarningThreshold;

public:
    /**
     * @brief 构造函数
     */
    SystemMonitor();
    
    /**
     * @brief 析构函数
     */
    ~SystemMonitor();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * @brief 初始化系统监控器
     * @return bool 初始化是否成功
     */
    bool begin();
    
    /**
     * @brief 系统监控器主循环
     */
    void loop();
    
    /**
     * @brief 停止系统监控器
     */
    void stop();
    
    // ==================== 监控控制 ====================
    
    /**
     * @brief 开始监控
     * @param interval 监控间隔(ms)
     */
    void startMonitoring(uint32_t interval = 5000);
    
    /**
     * @brief 停止监控
     */
    void stopMonitoring();
    
    /**
     * @brief 检查是否正在监控
     * @return bool 是否正在监控
     */
    bool isMonitoringActive() const { return isMonitoring; }
    
    // ==================== 性能指标获取 ====================
    
    /**
     * @brief 获取当前性能指标
     * @return JsonDocument 性能指标
     */
    JsonDocument getCurrentMetrics();
    
    /**
     * @brief 获取峰值性能指标
     * @return JsonDocument 峰值指标
     */
    JsonDocument getPeakMetrics() const;
    
    /**
     * @brief 获取性能历史数据
     * @param count 获取数量 (0表示全部)
     * @return JsonDocument 历史数据
     */
    JsonDocument getMetricsHistory(uint32_t count = 0) const;
    
    /**
     * @brief 重置峰值指标
     */
    void resetPeakMetrics();
    
    /**
     * @brief 清空历史数据
     */
    void clearHistory();
    
    // ==================== 健康状态检查 ====================
    
    /**
     * @brief 获取系统健康状态
     * @return JsonDocument 健康状态
     */
    JsonDocument getHealthStatus();
    
    /**
     * @brief 检查内存健康状态
     * @return bool 内存是否健康
     */
    bool checkMemoryHealth();
    
    /**
     * @brief 检查存储健康状态
     * @return bool 存储是否健康
     */
    bool checkStorageHealth();
    
    /**
     * @brief 检查WiFi健康状态
     * @return bool WiFi是否健康
     */
    bool checkWiFiHealth();
    
    /**
     * @brief 检查温度健康状态
     * @return bool 温度是否健康
     */
    bool checkTemperatureHealth();
    
    /**
     * @brief 检查任务健康状态
     * @return bool 任务是否健康
     */
    bool checkTasksHealth();
    
    // ==================== 系统信息 ====================
    
    /**
     * @brief 获取系统信息
     * @return JsonDocument 系统信息
     */
    JsonDocument getSystemInfo() const;
    
    /**
     * @brief 获取任务信息
     * @return JsonDocument 任务信息
     */
    JsonDocument getTaskInfo() const;
    
    /**
     * @brief 获取内存信息
     * @return JsonDocument 内存信息
     */
    JsonDocument getMemoryInfo() const;
    
    /**
     * @brief 获取存储信息
     * @return JsonDocument 存储信息
     */
    JsonDocument getStorageInfo() const;
    
    // ==================== 配置管理 ====================
    
    /**
     * @brief 设置监控间隔
     * @param interval 间隔时间(ms)
     */
    void setMonitoringInterval(uint32_t interval) { monitoringInterval = interval; }
    
    /**
     * @brief 设置历史数据最大大小
     * @param maxSize 最大大小
     */
    void setMaxHistorySize(uint32_t maxSize) { maxHistorySize = maxSize; }
    
    /**
     * @brief 设置内存警告阈值
     * @param warningThreshold 警告阈值(%)
     * @param errorThreshold 错误阈值(%)
     */
    void setMemoryThresholds(uint32_t warningThreshold, uint32_t errorThreshold);
    
    /**
     * @brief 设置温度警告阈值
     * @param warningThreshold 警告阈值(°C)
     * @param errorThreshold 错误阈值(°C)
     */
    void setTemperatureThresholds(float warningThreshold, float errorThreshold);
    
    /**
     * @brief 设置WiFi信号警告阈值
     * @param warningThreshold 警告阈值(dBm)
     */
    void setWiFiSignalThreshold(int32_t warningThreshold);

private:
    // ==================== 私有方法 ====================
    
    /**
     * @brief 更新性能指标
     */
    void updateMetrics();
    
    /**
     * @brief 更新健康状态
     */
    void updateHealthStatus();
    
    /**
     * @brief 添加历史记录
     * @param metrics 性能指标
     */
    void addToHistory(const PerformanceMetrics& metrics);
    
    /**
     * @brief 获取CPU使用率
     * @return uint32_t CPU使用率(%)
     */
    uint32_t getCPUUsage();
    
    /**
     * @brief 获取内存使用率
     * @return uint32_t 内存使用率(%)
     */
    uint32_t getMemoryUsage();
    
    /**
     * @brief 获取PSRAM使用率
     * @return uint32_t PSRAM使用率(%)
     */
    uint32_t getPSRAMUsage();
    
    /**
     * @brief 获取CPU温度
     * @return float CPU温度(°C)
     */
    float getCPUTemperature();
    
    /**
     * @brief 获取WiFi信号强度
     * @return int32_t 信号强度(dBm)
     */
    int32_t getWiFiSignalStrength();
    
    /**
     * @brief 获取任务数量
     * @return uint32_t 任务数量
     */
    uint32_t getTaskCount();
    
    /**
     * @brief 记录监控日志
     * @param level 日志级别
     * @param message 日志消息
     */
    void logMonitor(const String& level, const String& message);
};

#endif // SYSTEM_MONITOR_H
