[env:esp32-s3-devkitc-1]
; 使用2025年最新稳定版本 - Arduino Core 2.0.17，ESP-IDF 5.4.1
platform = espressif32@6.11.0
board = esp32-s3-devkitc-1
framework = arduino

; 库依赖 - 2025年最新稳定版本
lib_deps = 
    bblanchon/Arduino<PERSON>son@7.4.2                    ; JSON处理库 (2025年6月20日)
    ESP32Async/ESPAsyncWebServer@3.7.9             ; 异步Web服务器 (2025年6月30日)
    ESP32Async/AsyncTCP@3.4.5                      ; 异步TCP库 (2025年7月3日)
    crankyoldgit/IRremoteESP8266@2.8.6             ; 红外遥控库 (最新稳定版)
    ; LittleFS已内置在Arduino Core 2.0.17中

; ESP32-S3专用编译标志
build_flags =
    ; 硬件配置
    -DBOARD_HAS_PSRAM=0                    ; 禁用PSRAM (硬件问题)
    -DCONFIG_SPIRAM_SUPPORT=0              ; 禁用SPIRAM支持
    -DCONFIG_ESP32S3_DEFAULT_CPU_FREQ_240  ; 240MHz CPU频率
    
    ; 调试配置
    -DCORE_DEBUG_LEVEL=3                   ; 调试级别
    -DCONFIG_ARDUHAL_LOG_COLORS=1          ; 彩色日志输出
    
    ; AsyncTCP优化配置
    -DCONFIG_ASYNC_TCP_STACK_SIZE=16384    ; TCP堆栈大小
    -DCONFIG_ASYNC_TCP_PRIORITY=10         ; TCP任务优先级
    -DCONFIG_ASYNC_TCP_RUNNING_CORE=0      ; TCP运行在Core 0
    -DCONFIG_ASYNC_TCP_USE_WDT=1           ; 启用看门狗
    
    ; ArduinoJson优化配置
    -DARDUINOJSON_ENABLE_PROGMEM=1         ; 启用PROGMEM支持
    -DARDUINOJSON_DECODE_UNICODE=1         ; 启用Unicode解码
    -DARDUINOJSON_USE_DOUBLE=0             ; 禁用double类型节省内存

    ; ESPAsyncWebServer配置
    -DASYNCWEBSERVER_REGEX                 ; 启用正则表达式支持
    
    ; WiFi稳定性优化配置 - 专为AP模式优化
    -DCONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM=16    ; 增加静态RX缓冲区
    -DCONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM=64   ; 增加动态RX缓冲区
    -DCONFIG_ESP32_WIFI_TX_BUFFER_TYPE=1           ; 使用动态TX缓冲区
    -DCONFIG_ESP32_WIFI_AMPDU_TX_ENABLED=0         ; 禁用AMPDU TX（提高稳定性）
    -DCONFIG_ESP32_WIFI_AMPDU_RX_ENABLED=0         ; 禁用AMPDU RX（提高稳定性）
    -DCONFIG_ESP32_WIFI_NVS_ENABLED=1              ; 启用WiFi NVS存储

; 分区表配置 - 使用自定义分区表：大应用分区 + LittleFS文件系统
board_build.partitions = partitions.csv
board_build.filesystem = littlefs

; 上传和监控配置
upload_speed = 115200
monitor_speed = 115200
monitor_filters = 
    esp32_exception_decoder
    time

; Flash配置 - ESP32-S3优化
board_build.flash_mode = qio
board_build.f_flash = 80000000L
board_build.f_cpu = 240000000L

; 内存优化配置
board_build.arduino.memory_type = qio_opi
