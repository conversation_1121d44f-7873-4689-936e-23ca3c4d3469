/**
 * R1系统 - 数据格式验证工具
 * 确保系统中所有数据格式的一致性
 */

class DataValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
  }

  /**
   * 验证信号对象格式
   * @param {object} signal - 信号对象
   * @returns {boolean} 是否有效
   */
  validateSignal(signal) {
    this.errors = [];
    this.warnings = [];

    // 必需字段检查
    const requiredFields = ['id', 'name', 'type', 'data', 'frequency', 'created', 'lastSent', 'sentCount'];
    
    for (const field of requiredFields) {
      if (!(field in signal)) {
        this.errors.push(`缺少必需字段: ${field}`);
      }
    }

    // ID格式检查
    if (signal.id && !this.validateId(signal.id, 'signal')) {
      this.errors.push(`ID格式错误: ${signal.id}，应为 signal_8位数字`);
    }

    // 时间戳格式检查
    if (signal.created && !this.validateTimestamp(signal.created)) {
      this.errors.push(`created时间戳格式错误: ${signal.created}，应为毫秒时间戳`);
    }

    if (signal.lastSent !== null && signal.lastSent && !this.validateTimestamp(signal.lastSent)) {
      this.errors.push(`lastSent时间戳格式错误: ${signal.lastSent}，应为毫秒时间戳`);
    }

    // 类型检查
    const validTypes = ['tv', 'ac', 'fan', 'light', 'other'];
    if (signal.type && !validTypes.includes(signal.type)) {
      this.warnings.push(`信号类型 "${signal.type}" 不在推荐列表中: ${validTypes.join(', ')}`);
    }

    // 数据格式检查
    if (signal.data && !this.validateIRData(signal.data)) {
      this.warnings.push(`红外数据格式可能不正确: ${signal.data}`);
    }

    // 频率检查
    if (signal.frequency && !this.validateFrequency(signal.frequency)) {
      this.warnings.push(`载波频率格式可能不正确: ${signal.frequency}`);
    }

    // 计数器检查
    if (signal.sentCount && (!Number.isInteger(signal.sentCount) || signal.sentCount < 0)) {
      this.errors.push(`sentCount应为非负整数: ${signal.sentCount}`);
    }

    return this.errors.length === 0;
  }

  /**
   * 验证API响应格式
   * @param {object} response - API响应
   * @returns {boolean} 是否有效
   */
  validateAPIResponse(response) {
    this.errors = [];
    this.warnings = [];

    // 必需字段检查
    if (!('success' in response)) {
      this.errors.push('缺少必需字段: success');
    }

    if (!('timestamp' in response)) {
      this.errors.push('缺少必需字段: timestamp');
    }

    // 时间戳检查
    if (response.timestamp && !this.validateTimestamp(response.timestamp)) {
      this.errors.push(`timestamp格式错误: ${response.timestamp}，应为毫秒时间戳`);
    }

    // 成功响应检查
    if (response.success === true) {
      if (!('data' in response) && !('message' in response)) {
        this.warnings.push('成功响应应包含data或message字段');
      }
    }

    // 错误响应检查
    if (response.success === false) {
      if (!('error' in response)) {
        this.errors.push('错误响应缺少error字段');
      }
    }

    return this.errors.length === 0;
  }

  /**
   * 验证WebSocket消息格式
   * @param {object} message - WebSocket消息
   * @returns {boolean} 是否有效
   */
  validateWebSocketMessage(message) {
    this.errors = [];
    this.warnings = [];

    // 必需字段检查
    const requiredFields = ['type', 'payload', 'timestamp'];
    
    for (const field of requiredFields) {
      if (!(field in message)) {
        this.errors.push(`缺少必需字段: ${field}`);
      }
    }

    // 时间戳检查
    if (message.timestamp && !this.validateTimestamp(message.timestamp)) {
      this.errors.push(`timestamp格式错误: ${message.timestamp}，应为毫秒时间戳`);
    }

    // 消息类型检查
    const validTypes = [
      'connected', 'disconnected', 'error',
      'signal_learned', 'signal_sent', 'signal_deleted',
      'status_update', 'echo'
    ];
    
    if (message.type && !validTypes.includes(message.type)) {
      this.warnings.push(`消息类型 "${message.type}" 不在标准列表中`);
    }

    return this.errors.length === 0;
  }

  /**
   * 验证事件数据格式
   * @param {object} eventData - 事件数据
   * @returns {boolean} 是否有效
   */
  validateEventData(eventData) {
    this.errors = [];
    this.warnings = [];

    // 基本字段检查
    if (eventData.timestamp && !this.validateTimestamp(eventData.timestamp)) {
      this.errors.push(`timestamp格式错误: ${eventData.timestamp}，应为毫秒时间戳`);
    }

    return this.errors.length === 0;
  }

  /**
   * 验证ID格式
   * @param {string} id - ID字符串
   * @param {string} type - 期望的类型前缀
   * @returns {boolean} 是否有效
   */
  validateId(id, type) {
    if (typeof id !== 'string') return false;
    
    // 统一ID格式: 类型_8位数字
    const pattern = new RegExp(`^${type}_\\d{8}$`);
    return pattern.test(id);
  }

  /**
   * 验证时间戳格式
   * @param {number} timestamp - 时间戳
   * @returns {boolean} 是否有效
   */
  validateTimestamp(timestamp) {
    if (typeof timestamp !== 'number') return false;
    
    // 毫秒时间戳应该是13位数字
    const timestampStr = timestamp.toString();
    if (timestampStr.length !== 13) return false;
    
    // 检查是否在合理范围内 (2020-2030年)
    const year2020 = 1577836800000; // 2020-01-01
    const year2030 = 1893456000000; // 2030-01-01
    
    return timestamp >= year2020 && timestamp <= year2030;
  }

  /**
   * 验证红外数据格式
   * @param {string} data - 红外数据
   * @returns {boolean} 是否有效
   */
  validateIRData(data) {
    if (typeof data !== 'string') return false;
    
    // 常见红外数据格式
    const patterns = [
      /^NEC:0x[0-9A-F]{8}$/i,           // NEC格式
      /^RC5:0x[0-9A-F]{4}$/i,           // RC5格式
      /^SONY:0x[0-9A-F]{5}$/i,          // Sony格式
      /^RAW:\d+(,\d+)*$/,               // 原始格式
      /^mock_ir_data_\w+$/              // 测试数据格式
    ];
    
    return patterns.some(pattern => pattern.test(data));
  }

  /**
   * 验证载波频率格式
   * @param {string} frequency - 载波频率
   * @returns {boolean} 是否有效
   */
  validateFrequency(frequency) {
    if (typeof frequency !== 'string') return false;
    
    // 常见载波频率
    const validFrequencies = ['38000', '36000', '40000', '56000'];
    return validFrequencies.includes(frequency) || /^\d{5}$/.test(frequency);
  }

  /**
   * 获取验证错误
   * @returns {Array} 错误列表
   */
  getErrors() {
    return [...this.errors];
  }

  /**
   * 获取验证警告
   * @returns {Array} 警告列表
   */
  getWarnings() {
    return [...this.warnings];
  }

  /**
   * 获取验证报告
   * @returns {object} 验证报告
   */
  getReport() {
    return {
      valid: this.errors.length === 0,
      errors: this.getErrors(),
      warnings: this.getWarnings(),
      timestamp: Date.now()
    };
  }

  /**
   * 批量验证信号数组
   * @param {Array} signals - 信号数组
   * @returns {object} 批量验证结果
   */
  validateSignals(signals) {
    const results = {
      total: signals.length,
      valid: 0,
      invalid: 0,
      errors: [],
      warnings: []
    };

    signals.forEach((signal, index) => {
      if (this.validateSignal(signal)) {
        results.valid++;
      } else {
        results.invalid++;
        results.errors.push({
          index,
          signalId: signal.id,
          errors: this.getErrors(),
          warnings: this.getWarnings()
        });
      }
    });

    return results;
  }

  /**
   * 修复信号对象格式
   * @param {object} signal - 信号对象
   * @returns {object} 修复后的信号对象
   */
  fixSignalFormat(signal) {
    const fixed = { ...signal };

    // 修复ID格式
    if (!this.validateId(fixed.id, 'signal')) {
      fixed.id = R1Utils.generateId('signal');
    }

    // 修复时间戳格式
    if (!this.validateTimestamp(fixed.created)) {
      fixed.created = Date.now();
    }

    // 确保必需字段存在
    if (!fixed.lastSent) fixed.lastSent = null;
    if (!fixed.sentCount) fixed.sentCount = 0;
    if (!fixed.frequency) fixed.frequency = '38000';
    if (!fixed.type) fixed.type = 'other';
    if (!fixed.description) fixed.description = '';

    return fixed;
  }
}

// 创建全局验证器实例
window.R1DataValidator = new DataValidator();

// 导出验证器类
window.DataValidator = DataValidator;
