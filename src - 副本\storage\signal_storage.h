#ifndef SIGNAL_STORAGE_H
#define SIGNAL_STORAGE_H

/**
 * @file signal_storage.h
 * @brief 信号存储管理器 - 红外信号数据持久化
 * @details 管理红外信号的存储、检索、备份和恢复
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <LittleFS.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/semphr.h>
#include <vector>
#include <map>

#include "config/config.h"
#include "config/constants.h"
#include "storage/data_structures.h"

/**
 * @brief 信号存储管理器类
 * @details 负责红外信号数据的持久化存储和管理
 */
class SignalStorage {
private:
    // 存储配置
    String signalsDirectory;
    String indexFileName;
    String backupDirectory;
    
    // 内存缓存
    std::map<String, SignalData> signalCache;
    std::vector<String> signalIndex;
    
    // 同步原语
    SemaphoreHandle_t storageMutex;
    
    // 存储状态
    bool isInitialized;
    uint32_t lastSaveTime;
    uint32_t autoSaveInterval;
    bool isDirty;  // 缓存是否有未保存的更改
    
    // 统计信息
    uint32_t totalReads;
    uint32_t totalWrites;
    uint32_t cacheHits;
    uint32_t cacheMisses;
    
    // 存储限制
    uint32_t maxSignals;
    uint32_t maxFileSize;

public:
    /**
     * @brief 构造函数
     */
    SignalStorage();
    
    /**
     * @brief 析构函数
     */
    ~SignalStorage();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * @brief 初始化信号存储
     * @return bool 初始化是否成功
     */
    bool begin();
    
    /**
     * @brief 存储管理器主循环
     */
    void loop();
    
    /**
     * @brief 停止存储管理器
     */
    void stop();
    
    // ==================== 信号CRUD操作 ====================
    
    /**
     * @brief 保存信号
     * @param signal 信号数据
     * @return bool 保存是否成功
     */
    bool saveSignal(const SignalData& signal);
    
    /**
     * @brief 获取信号
     * @param signalId 信号ID
     * @return SignalData 信号数据
     */
    SignalData getSignal(const String& signalId);
    
    /**
     * @brief 更新信号
     * @param signal 信号数据
     * @return bool 更新是否成功
     */
    bool updateSignal(const SignalData& signal);
    
    /**
     * @brief 删除信号
     * @param signalId 信号ID
     * @return bool 删除是否成功
     */
    bool deleteSignal(const String& signalId);
    
    /**
     * @brief 检查信号是否存在
     * @param signalId 信号ID
     * @return bool 是否存在
     */
    bool signalExists(const String& signalId);
    
    // ==================== 批量操作 ====================
    
    /**
     * @brief 获取所有信号
     * @return std::vector<SignalData> 信号列表
     */
    std::vector<SignalData> getAllSignals();
    
    /**
     * @brief 按类型获取信号
     * @param type 信号类型
     * @return std::vector<SignalData> 信号列表
     */
    std::vector<SignalData> getSignalsByType(const String& type);
    
    /**
     * @brief 搜索信号
     * @param keyword 关键词
     * @return std::vector<SignalData> 匹配的信号列表
     */
    std::vector<SignalData> searchSignals(const String& keyword);
    
    /**
     * @brief 清空所有信号
     * @return bool 清空是否成功
     */
    bool clearAllSignals();
    
    /**
     * @brief 批量导入信号
     * @param signals 信号列表
     * @return uint32_t 成功导入的数量
     */
    uint32_t importSignals(const std::vector<SignalData>& signals);
    
    /**
     * @brief 批量导出信号
     * @return JsonDocument 导出的信号数据
     */
    JsonDocument exportSignals();
    
    // ==================== 状态查询 ====================
    
    /**
     * @brief 获取信号数量
     * @return uint32_t 信号数量
     */
    uint32_t getSignalCount() const { return signalCache.size(); }
    
    /**
     * @brief 获取存储使用情况
     * @return JsonDocument 存储使用情况
     */
    JsonDocument getStorageUsage() const;
    
    /**
     * @brief 获取存储统计信息
     * @return JsonDocument 统计信息
     */
    JsonDocument getStorageStats() const;
    
    /**
     * @brief 检查存储健康状态
     * @return bool 存储是否健康
     */
    bool checkStorageHealth() const;
    
    // ==================== 缓存管理 ====================
    
    /**
     * @brief 刷新缓存到磁盘
     * @return bool 刷新是否成功
     */
    bool flushCache();
    
    /**
     * @brief 重新加载缓存
     * @return bool 重新加载是否成功
     */
    bool reloadCache();

    /**
     * @brief 重新加载缓存（限制数量，避免内存不足导致AP热点不稳定）
     * @return bool 重新加载是否成功
     */
    bool reloadCacheLimited();

    /**
     * @brief 清空缓存
     */
    void clearCache();
    
    /**
     * @brief 设置自动保存间隔
     * @param interval 间隔时间(ms)
     */
    void setAutoSaveInterval(uint32_t interval) { autoSaveInterval = interval; }
    
    // ==================== 备份和恢复 ====================
    
    /**
     * @brief 创建备份
     * @param backupName 备份名称
     * @return bool 备份是否成功
     */
    bool createBackup(const String& backupName = "");
    
    /**
     * @brief 恢复备份
     * @param backupName 备份名称
     * @return bool 恢复是否成功
     */
    bool restoreBackup(const String& backupName);
    
    /**
     * @brief 获取备份列表
     * @return std::vector<String> 备份列表
     */
    std::vector<String> getBackupList() const;
    
    /**
     * @brief 删除备份
     * @param backupName 备份名称
     * @return bool 删除是否成功
     */
    bool deleteBackup(const String& backupName);
    
    // ==================== 配置管理 ====================
    
    /**
     * @brief 设置最大信号数量
     * @param maxCount 最大数量
     */
    void setMaxSignals(uint32_t maxCount) { maxSignals = maxCount; }
    
    /**
     * @brief 设置最大文件大小
     * @param maxSize 最大大小(字节)
     */
    void setMaxFileSize(uint32_t maxSize) { maxFileSize = maxSize; }

private:
    // ==================== 私有方法 ====================
    
    /**
     * @brief 初始化存储目录
     * @return bool 初始化是否成功
     */
    bool initializeDirectories();
    
    /**
     * @brief 加载信号索引
     * @return bool 加载是否成功
     */
    bool loadSignalIndex();
    
    /**
     * @brief 保存信号索引
     * @return bool 保存是否成功
     */
    bool saveSignalIndex();
    
    /**
     * @brief 加载单个信号文件
     * @param signalId 信号ID
     * @return SignalData 信号数据
     */
    SignalData loadSignalFile(const String& signalId);
    
    /**
     * @brief 保存单个信号文件
     * @param signal 信号数据
     * @return bool 保存是否成功
     */
    bool saveSignalFile(const SignalData& signal);
    
    /**
     * @brief 删除信号文件
     * @param signalId 信号ID
     * @return bool 删除是否成功
     */
    bool deleteSignalFile(const String& signalId);
    
    /**
     * @brief 获取信号文件路径
     * @param signalId 信号ID
     * @return String 文件路径
     */
    String getSignalFilePath(const String& signalId) const;
    
    /**
     * @brief 获取备份文件路径
     * @param backupName 备份名称
     * @return String 备份路径
     */
    String getBackupFilePath(const String& backupName) const;
    
    /**
     * @brief 验证信号数据
     * @param signal 信号数据
     * @return bool 是否有效
     */
    bool validateSignalData(const SignalData& signal) const;
    
    /**
     * @brief 生成备份名称
     * @return String 备份名称
     */
    String generateBackupName() const;
    
    /**
     * @brief 清理过期备份
     */
    void cleanupOldBackups();
    
    /**
     * @brief 记录存储日志
     * @param level 日志级别
     * @param message 日志消息
     */
    void logStorage(const String& level, const String& message);
};

#endif // SIGNAL_STORAGE_H
