#pragma once

#include <Arduino.h>
#include <vector>
#include <functional>
#include "../storage/timer_data.h"
#include "../storage/signal_storage.h"
#include "../ir/ir_manager.h"

/**
 * @brief 后端定时器管理器 - 负责定时任务的存储、调度和执行
 */
class TimerManagerBackend {
private:
    static TimerManagerBackend* instance;
    
    // 任务存储
    std::vector<TimerTask> tasks;
    TimerConfig config;
    TimerSystemStatus status;

    // 组件引用
    SignalStorage* signalStorage;
    IRSignalManager* irManager;

    // 事件回调函数类型
    typedef std::function<void(const String&, const String&, bool)> TaskExecutionCallback;
    TaskExecutionCallback onTaskExecuted;
    
    // 运行状态
    bool isRunning;
    uint32_t lastCheckTime;
    uint32_t checkInterval;
    
    // 执行队列
    std::vector<String> executionQueue;
    bool isExecuting;
    
public:
    TimerManagerBackend();
    ~TimerManagerBackend();
    
    // 单例模式
    static TimerManagerBackend* getInstance();
    
    // 初始化和控制
    bool begin(SignalStorage* storage, IRSignalManager* ir);
    void loop();
    void stop();
    
    // 任务管理
    String createTask(const TimerTask& task);
    bool updateTask(const String& taskId, const TimerTask& task);
    bool deleteTask(const String& taskId);
    TimerTask* getTask(const String& taskId);
    std::vector<TimerTask> getAllTasks() const;
    bool clearAllTasks();
    
    // 任务控制
    bool enableTask(const String& taskId, bool enabled);
    bool executeTask(const String& taskId);
    bool isTaskDue(const String& taskId) const;
    
    // 系统控制
    bool setMasterEnabled(bool enabled);
    bool isMasterEnabled() const { return config.masterEnabled; }
    TimerSystemStatus getSystemStatus() const;
    TimerConfig getConfig() const { return config; }
    bool updateConfig(const TimerConfig& newConfig);
    
    // 统计信息
    uint32_t getTaskCount() const { return tasks.size(); }
    uint32_t getActiveTaskCount() const;
    uint32_t getTotalExecutions() const;
    TimerTask* getNextDueTask();
    
    // 数据持久化
    bool saveTasksToStorage();
    bool loadTasksFromStorage();

    // 事件回调设置
    void setTaskExecutionCallback(TaskExecutionCallback callback) { onTaskExecuted = callback; }
    
private:
    // 内部方法
    void checkDueTasks();
    void executeTaskInternal(TimerTask& task);
    void updateSystemStatus();
    void cleanupExpiredTasks();
    String generateTaskId();
    bool validateTask(const TimerTask& task) const;
    void logTaskExecution(const TimerTask& task, bool success);
    
    // 信号发射
    bool emitSignals(const std::vector<String>& signalIds);
    bool emitSingleSignal(const String& signalId);
};

// 全局定时器管理器实例
extern TimerManagerBackend* timerManagerBackend;

// 便捷函数
TimerManagerBackend* getTimerManagerBackend();
