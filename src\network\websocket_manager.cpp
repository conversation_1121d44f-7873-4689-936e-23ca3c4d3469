#include "websocket_manager.h"
#include "core/system_manager.h"
#include "../core/time_manager.h"

// 静态成员初始化
WebSocketManager* WebSocketManager::instance = nullptr;

WebSocketManager::WebSocketManager()
    : wsServer(WEBSOCKET_PORT)
    , ws("/ws")
    , totalMessagesSent(0)
    , totalMessagesReceived(0)
    , totalConnections(0)
    , totalDisconnections(0)
    , isRunning(false)
    , startTime(0)
    , lastHeartbeatCheck(0)
    , heartbeatInterval(60000)  // 60秒心跳 (减少频率)
    , clientTimeout(180000)     // 180秒超时 (3分钟，更宽松)
    , messagesSent(0) {         // 消息发送计数
    
    instance = this;
}

WebSocketManager::~WebSocketManager() {
    stop();
    instance = nullptr;
}

bool WebSocketManager::begin() {
    Serial.printf("🔌 初始化WebSocket服务器 (端口: %d)...\n", WEBSOCKET_PORT);
    
    // 设置WebSocket事件处理器
    ws.onEvent(handleWebSocketEvent);
    
    // 将WebSocket添加到独立服务器
    wsServer.addHandler(&ws);
    
    // 启动WebSocket服务器
    wsServer.begin();
    
    isRunning = true;
    startTime = millis();
    lastHeartbeatCheck = millis();
    
    // 根据WiFi模式显示正确的IP地址
    IPAddress serverIP;
    if (WiFi.getMode() == WIFI_AP || WiFi.getMode() == WIFI_AP_STA) {
        serverIP = WiFi.softAPIP();
    } else if (WiFi.getMode() == WIFI_STA && WiFi.isConnected()) {
        serverIP = WiFi.localIP();
    } else {
        serverIP = IPAddress(192, 168, 4, 1);  // 默认AP IP
    }

    Serial.printf("✅ WebSocket服务器启动成功: ws://%s:%d/ws\n",
                  serverIP.toString().c_str(), WEBSOCKET_PORT);
    
    return true;
}

void WebSocketManager::loop() {
    if (!isRunning) {
        return;
    }
    
    uint32_t currentTime = millis();
    
    // 定期心跳检测
    if (currentTime - lastHeartbeatCheck >= heartbeatInterval) {
        lastHeartbeatCheck = currentTime;
        performHeartbeatCheck();
    }
    
    // 清理断开的客户端
    cleanupDisconnectedClients();
}

void WebSocketManager::stop() {
    if (isRunning) {
        Serial.println("🛑 停止WebSocket服务器");
        
        // 断开所有客户端
        disconnectAllClients();
        
        // 停止服务器
        wsServer.end();
        
        isRunning = false;
    }
}

bool WebSocketManager::hasActiveClients() const {
    for (const auto& clientPair : connectedClients) {
        if (clientPair.second.isActive) {
            return true;
        }
    }
    return false;
}

WebSocketManager::ClientInfo WebSocketManager::getClientInfo(uint32_t clientId) const {
    auto it = connectedClients.find(clientId);
    if (it != connectedClients.end()) {
        return it->second;
    }
    return ClientInfo();
}

void WebSocketManager::disconnectClient(uint32_t clientId) {
    AsyncWebSocketClient* client = ws.client(clientId);
    if (client) {
        logWebSocket("INFO", "主动断开客户端", clientId);
        client->close();
    }
}

void WebSocketManager::disconnectAllClients() {
    logWebSocket("INFO", "断开所有客户端");
    ws.closeAll();
    connectedClients.clear();
}

void WebSocketManager::broadcastMessage(const String& message) {
    if (!isRunning || !hasActiveClients()) {
        return;
    }
    
    ws.textAll(message);
    totalMessagesSent++;
    messagesSent++;
    
    if (DEBUG_LEVEL >= 4) {
        logWebSocket("DEBUG", "广播消息: " + message.substring(0, 100) + "...");
    }
}

void WebSocketManager::sendToClient(uint32_t clientId, const String& message) {
    AsyncWebSocketClient* client = ws.client(clientId);
    if (client && client->status() == WS_CONNECTED) {
        client->text(message);
        totalMessagesSent++;
        messagesSent++;
        
        if (DEBUG_LEVEL >= 4) {
            logWebSocket("DEBUG", "发送消息", clientId);
        }
    }
}

void WebSocketManager::broadcastJson(const JsonDocument& doc) {
    String message;
    serializeJson(doc, message);
    broadcastMessage(message);
}

void WebSocketManager::sendJsonToClient(uint32_t clientId, const JsonDocument& doc) {
    String message;
    serializeJson(doc, message);
    sendToClient(clientId, message);
}

// ==================== 前端期望的WebSocket事件 ====================

void WebSocketManager::sendConnectedEvent(AsyncWebSocketClient *client) {
    JsonDocument payload;
    payload["message"] = "WebSocket连接成功";
    payload["clientId"] = String("client_") + String(client->id());
    payload["serverTime"] = getCurrentUnixMs();
    payload["serverVersion"] = FIRMWARE_VERSION;
    
    String message = createWebSocketMessage("connected", payload);
    client->text(message);
    
    logWebSocket("INFO", "发送连接事件", client->id());
}

void WebSocketManager::sendSignalLearnedEvent(const JsonDocument& signalData) {
    JsonDocument payload;
    payload["signal"] = signalData;
    payload["success"] = true;
    payload["timestamp"] = getCurrentUnixMs();
    
    String message = createWebSocketMessage("signal_learned", payload);
    broadcastMessage(message);
    
    logWebSocket("INFO", "发送信号学习事件");
}

void WebSocketManager::sendSignalSentEvent(const String& signalId, bool success) {
    JsonDocument payload;
    payload["signalId"] = signalId;
    payload["success"] = success;
    payload["timestamp"] = getCurrentUnixMs();
    
    String message = createWebSocketMessage("signal_sent", payload);
    broadcastMessage(message);
    
    logWebSocket("INFO", "发送信号发射事件: " + signalId);
}

void WebSocketManager::sendStatusUpdateEvent(const JsonDocument& statusData) {
    JsonDocument payload = statusData;
    payload["timestamp"] = getCurrentUnixMs();
    
    String message = createWebSocketMessage("status_update", payload);
    broadcastMessage(message);
    
    if (DEBUG_LEVEL >= 4) {
        logWebSocket("DEBUG", "发送状态更新事件");
    }
}

void WebSocketManager::sendErrorEvent(const String& error, const String& context) {
    JsonDocument payload;
    payload["error"] = error;
    payload["context"] = context;
    payload["timestamp"] = getCurrentUnixMs();
    payload["severity"] = "error";
    
    String message = createWebSocketMessage("error", payload);
    broadcastMessage(message);
    
    logWebSocket("ERROR", "发送错误事件: " + error);
}

void WebSocketManager::sendCustomEvent(const String& eventType, const JsonDocument& payload) {
    String message = createWebSocketMessage(eventType, payload);
    broadcastMessage(message);
    
    logWebSocket("INFO", "发送自定义事件: " + eventType);
}

JsonDocument WebSocketManager::getServerStats() const {
    JsonDocument doc;
    
    doc["is_running"] = isRunning;
    doc["port"] = WEBSOCKET_PORT;
    doc["uptime"] = (millis() - startTime) / 1000;
    doc["connected_clients"] = connectedClients.size();
    doc["total_connections"] = totalConnections;
    doc["total_disconnections"] = totalDisconnections;
    doc["messages_sent"] = totalMessagesSent;
    doc["messages_received"] = totalMessagesReceived;
    doc["heartbeat_interval"] = heartbeatInterval;
    doc["client_timeout"] = clientTimeout;
    
    return doc;
}

JsonDocument WebSocketManager::getClientsInfo() const {
    JsonDocument doc;
    JsonArray clients = doc["clients"].to<JsonArray>();

    for (const auto& clientPair : connectedClients) {
        const ClientInfo& info = clientPair.second;

        JsonObject clientObj = clients.add<JsonObject>();
        clientObj["id"] = info.id;
        clientObj["ip"] = info.ip.toString();
        clientObj["connect_time"] = info.connectTime;
        clientObj["last_ping"] = info.lastPing;
        clientObj["is_active"] = info.isActive;
        clientObj["user_agent"] = info.userAgent;
        clientObj["connected_duration"] = (millis() - info.connectTime) / 1000;
    }
    
    doc["total_clients"] = connectedClients.size();
    
    return doc;
}

// ==================== 私有方法实现 ====================

void WebSocketManager::handleWebSocketEvent(AsyncWebSocket *server, AsyncWebSocketClient *client,
                                           AwsEventType type, void *arg, uint8_t *data, size_t len) {
    if (!instance) {
        return;
    }
    
    switch (type) {
        case WS_EVT_CONNECT:
            instance->handleClientConnect(client);
            break;
            
        case WS_EVT_DISCONNECT:
            instance->handleClientDisconnect(client);
            break;
            
        case WS_EVT_DATA:
            instance->handleClientMessage(client, data, len);
            break;
            
        case WS_EVT_PONG:
            instance->handleClientPong(client);
            break;
            
        case WS_EVT_ERROR:
            instance->logWebSocket("ERROR", "WebSocket错误", client->id());
            break;
            
        default:
            break;
    }
}

void WebSocketManager::handleClientConnect(AsyncWebSocketClient *client) {
    ClientInfo info;
    info.id = client->id();
    info.ip = getClientIP(client);
    info.connectTime = millis();
    info.lastPing = millis();
    info.isActive = true;

    // User-Agent信息在WebSocket连接中不直接可用
    info.userAgent = "WebSocket Client";

    connectedClients[client->id()] = info;
    totalConnections++;

    logWebSocket("INFO", "客户端连接", client->id());

    // 发送连接成功事件
    sendConnectedEvent(client);

    // 通知系统管理器
    SystemManager& systemManager = SystemManager::getInstance();
    JsonDocument eventData;
    eventData["client_id"] = client->id();
    eventData["client_ip"] = info.ip.toString();
    eventData["total_clients"] = connectedClients.size();
    systemManager.sendSystemEvent("websocket_client_connected", eventData);
}

void WebSocketManager::handleClientDisconnect(AsyncWebSocketClient *client) {
    uint32_t clientId = client->id();

    auto it = connectedClients.find(clientId);
    if (it != connectedClients.end()) {
        connectedClients.erase(it);
    }

    totalDisconnections++;

    logWebSocket("INFO", "客户端断开", clientId);

    // 通知系统管理器
    SystemManager& systemManager = SystemManager::getInstance();
    JsonDocument eventData;
    eventData["client_id"] = clientId;
    eventData["total_clients"] = connectedClients.size();
    systemManager.sendSystemEvent("websocket_client_disconnected", eventData);
}

void WebSocketManager::handleClientMessage(AsyncWebSocketClient *client, uint8_t *data, size_t len) {
    totalMessagesReceived++;

    // 更新客户端活跃状态
    auto it = connectedClients.find(client->id());
    if (it != connectedClients.end()) {
        it->second.lastPing = millis();
        it->second.isActive = true;
    }

    // 解析JSON消息
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, data, len);

    if (error) {
        logWebSocket("ERROR", "JSON解析失败: " + String(error.c_str()), client->id());

        // 发送错误响应 - 使用标准WebSocket消息格式
        JsonDocument payload;
        payload["error"] = "Invalid JSON format";
        payload["code"] = "PARSE_ERROR";
        payload["message"] = "JSON格式错误";

        String message = createWebSocketMessage("error", payload);
        sendToClient(client->id(), message);
        return;
    }

    // 处理不同类型的消息
    String messageType = doc["type"].as<String>();

    if (messageType == "ping") {
        // 响应ping消息 - 使用标准WebSocket消息格式
        JsonDocument payload;
        payload["message"] = "pong";
        payload["serverTime"] = getCurrentUnixMs();

        String message = createWebSocketMessage("pong", payload);
        sendToClient(client->id(), message);

    } else if (messageType == "subscribe") {
        // 处理订阅请求 - 使用标准WebSocket消息格式
        String eventType = doc["event"].as<String>();
        logWebSocket("INFO", "客户端订阅事件: " + eventType, client->id());

        JsonDocument payload;
        payload["event"] = eventType;
        payload["status"] = "subscribed";
        payload["message"] = "订阅成功";

        String message = createWebSocketMessage("subscription_confirmed", payload);
        sendToClient(client->id(), message);

    } else {
        logWebSocket("WARN", "未知消息类型: " + messageType, client->id());
    }
}

void WebSocketManager::handleClientPong(AsyncWebSocketClient *client) {
    // 更新客户端心跳时间
    auto it = connectedClients.find(client->id());
    if (it != connectedClients.end()) {
        it->second.lastPing = millis();
        it->second.isActive = true;
    }

    if (DEBUG_LEVEL >= 4) {
        logWebSocket("DEBUG", "收到Pong", client->id());
    }
}

void WebSocketManager::performHeartbeatCheck() {
    uint32_t currentTime = millis();
    std::vector<uint32_t> timeoutClients;

    // 检查所有客户端的心跳状态
    for (auto& clientPair : connectedClients) {
        ClientInfo& info = clientPair.second;

        if (currentTime - info.lastPing > clientTimeout) {
            // 客户端超时
            timeoutClients.push_back(info.id);
            info.isActive = false;
        }
    }

    // 断开超时的客户端
    for (uint32_t clientId : timeoutClients) {
        logWebSocket("WARN", "客户端心跳超时，断开连接", clientId);
        disconnectClient(clientId);
    }

    // 向活跃客户端发送ping
    if (hasActiveClients()) {
        JsonDocument pingMessage;
        pingMessage["type"] = "ping";
        pingMessage["timestamp"] = getCurrentUnixMs();
        broadcastJson(pingMessage);

        if (DEBUG_LEVEL >= 4) {
            logWebSocket("DEBUG", "发送心跳ping");
        }
    }
}

void WebSocketManager::cleanupDisconnectedClients() {
    // 清理已断开连接的客户端记录
    auto it = connectedClients.begin();
    while (it != connectedClients.end()) {
        AsyncWebSocketClient* client = ws.client(it->first);
        if (!client || client->status() != WS_CONNECTED) {
            logWebSocket("DEBUG", "清理断开的客户端记录", it->first);
            it = connectedClients.erase(it);
        } else {
            ++it;
        }
    }
}

String WebSocketManager::createWebSocketMessage(const String& type, const JsonDocument& payload) {
    JsonDocument message;
    message["type"] = type;
    message["payload"] = payload;
    message["timestamp"] = getCurrentUnixMs();

    String result;
    serializeJson(message, result);
    return result;
}

IPAddress WebSocketManager::getClientIP(AsyncWebSocketClient *client) {
    return client->remoteIP();
}

void WebSocketManager::logWebSocket(const String& level, const String& message, uint32_t clientId) {
    String logMessage = "[WS] " + message;
    if (clientId > 0) {
        logMessage += " (客户端: " + String(clientId) + ")";
    }

    if (level == "ERROR") {
        Serial.println("❌ " + logMessage);
    } else if (level == "WARN") {
        Serial.println("⚠️ " + logMessage);
    } else if (level == "INFO") {
        Serial.println("ℹ️ " + logMessage);
    } else if (level == "DEBUG" && DEBUG_LEVEL >= 4) {
        Serial.println("🔍 " + logMessage);
    }
}

uint64_t WebSocketManager::getCurrentUnixMs() const {
    TimeManager* timeManager = TimeManager::getInstance();
    if (timeManager && timeManager->isTimeSynced()) {
        return timeManager->getUnixTimestampMs();
    }
    return millis();
}

// ==================== 新增事件方法 ====================

void WebSocketManager::sendLearningStartedEvent(uint32_t timeout) {
    JsonDocument payload;
    payload["timeout"] = timeout;
    payload["message"] = "信号学习已开始";
    payload["timestamp"] = getCurrentUnixMs();

    String message = createWebSocketMessage("learning_started", payload);
    broadcastMessage(message);

    logWebSocket("INFO", "发送学习开始事件，超时: " + String(timeout) + "ms");
}

void WebSocketManager::sendLearningTimeoutEvent() {
    JsonDocument payload;
    payload["message"] = "信号学习超时";
    payload["timestamp"] = getCurrentUnixMs();

    String message = createWebSocketMessage("learning_timeout", payload);
    broadcastMessage(message);

    logWebSocket("INFO", "发送学习超时事件");
}

void WebSocketManager::sendTimerExecutedEvent(const String& taskId, const String& taskName, bool success) {
    JsonDocument payload;
    payload["task_id"] = taskId;
    payload["task_name"] = taskName;
    payload["success"] = success;
    payload["message"] = success ? "定时任务执行成功" : "定时任务执行失败";
    payload["timestamp"] = getCurrentUnixMs();

    String message = createWebSocketMessage("timer_executed", payload);
    broadcastMessage(message);

    logWebSocket("INFO", "发送定时任务执行事件: " + taskName + " - " + (success ? "成功" : "失败"));
}

void WebSocketManager::sendTimerStatusEvent(bool enabled) {
    JsonDocument payload;
    payload["enabled"] = enabled;
    payload["message"] = enabled ? "定时器系统已启用" : "定时器系统已禁用";
    payload["timestamp"] = getCurrentUnixMs();

    String message = createWebSocketMessage("timer_status_changed", payload);
    broadcastMessage(message);

    logWebSocket("INFO", "发送定时器状态事件: " + String(enabled ? "启用" : "禁用"));
}
