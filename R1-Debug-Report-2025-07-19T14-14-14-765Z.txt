================================================================================
R1智能红外控制系统 - 调试报告
================================================================================
生成时间: 2025-07-19T14:14:14.760Z
会话时长: 58秒
日志总数: 874
用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
页面URL: http://***********:8000/

---------------------------------------- 系统状态 ----------------------------------------
{
  "currentModule": "signalManager",
  "isInitialized": true,
  "modules": [
    "signalManager",
    "controlModule",
    "timerSettings",
    "statusDisplay",
    "systemMonitor"
  ],
  "performance": {
    "startTime": 207.5,
    "initTime": 1934.7000000029802,
    "moduleLoadTime": 911,
    "memoryUsage": 0
  },
  "esp32Connected": true
}

---------------------------------------- 内存信息 ----------------------------------------
已用内存: 2MB
总内存: 3MB
内存限制: 4096MB

---------------------------------------- 错误统计 ----------------------------------------
LOG: 866条
ERROR: 6条
WARN: 2条

---------------------------------------- 详细日志 ----------------------------------------
[1] [+0ms] [LOG] 2025-07-19T14:13:17.185Z
✅ 错误收集器已启动，点击右下角按钮下载报告

[2] [+0ms] [LOG] 2025-07-19T14:13:17.185Z
✅ 错误收集器已启动，点击右下角按钮下载报告

[3] [+0ms] [LOG] 2025-07-19T14:13:17.185Z
🌙 深色模式已启用

[4] [+0ms] [LOG] 2025-07-19T14:13:17.185Z
🌙 深色模式已启用

[5] [+0ms] [LOG] 2025-07-19T14:13:17.185Z
✅ 核心组件初始化完成 (会话ID: session_34397185)

[6] [+0ms] [LOG] 2025-07-19T14:13:17.185Z
✅ 核心组件初始化完成 (会话ID: session_34397185)

[7] [+0ms] [LOG] 2025-07-19T14:13:17.185Z
➕ UnifiedTimerManager: 添加定时器 [system_time_update], 间隔: 1000ms, 重复: true, 下次执行: 2025/7/19 22:13:18

[8] [+0ms] [LOG] 2025-07-19T14:13:17.185Z
➕ UnifiedTimerManager: 添加定时器 [system_time_update], 间隔: 1000ms, 重复: true, 下次执行: 2025/7/19 22:13:18

[9] [+0ms] [LOG] 2025-07-19T14:13:17.185Z
🚀 UnifiedTimerManager: 主定时器已启动，间隔: 100ms, 定时器数量: 1

[10] [+0ms] [LOG] 2025-07-19T14:13:17.185Z
🚀 UnifiedTimerManager: 主定时器已启动，间隔: 100ms, 定时器数量: 1

[11] [+1ms] [LOG] 2025-07-19T14:13:17.186Z
⏰ 系统时间显示已启动

[12] [+1ms] [LOG] 2025-07-19T14:13:17.186Z
⏰ 系统时间显示已启动

[13] [+1ms] [LOG] 2025-07-19T14:13:17.186Z
📡 初始化ESP32管理器...

[14] [+1ms] [LOG] 2025-07-19T14:13:17.186Z
📡 初始化ESP32管理器...

[15] [+1ms] [LOG] 2025-07-19T14:13:17.186Z
🚀 初始化ESP32管理器...

[16] [+1ms] [LOG] 2025-07-19T14:13:17.186Z
🚀 初始化ESP32管理器...

[17] [+1ms] [LOG] 2025-07-19T14:13:17.186Z
📡 HTTP API: http://***********:8000

[18] [+1ms] [LOG] 2025-07-19T14:13:17.186Z
📡 HTTP API: http://***********:8000

[19] [+1ms] [LOG] 2025-07-19T14:13:17.186Z
🔌 WebSocket: ws://***********:8001/ws

[20] [+1ms] [LOG] 2025-07-19T14:13:17.186Z
🔌 WebSocket: ws://***********:8001/ws

[21] [+1ms] [LOG] 2025-07-19T14:13:17.186Z
🔍 测试后端连接...

[22] [+1ms] [LOG] 2025-07-19T14:13:17.186Z
🔍 测试后端连接...

[23] [+1ms] [LOG] 2025-07-19T14:13:17.186Z
📡 请求URL: http://***********:8000/api/status

[24] [+1ms] [LOG] 2025-07-19T14:13:17.186Z
📡 请求URL: http://***********:8000/api/status

[25] [+444ms] [LOG] 2025-07-19T14:13:17.629Z
📊 HTTP状态: 200 OK

[26] [+444ms] [LOG] 2025-07-19T14:13:17.629Z
📊 HTTP状态: 200 OK

[27] [+444ms] [LOG] 2025-07-19T14:13:17.629Z
📋 响应头: {
  "content-type": "application/json",
  "content-length": "237"
}
  参数2: {
  "content-type": "application/json",
  "content-length": "237"
}

[28] [+444ms] [LOG] 2025-07-19T14:13:17.629Z
📋 响应头: {
  "content-type": "application/json",
  "content-length": "237"
}
  参数2: {
  "content-type": "application/json",
  "content-length": "237"
}

[29] [+445ms] [LOG] 2025-07-19T14:13:17.630Z
📦 响应数据: {
  "success": true,
  "data": {
    "uptime": 99,
    "memory_usage": 36.2313,
    "signal_count": 1,
    "wifi_strength": 0,
    "free_heap": 220780,
    "chip_temperature": 57.1,
    "timestamp": 100448
  },
  "message": "系统状态获取成功",
  "timestamp": 1735689700449,
  "responseTime": 0
}
  参数2: {
  "success": true,
  "data": {
    "uptime": 99,
    "memory_usage": 36.2313,
    "signal_count": 1,
    "wifi_strength": 0,
    "free_heap": 220780,
    "chip_temperature": 57.1,
    "timestamp": 100448
  },
  "message": "系统状态获取成功",
  "timestamp": 1735689700449,
  "responseTime": 0
}

[30] [+445ms] [LOG] 2025-07-19T14:13:17.630Z
📦 响应数据: {
  "success": true,
  "data": {
    "uptime": 99,
    "memory_usage": 36.2313,
    "signal_count": 1,
    "wifi_strength": 0,
    "free_heap": 220780,
    "chip_temperature": 57.1,
    "timestamp": 100448
  },
  "message": "系统状态获取成功",
  "timestamp": 1735689700449,
  "responseTime": 0
}
  参数2: {
  "success": true,
  "data": {
    "uptime": 99,
    "memory_usage": 36.2313,
    "signal_count": 1,
    "wifi_strength": 0,
    "free_heap": 220780,
    "chip_temperature": 57.1,
    "timestamp": 100448
  },
  "message": "系统状态获取成功",
  "timestamp": 1735689700449,
  "responseTime": 0
}

[31] [+445ms] [LOG] 2025-07-19T14:13:17.630Z
✅ 后端连接正常

[32] [+445ms] [LOG] 2025-07-19T14:13:17.630Z
✅ 后端连接正常

[33] [+612ms] [LOG] 2025-07-19T14:13:17.797Z
✅ WebSocket连接成功

[34] [+612ms] [LOG] 2025-07-19T14:13:17.797Z
✅ WebSocket连接成功

[35] [+612ms] [LOG] 2025-07-19T14:13:17.797Z
✅ ESP32管理器初始化完成 - 在线模式

[36] [+613ms] [LOG] 2025-07-19T14:13:17.798Z
✅ ESP32管理器初始化完成 - 在线模式

[37] [+613ms] [LOG] 2025-07-19T14:13:17.798Z
✅ ESP32连接成功

[38] [+613ms] [LOG] 2025-07-19T14:13:17.798Z
✅ ESP32连接成功

[39] [+613ms] [LOG] 2025-07-19T14:13:17.798Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/config/user",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/config/user",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[40] [+613ms] [LOG] 2025-07-19T14:13:17.798Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/config/user",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/config/user",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[41] [+849ms] [LOG] 2025-07-19T14:13:18.034Z
📨 WebSocket消息: {
  "type": "connected",
  "payload": {
    "message": "WebSocket连接成功",
    "clientId": "client_2",
    "serverTime": 100872,
    "serverVersion": "1.0.0"
  },
  "timestamp": 100872
}
  参数2: {
  "type": "connected",
  "payload": {
    "message": "WebSocket连接成功",
    "clientId": "client_2",
    "serverTime": 100872,
    "serverVersion": "1.0.0"
  },
  "timestamp": 100872
}

[42] [+849ms] [LOG] 2025-07-19T14:13:18.034Z
📨 WebSocket消息: {
  "type": "connected",
  "payload": {
    "message": "WebSocket连接成功",
    "clientId": "client_2",
    "serverTime": 100872,
    "serverVersion": "1.0.0"
  },
  "timestamp": 100872
}
  参数2: {
  "type": "connected",
  "payload": {
    "message": "WebSocket连接成功",
    "clientId": "client_2",
    "serverTime": 100872,
    "serverVersion": "1.0.0"
  },
  "timestamp": 100872
}

[43] [+1001ms] [LOG] 2025-07-19T14:13:18.186Z
⏰ UnifiedTimerManager: 1 个定时器到期

[44] [+1001ms] [LOG] 2025-07-19T14:13:18.186Z
⏰ UnifiedTimerManager: 1 个定时器到期

[45] [+1001ms] [LOG] 2025-07-19T14:13:18.186Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[46] [+1001ms] [LOG] 2025-07-19T14:13:18.186Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[47] [+1001ms] [LOG] 2025-07-19T14:13:18.186Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[48] [+1001ms] [LOG] 2025-07-19T14:13:18.186Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[49] [+1001ms] [LOG] 2025-07-19T14:13:18.186Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:19

[50] [+1001ms] [LOG] 2025-07-19T14:13:18.186Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:19

[51] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
✅ 已加载当前硬件配置: {
  "ir_recv_pin": 14,
  "ir_send_pin": 21,
  "ir_frequency": 38000,
  "ir_duty_cycle": 33,
  "status_led_pin": 2,
  "status_led_enabled": true
}
  参数2: {
  "ir_recv_pin": 14,
  "ir_send_pin": 21,
  "ir_frequency": 38000,
  "ir_duty_cycle": 33,
  "status_led_pin": 2,
  "status_led_enabled": true
}

[52] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
✅ 已加载当前硬件配置: {
  "ir_recv_pin": 14,
  "ir_send_pin": 21,
  "ir_frequency": 38000,
  "ir_duty_cycle": 33,
  "status_led_pin": 2,
  "status_led_enabled": true
}
  参数2: {
  "ir_recv_pin": 14,
  "ir_send_pin": 21,
  "ir_frequency": 38000,
  "ir_duty_cycle": 33,
  "status_led_pin": 2,
  "status_led_enabled": true
}

[53] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
📋 当前硬件配置:

[54] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
📋 当前硬件配置:

[55] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
  红外接收引脚: GPIO14

[56] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
  红外接收引脚: GPIO14

[57] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
  红外发射引脚: GPIO21

[58] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
  红外发射引脚: GPIO21

[59] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
  红外频率: 38000 Hz

[60] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
  红外频率: 38000 Hz

[61] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
  占空比: 33%

[62] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
  占空比: 33%

[63] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
  状态LED引脚: GPIO2

[64] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
  状态LED引脚: GPIO2

[65] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
  状态LED启用: 是

[66] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
  状态LED启用: 是

[67] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
📦 开始初始化所有模块...

[68] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
📦 开始初始化所有模块...

[69] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔧 初始化 signalManager 模块...

[70] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔧 初始化 signalManager 模块...

[71] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔍 signalManager 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[72] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔍 signalManager 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[73] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔧 开始初始化 SignalManager 模块...

[74] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔧 开始初始化 SignalManager 模块...

[75] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔧 初始化 controlModule 模块...

[76] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔧 初始化 controlModule 模块...

[77] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔍 controlModule 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[78] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔍 controlModule 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[79] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔧 开始初始化 ControlModule 模块...

[80] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔧 开始初始化 ControlModule 模块...

[81] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔧 初始化 timerSettings 模块...

[82] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔧 初始化 timerSettings 模块...

[83] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔍 timerSettings 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[84] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔍 timerSettings 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[85] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔧 开始初始化 TimerSettings 模块...

[86] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔧 开始初始化 TimerSettings 模块...

[87] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
TimerSettings: 事件监听器设置完成

[88] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
TimerSettings: 事件监听器设置完成

[89] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔧 初始化 statusDisplay 模块...

[90] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔧 初始化 statusDisplay 模块...

[91] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔍 statusDisplay 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[92] [+1022ms] [LOG] 2025-07-19T14:13:18.207Z
🔍 statusDisplay 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[93] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
🔧 开始初始化 StatusDisplay 模块...

[94] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
🔧 开始初始化 StatusDisplay 模块...

[95] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
🔧 初始化 systemMonitor 模块...

[96] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
🔧 初始化 systemMonitor 模块...

[97] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
🔍 systemMonitor 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[98] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
🔍 systemMonitor 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[99] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
🔧 开始初始化 SystemMonitor 模块...

[100] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
🔧 开始初始化 SystemMonitor 模块...

[101] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
📡 SignalManager 事件监听器初始化完成

[102] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
📡 SignalManager 事件监听器初始化完成

[103] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
🔍 渲染信号 - 总数: 0, 过滤后: 0

[104] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
🔍 渲染信号 - 总数: 0, 过滤后: 0

[105] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[106] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[107] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
📡 ControlModule 事件监听器初始化完成

[108] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
📡 ControlModule 事件监听器初始化完成

[109] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
ControlModule: 事件绑定完成

[110] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
ControlModule: 事件绑定完成

[111] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
📡 TimerSettings 事件监听器初始化完成

[112] [+1023ms] [LOG] 2025-07-19T14:13:18.208Z
📡 TimerSettings 事件监听器初始化完成

[113] [+1024ms] [LOG] 2025-07-19T14:13:18.209Z
TimerSettings: 默认时间值设置完成（9-18点）

[114] [+1024ms] [LOG] 2025-07-19T14:13:18.209Z
TimerSettings: 默认时间值设置完成（9-18点）

[115] [+1024ms] [LOG] 2025-07-19T14:13:18.209Z
TimerSettings: 时间选择器事件绑定完成

[116] [+1024ms] [LOG] 2025-07-19T14:13:18.209Z
TimerSettings: 时间选择器事件绑定完成

[117] [+1024ms] [LOG] 2025-07-19T14:13:18.209Z
TimerSettings: 主开关状态: false

[118] [+1024ms] [LOG] 2025-07-19T14:13:18.209Z
TimerSettings: 主开关状态: false

[119] [+1024ms] [LOG] 2025-07-19T14:13:18.209Z
TimerSettings: 间隔设置状态已更新 {
  "hasMultipleTasks": false,
  "intervalDisabled": false,
  "reason": "单任务模式允许间隔"
}
  参数2: {
  "hasMultipleTasks": false,
  "intervalDisabled": false,
  "reason": "单任务模式允许间隔"
}

[120] [+1024ms] [LOG] 2025-07-19T14:13:18.209Z
TimerSettings: 间隔设置状态已更新 {
  "hasMultipleTasks": false,
  "intervalDisabled": false,
  "reason": "单任务模式允许间隔"
}
  参数2: {
  "hasMultipleTasks": false,
  "intervalDisabled": false,
  "reason": "单任务模式允许间隔"
}

[121] [+1024ms] [LOG] 2025-07-19T14:13:18.209Z
TimerSettings: 事件绑定完成

[122] [+1024ms] [LOG] 2025-07-19T14:13:18.209Z
TimerSettings: 事件绑定完成

[123] [+1024ms] [LOG] 2025-07-19T14:13:18.209Z
📡 StatusDisplay 事件监听器初始化完成

[124] [+1024ms] [LOG] 2025-07-19T14:13:18.209Z
📡 StatusDisplay 事件监听器初始化完成

[125] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
📡 SystemMonitor 事件监听器初始化完成

[126] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
📡 SystemMonitor 事件监听器初始化完成

[127] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🔍 SystemMonitor: 开始设置UI...

[128] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🔍 SystemMonitor: 开始设置UI...

[129] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🔍 SystemMonitor: 绑定过滤器事件...

[130] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🔍 SystemMonitor: 绑定过滤器事件...

[131] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🔍 SystemMonitor: 查找systemMonitorArea结果: {}
  参数2: {}

[132] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🔍 SystemMonitor: 查找systemMonitorArea结果: {}
  参数2: {}

[133] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🎨 SignalManager UI初始化完成

[134] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🎨 SignalManager UI初始化完成

[135] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
📥 加载 0 个标准格式信号

[136] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
📥 加载 0 个标准格式信号

[137] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
✅ 从本地存储加载了 0 个信号

[138] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
✅ 从本地存储加载了 0 个信号

[139] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
📡 从ESP32加载信号列表...

[140] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
📡 从ESP32加载信号列表...

[141] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[142] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[143] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🎨 ControlModule UI初始化完成

[144] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🎨 ControlModule UI初始化完成

[145] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🎨 TimerSettings UI初始化完成

[146] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🎨 TimerSettings UI初始化完成

[147] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
TimerSettings: 数据加载待重新实现

[148] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
TimerSettings: 数据加载待重新实现

[149] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🎨 StatusDisplay UI初始化完成

[150] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🎨 StatusDisplay UI初始化完成

[151] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
StatusDisplay 数据加载完成，等待其他模块初始化

[152] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
StatusDisplay 数据加载完成，等待其他模块初始化

[153] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🎨 SystemMonitor UI初始化完成

[154] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🎨 SystemMonitor UI初始化完成

[155] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[156] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[157] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[158] [+1025ms] [LOG] 2025-07-19T14:13:18.210Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[159] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[160] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[161] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[162] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[163] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
📊 ControlModule 数据加载完成

[164] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
📊 ControlModule 数据加载完成

[165] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
✅ ControlModule 模块初始化完成，耗时: 3.80ms

[166] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
✅ ControlModule 模块初始化完成，耗时: 3.80ms

[167] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
📊 TimerSettings 数据加载完成

[168] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
📊 TimerSettings 数据加载完成

[169] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
✅ TimerSettings 模块初始化完成，耗时: 3.80ms

[170] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
✅ TimerSettings 模块初始化完成，耗时: 3.80ms

[171] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
📊 StatusDisplay 数据加载完成

[172] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
📊 StatusDisplay 数据加载完成

[173] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
✅ StatusDisplay 模块初始化完成，耗时: 3.70ms

[174] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
✅ StatusDisplay 模块初始化完成，耗时: 3.70ms

[175] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
📊 SystemMonitor 数据加载完成

[176] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
📊 SystemMonitor 数据加载完成

[177] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
✅ SystemMonitor 模块初始化完成，耗时: 3.70ms

[178] [+1026ms] [LOG] 2025-07-19T14:13:18.211Z
✅ SystemMonitor 模块初始化完成，耗时: 3.70ms

[179] [+1041ms] [LOG] 2025-07-19T14:13:18.226Z
✅ 模块就绪: ControlModule

[180] [+1041ms] [LOG] 2025-07-19T14:13:18.226Z
✅ 模块就绪: ControlModule

[181] [+1041ms] [LOG] 2025-07-19T14:13:18.226Z
✅ 模块就绪: TimerSettings

[182] [+1041ms] [LOG] 2025-07-19T14:13:18.226Z
✅ 模块就绪: TimerSettings

[183] [+1041ms] [LOG] 2025-07-19T14:13:18.226Z
✅ 模块就绪: StatusDisplay

[184] [+1041ms] [LOG] 2025-07-19T14:13:18.226Z
✅ 模块就绪: StatusDisplay

[185] [+1041ms] [LOG] 2025-07-19T14:13:18.226Z
✅ 模块就绪: SystemMonitor

[186] [+1041ms] [LOG] 2025-07-19T14:13:18.226Z
✅ 模块就绪: SystemMonitor

[187] [+1080ms] [LOG] 2025-07-19T14:13:18.265Z
📨 WebSocket消息: {
  "type": "websocket_client_connected",
  "payload": {
    "client_id": 2,
    "client_ip": "***********",
    "total_clients": 1
  },
  "timestamp": 1735689700874
}
  参数2: {
  "type": "websocket_client_connected",
  "payload": {
    "client_id": 2,
    "client_ip": "***********",
    "total_clients": 1
  },
  "timestamp": 1735689700874
}

[188] [+1080ms] [LOG] 2025-07-19T14:13:18.265Z
📨 WebSocket消息: {
  "type": "websocket_client_connected",
  "payload": {
    "client_id": 2,
    "client_ip": "***********",
    "total_clients": 1
  },
  "timestamp": 1735689700874
}
  参数2: {
  "type": "websocket_client_connected",
  "payload": {
    "client_id": 2,
    "client_ip": "***********",
    "total_clients": 1
  },
  "timestamp": 1735689700874
}

[189] [+1931ms] [LOG] 2025-07-19T14:13:19.116Z
💾 保存 1 个标准格式信号

[190] [+1931ms] [LOG] 2025-07-19T14:13:19.116Z
💾 保存 1 个标准格式信号

[191] [+1931ms] [LOG] 2025-07-19T14:13:19.116Z
💾 保存前信号总数: 1

[192] [+1931ms] [LOG] 2025-07-19T14:13:19.116Z
💾 保存前信号总数: 1

[193] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
✅ 信号 "新学习信号" 将被保存

[194] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
✅ 信号 "新学习信号" 将被保存

[195] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
💾 准备保存 1 个有效信号

[196] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
💾 准备保存 1 个有效信号

[197] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
💾 内存中保留 1 个信号

[198] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
💾 内存中保留 1 个信号

[199] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
✅ 标准格式信号数据保存完成 - 保存了 1 个信号

[200] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
✅ 标准格式信号数据保存完成 - 保存了 1 个信号

[201] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
🔍 渲染信号 - 总数: 1, 过滤后: 1

[202] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
🔍 渲染信号 - 总数: 1, 过滤后: 1

[203] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[204] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[205] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
🔍 开始渲染 1 个信号到网格

[206] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
🔍 开始渲染 1 个信号到网格

[207] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
✅ 信号 1: 新学习信号 HTML生成成功

[208] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
✅ 信号 1: 新学习信号 HTML生成成功

[209] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
📊 HTML生成完成，共 1 个信号，DOM子元素: 1

[210] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
📊 HTML生成完成，共 1 个信号，DOM子元素: 1

[211] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
✅ 网格渲染完成，容器子元素数量: 1

[212] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
✅ 网格渲染完成，容器子元素数量: 1

[213] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
✅ 从ESP32加载了 1 个信号

[214] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
✅ 从ESP32加载了 1 个信号

[215] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
🔍 渲染信号 - 总数: 1, 过滤后: 1

[216] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
🔍 渲染信号 - 总数: 1, 过滤后: 1

[217] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[218] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[219] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
🔍 使用缓存，跳过渲染

[220] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
🔍 使用缓存，跳过渲染

[221] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
📊 SignalManager 数据加载完成

[222] [+1932ms] [LOG] 2025-07-19T14:13:19.117Z
📊 SignalManager 数据加载完成

[223] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
✅ SignalManager 模块初始化完成，耗时: 910.40ms

[224] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
✅ SignalManager 模块初始化完成，耗时: 910.40ms

[225] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
✅ signalManager 模块初始化成功

[226] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
✅ signalManager 模块初始化成功

[227] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
✅ controlModule 模块初始化成功

[228] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
✅ controlModule 模块初始化成功

[229] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
✅ timerSettings 模块初始化成功

[230] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
✅ timerSettings 模块初始化成功

[231] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
✅ statusDisplay 模块初始化成功

[232] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
✅ statusDisplay 模块初始化成功

[233] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
✅ systemMonitor 模块初始化成功

[234] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
✅ systemMonitor 模块初始化成功

[235] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
📦 模块初始化完成: 成功 5 个，失败 0 个，耗时: 911.00ms

[236] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
📦 模块初始化完成: 成功 5 个，失败 0 个，耗时: 911.00ms

[237] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
✅ R1系统启动完成，耗时: 1934.70ms

[238] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
✅ R1系统启动完成，耗时: 1934.70ms

[239] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
➕ UnifiedTimerManager: 添加定时器 [notification_notification_34399118], 间隔: 5000ms, 重复: false, 下次执行: 2025/7/19 22:13:24

[240] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
➕ UnifiedTimerManager: 添加定时器 [notification_notification_34399118], 间隔: 5000ms, 重复: false, 下次执行: 2025/7/19 22:13:24

[241] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
⚠️ UnifiedTimerManager: 主定时器已在运行

[242] [+1933ms] [LOG] 2025-07-19T14:13:19.118Z
⚠️ UnifiedTimerManager: 主定时器已在运行

[243] [+1951ms] [LOG] 2025-07-19T14:13:19.136Z
✅ 模块就绪: SignalManager

[244] [+1951ms] [LOG] 2025-07-19T14:13:19.136Z
✅ 模块就绪: SignalManager

[245] [+1951ms] [LOG] 2025-07-19T14:13:19.136Z
🎉 所有模块初始化完成: 成功 5 个

[246] [+1951ms] [LOG] 2025-07-19T14:13:19.136Z
🎉 所有模块初始化完成: 成功 5 个

[247] [+1951ms] [LOG] 2025-07-19T14:13:19.136Z
StatusDisplay: 收到系统模块初始化完成事件，开始自动更新

[248] [+1951ms] [LOG] 2025-07-19T14:13:19.136Z
StatusDisplay: 收到系统模块初始化完成事件，开始自动更新

[249] [+1951ms] [LOG] 2025-07-19T14:13:19.136Z
StatusDisplay: updateCurrentTaskDisplay 被调用，现在主要通过事件驱动更新

[250] [+1951ms] [LOG] 2025-07-19T14:13:19.136Z
StatusDisplay: updateCurrentTaskDisplay 被调用，现在主要通过事件驱动更新

[251] [+2001ms] [LOG] 2025-07-19T14:13:19.186Z
⏰ UnifiedTimerManager: 1 个定时器到期

[252] [+2001ms] [LOG] 2025-07-19T14:13:19.186Z
⏰ UnifiedTimerManager: 1 个定时器到期

[253] [+2001ms] [LOG] 2025-07-19T14:13:19.186Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[254] [+2001ms] [LOG] 2025-07-19T14:13:19.186Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[255] [+2001ms] [LOG] 2025-07-19T14:13:19.186Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[256] [+2001ms] [LOG] 2025-07-19T14:13:19.186Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[257] [+2001ms] [LOG] 2025-07-19T14:13:19.186Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:20

[258] [+2001ms] [LOG] 2025-07-19T14:13:19.186Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:20

[259] [+3009ms] [LOG] 2025-07-19T14:13:20.194Z
⏰ UnifiedTimerManager: 1 个定时器到期

[260] [+3009ms] [LOG] 2025-07-19T14:13:20.194Z
⏰ UnifiedTimerManager: 1 个定时器到期

[261] [+3009ms] [LOG] 2025-07-19T14:13:20.194Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 7ms

[262] [+3009ms] [LOG] 2025-07-19T14:13:20.194Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 7ms

[263] [+3009ms] [LOG] 2025-07-19T14:13:20.194Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[264] [+3009ms] [LOG] 2025-07-19T14:13:20.194Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[265] [+3009ms] [LOG] 2025-07-19T14:13:20.194Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:21

[266] [+3009ms] [LOG] 2025-07-19T14:13:20.194Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:21

[267] [+4114ms] [LOG] 2025-07-19T14:13:21.299Z
⏰ UnifiedTimerManager: 1 个定时器到期

[268] [+4114ms] [LOG] 2025-07-19T14:13:21.299Z
⏰ UnifiedTimerManager: 1 个定时器到期

[269] [+4114ms] [LOG] 2025-07-19T14:13:21.299Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 106ms

[270] [+4114ms] [LOG] 2025-07-19T14:13:21.299Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 106ms

[271] [+4114ms] [LOG] 2025-07-19T14:13:21.299Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[272] [+4114ms] [LOG] 2025-07-19T14:13:21.299Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[273] [+4114ms] [LOG] 2025-07-19T14:13:21.299Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:22

[274] [+4114ms] [LOG] 2025-07-19T14:13:21.299Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:22

[275] [+5203ms] [LOG] 2025-07-19T14:13:22.388Z
⏰ UnifiedTimerManager: 1 个定时器到期

[276] [+5203ms] [LOG] 2025-07-19T14:13:22.388Z
⏰ UnifiedTimerManager: 1 个定时器到期

[277] [+5203ms] [LOG] 2025-07-19T14:13:22.388Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 89ms

[278] [+5204ms] [LOG] 2025-07-19T14:13:22.389Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 89ms

[279] [+5204ms] [LOG] 2025-07-19T14:13:22.389Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[280] [+5204ms] [LOG] 2025-07-19T14:13:22.389Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[281] [+5204ms] [LOG] 2025-07-19T14:13:22.389Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:23

[282] [+5204ms] [LOG] 2025-07-19T14:13:22.389Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:23

[283] [+6204ms] [LOG] 2025-07-19T14:13:23.389Z
⏰ UnifiedTimerManager: 1 个定时器到期

[284] [+6204ms] [LOG] 2025-07-19T14:13:23.389Z
⏰ UnifiedTimerManager: 1 个定时器到期

[285] [+6204ms] [LOG] 2025-07-19T14:13:23.389Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[286] [+6204ms] [LOG] 2025-07-19T14:13:23.389Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[287] [+6204ms] [LOG] 2025-07-19T14:13:23.389Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[288] [+6204ms] [LOG] 2025-07-19T14:13:23.389Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[289] [+6204ms] [LOG] 2025-07-19T14:13:23.389Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:24

[290] [+6204ms] [LOG] 2025-07-19T14:13:23.389Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:24

[291] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🔄 [direct] switchModule开始: signal-manager -> controlModule (7177.200ms)

[292] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🔄 [direct] switchModule开始: signal-manager -> controlModule (7177.200ms)

[293] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🔄 [direct] 步骤1-状态更新: 0.000ms

[294] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🔄 [direct] 步骤1-状态更新: 0.000ms

[295] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🔧 [direct] 模块ID映射: signal-manager -> signal-manager, controlModule -> control-module

[296] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🔧 [direct] 模块ID映射: signal-manager -> signal-manager, controlModule -> control-module

[297] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🔄 [direct] 步骤2-DOM查询: 0.100ms {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}
  参数2: {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}

[298] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🔄 [direct] 步骤2-DOM查询: 0.100ms {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}
  参数2: {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}

[299] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🔄 [direct] 隐藏面板: signal-manager (ID: signal-manager)

[300] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🔄 [direct] 隐藏面板: signal-manager (ID: signal-manager)

[301] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🎯 [direct] 隐藏前CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[302] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🎯 [direct] 隐藏前CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[303] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🎯 [direct] 隐藏后CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[304] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🎯 [direct] 隐藏后CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[305] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🔄 [direct] 步骤3-隐藏面板: 0.400ms

[306] [+6968ms] [LOG] 2025-07-19T14:13:24.153Z
🔄 [direct] 步骤3-隐藏面板: 0.400ms

[307] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🔄 [direct] 显示面板: controlModule (ID: control-module)

[308] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🔄 [direct] 显示面板: controlModule (ID: control-module)

[309] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🎯 [direct] 显示前CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[310] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🎯 [direct] 显示前CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[311] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🎯 [direct] 显示后CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[312] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🎯 [direct] 显示后CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[313] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🔄 [direct] 步骤4-显示面板: 0.200ms

[314] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🔄 [direct] 步骤4-显示面板: 0.200ms

[315] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🔄 [direct] 更新3个标签状态

[316] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🔄 [direct] 更新3个标签状态

[317] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🔄 [direct] 步骤5-清除标签: 0.100ms

[318] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🔄 [direct] 步骤5-清除标签: 0.100ms

[319] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🔄 [direct] 激活标签: controlModule (data-module: control-module)

[320] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🔄 [direct] 激活标签: controlModule (data-module: control-module)

[321] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🔄 [direct] 步骤6-激活标签: 0.100ms

[322] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🔄 [direct] 步骤6-激活标签: 0.100ms

[323] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🏁 [direct] switchModule完成: 1.200ms

[324] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
🏁 [direct] switchModule完成: 1.200ms

[325] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
📊 [direct] 性能分解: {
  "状态更新": "0.000ms",
  "DOM查询": "0.100ms",
  "隐藏面板": "0.400ms",
  "显示面板": "0.200ms",
  "清除标签": "0.100ms",
  "激活标签": "0.100ms",
  "总计": "1.200ms"
}
  参数2: {
  "状态更新": "0.000ms",
  "DOM查询": "0.100ms",
  "隐藏面板": "0.400ms",
  "显示面板": "0.200ms",
  "清除标签": "0.100ms",
  "激活标签": "0.100ms",
  "总计": "1.200ms"
}

[326] [+6969ms] [LOG] 2025-07-19T14:13:24.154Z
📊 [direct] 性能分解: {
  "状态更新": "0.000ms",
  "DOM查询": "0.100ms",
  "隐藏面板": "0.400ms",
  "显示面板": "0.200ms",
  "清除标签": "0.100ms",
  "激活标签": "0.100ms",
  "总计": "1.200ms"
}
  参数2: {
  "状态更新": "0.000ms",
  "DOM查询": "0.100ms",
  "隐藏面板": "0.400ms",
  "显示面板": "0.200ms",
  "清除标签": "0.100ms",
  "激活标签": "0.100ms",
  "总计": "1.200ms"
}

[327] [+7001ms] [LOG] 2025-07-19T14:13:24.186Z
⏰ UnifiedTimerManager: 1 个定时器到期

[328] [+7001ms] [LOG] 2025-07-19T14:13:24.186Z
⏰ UnifiedTimerManager: 1 个定时器到期

[329] [+7001ms] [LOG] 2025-07-19T14:13:24.186Z
🔥 UnifiedTimerManager: 执行定时器 [notification_notification_34399118], 延迟: 68ms

[330] [+7001ms] [LOG] 2025-07-19T14:13:24.186Z
🔥 UnifiedTimerManager: 执行定时器 [notification_notification_34399118], 延迟: 68ms

[331] [+7001ms] [LOG] 2025-07-19T14:13:24.186Z
➕ UnifiedTimerManager: 添加定时器 [notification_remove_notification_34399118], 间隔: 300ms, 重复: false, 下次执行: 2025/7/19 22:13:24

[332] [+7001ms] [LOG] 2025-07-19T14:13:24.186Z
➕ UnifiedTimerManager: 添加定时器 [notification_remove_notification_34399118], 间隔: 300ms, 重复: false, 下次执行: 2025/7/19 22:13:24

[333] [+7001ms] [LOG] 2025-07-19T14:13:24.186Z
⚠️ UnifiedTimerManager: 主定时器已在运行

[334] [+7001ms] [LOG] 2025-07-19T14:13:24.186Z
⚠️ UnifiedTimerManager: 主定时器已在运行

[335] [+7001ms] [LOG] 2025-07-19T14:13:24.186Z
✅ UnifiedTimerManager: 定时器 [notification_notification_34399118] 执行成功

[336] [+7001ms] [LOG] 2025-07-19T14:13:24.186Z
✅ UnifiedTimerManager: 定时器 [notification_notification_34399118] 执行成功

[337] [+7001ms] [LOG] 2025-07-19T14:13:24.186Z
🗑️ UnifiedTimerManager: 一次性定时器 [notification_notification_34399118] 执行完成，已删除

[338] [+7001ms] [LOG] 2025-07-19T14:13:24.186Z
🗑️ UnifiedTimerManager: 一次性定时器 [notification_notification_34399118] 执行完成，已删除

[339] [+7205ms] [LOG] 2025-07-19T14:13:24.390Z
⏰ UnifiedTimerManager: 1 个定时器到期

[340] [+7205ms] [LOG] 2025-07-19T14:13:24.390Z
⏰ UnifiedTimerManager: 1 个定时器到期

[341] [+7205ms] [LOG] 2025-07-19T14:13:24.390Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[342] [+7205ms] [LOG] 2025-07-19T14:13:24.390Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[343] [+7205ms] [LOG] 2025-07-19T14:13:24.390Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[344] [+7205ms] [LOG] 2025-07-19T14:13:24.390Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[345] [+7206ms] [LOG] 2025-07-19T14:13:24.391Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:25

[346] [+7206ms] [LOG] 2025-07-19T14:13:24.391Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:25

[347] [+7313ms] [LOG] 2025-07-19T14:13:24.498Z
⏰ UnifiedTimerManager: 1 个定时器到期

[348] [+7313ms] [LOG] 2025-07-19T14:13:24.498Z
⏰ UnifiedTimerManager: 1 个定时器到期

[349] [+7313ms] [LOG] 2025-07-19T14:13:24.498Z
🔥 UnifiedTimerManager: 执行定时器 [notification_remove_notification_34399118], 延迟: 12ms

[350] [+7313ms] [LOG] 2025-07-19T14:13:24.498Z
🔥 UnifiedTimerManager: 执行定时器 [notification_remove_notification_34399118], 延迟: 12ms

[351] [+7313ms] [LOG] 2025-07-19T14:13:24.498Z
✅ UnifiedTimerManager: 定时器 [notification_remove_notification_34399118] 执行成功

[352] [+7313ms] [LOG] 2025-07-19T14:13:24.498Z
✅ UnifiedTimerManager: 定时器 [notification_remove_notification_34399118] 执行成功

[353] [+7313ms] [LOG] 2025-07-19T14:13:24.498Z
🗑️ UnifiedTimerManager: 一次性定时器 [notification_remove_notification_34399118] 执行完成，已删除

[354] [+7313ms] [LOG] 2025-07-19T14:13:24.498Z
🗑️ UnifiedTimerManager: 一次性定时器 [notification_remove_notification_34399118] 执行完成，已删除

[355] [+7970ms] [LOG] 2025-07-19T14:13:25.155Z
ControlModule: 发射模式设置为: single

[356] [+7970ms] [LOG] 2025-07-19T14:13:25.155Z
ControlModule: 发射模式设置为: single

[357] [+8214ms] [LOG] 2025-07-19T14:13:25.399Z
⏰ UnifiedTimerManager: 1 个定时器到期

[358] [+8214ms] [LOG] 2025-07-19T14:13:25.399Z
⏰ UnifiedTimerManager: 1 个定时器到期

[359] [+8214ms] [LOG] 2025-07-19T14:13:25.399Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 9ms

[360] [+8214ms] [LOG] 2025-07-19T14:13:25.399Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 9ms

[361] [+8214ms] [LOG] 2025-07-19T14:13:25.399Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[362] [+8214ms] [LOG] 2025-07-19T14:13:25.399Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[363] [+8214ms] [LOG] 2025-07-19T14:13:25.399Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:26

[364] [+8214ms] [LOG] 2025-07-19T14:13:25.399Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:26

[365] [+8811ms] [LOG] 2025-07-19T14:13:25.996Z
🔄 [direct] switchModule开始: controlModule -> signalManager (9020.500ms)

[366] [+8811ms] [LOG] 2025-07-19T14:13:25.996Z
🔄 [direct] switchModule开始: controlModule -> signalManager (9020.500ms)

[367] [+8811ms] [LOG] 2025-07-19T14:13:25.996Z
🔄 [direct] 步骤1-状态更新: 0.000ms

[368] [+8811ms] [LOG] 2025-07-19T14:13:25.996Z
🔄 [direct] 步骤1-状态更新: 0.000ms

[369] [+8811ms] [LOG] 2025-07-19T14:13:25.996Z
🔧 [direct] 模块ID映射: controlModule -> control-module, signalManager -> signal-manager

[370] [+8811ms] [LOG] 2025-07-19T14:13:25.996Z
🔧 [direct] 模块ID映射: controlModule -> control-module, signalManager -> signal-manager

[371] [+8811ms] [LOG] 2025-07-19T14:13:25.996Z
🔄 [direct] 步骤2-DOM查询: 0.100ms {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}
  参数2: {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}

[372] [+8811ms] [LOG] 2025-07-19T14:13:25.996Z
🔄 [direct] 步骤2-DOM查询: 0.100ms {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}
  参数2: {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}

[373] [+8811ms] [LOG] 2025-07-19T14:13:25.996Z
🔄 [direct] 隐藏面板: controlModule (ID: control-module)

[374] [+8811ms] [LOG] 2025-07-19T14:13:25.996Z
🔄 [direct] 隐藏面板: controlModule (ID: control-module)

[375] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🎯 [direct] 隐藏前CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[376] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🎯 [direct] 隐藏前CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[377] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🎯 [direct] 隐藏后CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[378] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🎯 [direct] 隐藏后CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[379] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🔄 [direct] 步骤3-隐藏面板: 0.300ms

[380] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🔄 [direct] 步骤3-隐藏面板: 0.300ms

[381] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🔄 [direct] 显示面板: signalManager (ID: signal-manager)

[382] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🔄 [direct] 显示面板: signalManager (ID: signal-manager)

[383] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🎯 [direct] 显示前CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[384] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🎯 [direct] 显示前CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[385] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🎯 [direct] 显示后CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[386] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🎯 [direct] 显示后CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[387] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🔄 [direct] 步骤4-显示面板: 0.300ms

[388] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🔄 [direct] 步骤4-显示面板: 0.300ms

[389] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🔄 [direct] 更新3个标签状态

[390] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🔄 [direct] 更新3个标签状态

[391] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🔄 [direct] 步骤5-清除标签: 0.100ms

[392] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🔄 [direct] 步骤5-清除标签: 0.100ms

[393] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🔄 [direct] 激活标签: signalManager (data-module: signal-manager)

[394] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🔄 [direct] 激活标签: signalManager (data-module: signal-manager)

[395] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🔄 [direct] 步骤6-激活标签: 0.000ms

[396] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🔄 [direct] 步骤6-激活标签: 0.000ms

[397] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🏁 [direct] switchModule完成: 1.100ms

[398] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
🏁 [direct] switchModule完成: 1.100ms

[399] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
📊 [direct] 性能分解: {
  "状态更新": "0.000ms",
  "DOM查询": "0.100ms",
  "隐藏面板": "0.300ms",
  "显示面板": "0.300ms",
  "清除标签": "0.100ms",
  "激活标签": "0.000ms",
  "总计": "1.100ms"
}
  参数2: {
  "状态更新": "0.000ms",
  "DOM查询": "0.100ms",
  "隐藏面板": "0.300ms",
  "显示面板": "0.300ms",
  "清除标签": "0.100ms",
  "激活标签": "0.000ms",
  "总计": "1.100ms"
}

[400] [+8812ms] [LOG] 2025-07-19T14:13:25.997Z
📊 [direct] 性能分解: {
  "状态更新": "0.000ms",
  "DOM查询": "0.100ms",
  "隐藏面板": "0.300ms",
  "显示面板": "0.300ms",
  "清除标签": "0.100ms",
  "激活标签": "0.000ms",
  "总计": "1.100ms"
}
  参数2: {
  "状态更新": "0.000ms",
  "DOM查询": "0.100ms",
  "隐藏面板": "0.300ms",
  "显示面板": "0.300ms",
  "清除标签": "0.100ms",
  "激活标签": "0.000ms",
  "总计": "1.100ms"
}

[401] [+9302ms] [LOG] 2025-07-19T14:13:26.487Z
⏰ UnifiedTimerManager: 1 个定时器到期

[402] [+9302ms] [LOG] 2025-07-19T14:13:26.487Z
⏰ UnifiedTimerManager: 1 个定时器到期

[403] [+9302ms] [LOG] 2025-07-19T14:13:26.487Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 88ms

[404] [+9302ms] [LOG] 2025-07-19T14:13:26.487Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 88ms

[405] [+9303ms] [LOG] 2025-07-19T14:13:26.488Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[406] [+9303ms] [LOG] 2025-07-19T14:13:26.488Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[407] [+9303ms] [LOG] 2025-07-19T14:13:26.488Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:27

[408] [+9303ms] [LOG] 2025-07-19T14:13:26.488Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:27

[409] [+10306ms] [LOG] 2025-07-19T14:13:27.491Z
⏰ UnifiedTimerManager: 1 个定时器到期

[410] [+10306ms] [LOG] 2025-07-19T14:13:27.491Z
⏰ UnifiedTimerManager: 1 个定时器到期

[411] [+10306ms] [LOG] 2025-07-19T14:13:27.491Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[412] [+10306ms] [LOG] 2025-07-19T14:13:27.491Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[413] [+10306ms] [LOG] 2025-07-19T14:13:27.491Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[414] [+10306ms] [LOG] 2025-07-19T14:13:27.491Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[415] [+10306ms] [LOG] 2025-07-19T14:13:27.491Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:28

[416] [+10306ms] [LOG] 2025-07-19T14:13:27.491Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:28

[417] [+10434ms] [LOG] 2025-07-19T14:13:27.619Z
🎮 [DEBUG] ControlModule.handleSendSignalRequest() - 来自信号管理模块: {
  "signalId": "signal_71244_1",
  "source": "SignalManager"
}
  参数2: {
  "signalId": "signal_71244_1",
  "source": "SignalManager"
}

[418] [+10434ms] [LOG] 2025-07-19T14:13:27.619Z
🎮 [DEBUG] ControlModule.handleSendSignalRequest() - 来自信号管理模块: {
  "signalId": "signal_71244_1",
  "source": "SignalManager"
}
  参数2: {
  "signalId": "signal_71244_1",
  "source": "SignalManager"
}

[419] [+10434ms] [LOG] 2025-07-19T14:13:27.619Z
📤 [DEBUG] ControlModule: 发送 signal.request.by-ids 事件获取单个信号详情

[420] [+10434ms] [LOG] 2025-07-19T14:13:27.619Z
📤 [DEBUG] ControlModule: 发送 signal.request.by-ids 事件获取单个信号详情

[421] [+10449ms] [LOG] 2025-07-19T14:13:27.634Z
📨 [DEBUG] SignalManager.handleSignalRequestByIds() - 来源: ControlModule, IDs: signal_71244_1

[422] [+10449ms] [LOG] 2025-07-19T14:13:27.634Z
📨 [DEBUG] SignalManager.handleSignalRequestByIds() - 来源: ControlModule, IDs: signal_71244_1

[423] [+10449ms] [LOG] 2025-07-19T14:13:27.634Z
🔍 [DEBUG] 查找信号 ID: signal_71244_1, 找到: 新学习信号

[424] [+10449ms] [LOG] 2025-07-19T14:13:27.634Z
🔍 [DEBUG] 查找信号 ID: signal_71244_1, 找到: 新学习信号

[425] [+10450ms] [LOG] 2025-07-19T14:13:27.635Z
📤 [DEBUG] SignalManager: 返回 1 个指定信号，信号名称: 新学习信号

[426] [+10450ms] [LOG] 2025-07-19T14:13:27.635Z
📤 [DEBUG] SignalManager: 返回 1 个指定信号，信号名称: 新学习信号

[427] [+10450ms] [LOG] 2025-07-19T14:13:27.635Z
🔄 [DEBUG] SignalManager: 调用回调函数，传递 1 个信号

[428] [+10450ms] [LOG] 2025-07-19T14:13:27.635Z
🔄 [DEBUG] SignalManager: 调用回调函数，传递 1 个信号

[429] [+10450ms] [LOG] 2025-07-19T14:13:27.635Z
🔄 [DEBUG] ControlModule: 收到信号回调，信号数量: 1

[430] [+10450ms] [LOG] 2025-07-19T14:13:27.635Z
🔄 [DEBUG] ControlModule: 收到信号回调，信号数量: 1

[431] [+10450ms] [LOG] 2025-07-19T14:13:27.635Z
✅ [DEBUG] ControlModule: 获得信号详情: 新学习信号, 创建单个发射任务

[432] [+10450ms] [LOG] 2025-07-19T14:13:27.635Z
✅ [DEBUG] ControlModule: 获得信号详情: 新学习信号, 创建单个发射任务

[433] [+10450ms] [LOG] 2025-07-19T14:13:27.635Z
🚀 [DEBUG] ControlModule: 启动单个信号发射任务: 单个信号发射

[434] [+10450ms] [LOG] 2025-07-19T14:13:27.635Z
🚀 [DEBUG] ControlModule: 启动单个信号发射任务: 单个信号发射

[435] [+10451ms] [LOG] 2025-07-19T14:13:27.636Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 29,
  "bodyPreview": "{\"signalId\":\"signal_71244_1\"}"
}
  参数2: {
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 29,
  "bodyPreview": "{\"signalId\":\"signal_71244_1\"}"
}

[436] [+10451ms] [LOG] 2025-07-19T14:13:27.636Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 29,
  "bodyPreview": "{\"signalId\":\"signal_71244_1\"}"
}
  参数2: {
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 29,
  "bodyPreview": "{\"signalId\":\"signal_71244_1\"}"
}

[437] [+10452ms] [LOG] 2025-07-19T14:13:27.637Z
📊 StatusDisplay: 解析后的任务信息: {
  "name": "单个信号发射",
  "type": "single",
  "status": "executing",
  "totalSignals": 1,
  "currentIndex": 0,
  "currentSignal": "新学习信号"
}
  参数2: {
  "name": "单个信号发射",
  "type": "single",
  "status": "executing",
  "totalSignals": 1,
  "currentIndex": 0,
  "currentSignal": "新学习信号"
}

[438] [+10452ms] [LOG] 2025-07-19T14:13:27.637Z
📊 StatusDisplay: 解析后的任务信息: {
  "name": "单个信号发射",
  "type": "single",
  "status": "executing",
  "totalSignals": 1,
  "currentIndex": 0,
  "currentSignal": "新学习信号"
}
  参数2: {
  "name": "单个信号发射",
  "type": "single",
  "status": "executing",
  "totalSignals": 1,
  "currentIndex": 0,
  "currentSignal": "新学习信号"
}

[439] [+10452ms] [LOG] 2025-07-19T14:13:27.637Z
📊 StatusDisplay: updateSystemStatus 被调用 - status: executing, text: 执行中

[440] [+10452ms] [LOG] 2025-07-19T14:13:27.637Z
📊 StatusDisplay: updateSystemStatus 被调用 - status: executing, text: 执行中

[441] [+10452ms] [LOG] 2025-07-19T14:13:27.637Z
📊 StatusDisplay: 状态更新完成 - 显示文本: 执行中, 颜色: var(--primary-color)

[442] [+10452ms] [LOG] 2025-07-19T14:13:27.637Z
📊 StatusDisplay: 状态更新完成 - 显示文本: 执行中, 颜色: var(--primary-color)

[443] [+10452ms] [LOG] 2025-07-19T14:13:27.637Z
🔍 SystemMonitor: 收到 control.emit.started 事件，原始数据: {
  "task": {
    "id": "emit-task-1752934407635",
    "name": "单个信号发射",
    "type": "single",
    "signals": [
      {
        "id": "signal_71244_1",
        "name": "新学习信号",
        "type": "other",
        "description": "学习的红外信号",
        "signalCode": "0x552aa000000",
        "protocol": "RAW",
        "frequency": "38000",
        "data": "0x552aa000000",
        "isLearned": true,
        "created": 73322,
        "lastSent": 0,
        "sentCount": 0
      }
    ],
    "priority": 2,
    "isManual": true,
    "startTime": 1752934407635,
    "isLoopMode": false
  },
  "source": "ControlModule"
}
  参数2: {
  "task": {
    "id": "emit-task-1752934407635",
    "name": "单个信号发射",
    "type": "single",
    "signals": [
      {
        "id": "signal_71244_1",
        "name": "新学习信号",
        "type": "other",
        "description": "学习的红外信号",
        "signalCode": "0x552aa000000",
        "protocol": "RAW",
        "frequency": "38000",
        "data": "0x552aa000000",
        "isLearned": true,
        "created": 73322,
        "lastSent": 0,
        "sentCount": 0
      }
    ],
    "priority": 2,
    "isManual": true,
    "startTime": 1752934407635,
    "isLoopMode": false
  },
  "source": "ControlModule"
}

[444] [+10453ms] [LOG] 2025-07-19T14:13:27.638Z
🔍 SystemMonitor: 收到 control.emit.started 事件，原始数据: {
  "task": {
    "id": "emit-task-1752934407635",
    "name": "单个信号发射",
    "type": "single",
    "signals": [
      {
        "id": "signal_71244_1",
        "name": "新学习信号",
        "type": "other",
        "description": "学习的红外信号",
        "signalCode": "0x552aa000000",
        "protocol": "RAW",
        "frequency": "38000",
        "data": "0x552aa000000",
        "isLearned": true,
        "created": 73322,
        "lastSent": 0,
        "sentCount": 0
      }
    ],
    "priority": 2,
    "isManual": true,
    "startTime": 1752934407635,
    "isLoopMode": false
  },
  "source": "ControlModule"
}
  参数2: {
  "task": {
    "id": "emit-task-1752934407635",
    "name": "单个信号发射",
    "type": "single",
    "signals": [
      {
        "id": "signal_71244_1",
        "name": "新学习信号",
        "type": "other",
        "description": "学习的红外信号",
        "signalCode": "0x552aa000000",
        "protocol": "RAW",
        "frequency": "38000",
        "data": "0x552aa000000",
        "isLearned": true,
        "created": 73322,
        "lastSent": 0,
        "sentCount": 0
      }
    ],
    "priority": 2,
    "isManual": true,
    "startTime": 1752934407635,
    "isLoopMode": false
  },
  "source": "ControlModule"
}

[445] [+10453ms] [LOG] 2025-07-19T14:13:27.638Z
🔍 SystemMonitor: 解析后的任务信息: {
  "taskType": "single",
  "taskName": "单个信号发射",
  "taskId": "emit-task-1752934407635",
  "signalsCount": 1,
  "isLoopMode": false,
  "modeText": "单次",
  "rawEventData": {
    "task": {
      "id": "emit-task-1752934407635",
      "name": "单个信号发射",
      "type": "single",
      "signals": [
        {
          "id": "signal_71244_1",
          "name": "新学习信号",
          "type": "other",
          "description": "学习的红外信号",
          "signalCode": "0x552aa000000",
          "protocol": "RAW",
          "frequency": "38000",
          "data": "0x552aa000000",
          "isLearned": true,
          "created": 73322,
          "lastSent": 0,
          "sentCount": 0
        }
      ],
      "priority": 2,
      "isManual": true,
      "startTime": 1752934407635,
      "isLoopMode": false
    },
    "source": "ControlModule"
  }
}
  参数2: {
  "taskType": "single",
  "taskName": "单个信号发射",
  "taskId": "emit-task-1752934407635",
  "signalsCount": 1,
  "isLoopMode": false,
  "modeText": "单次",
  "rawEventData": {
    "task": {
      "id": "emit-task-1752934407635",
      "name": "单个信号发射",
      "type": "single",
      "signals": [
        {
          "id": "signal_71244_1",
          "name": "新学习信号",
          "type": "other",
          "description": "学习的红外信号",
          "signalCode": "0x552aa000000",
          "protocol": "RAW",
          "frequency": "38000",
          "data": "0x552aa000000",
          "isLearned": true,
          "created": 73322,
          "lastSent": 0,
          "sentCount": 0
        }
      ],
      "priority": 2,
      "isManual": true,
      "startTime": 1752934407635,
      "isLoopMode": false
    },
    "source": "ControlModule"
  }
}

[446] [+10453ms] [LOG] 2025-07-19T14:13:27.638Z
🔍 SystemMonitor: 解析后的任务信息: {
  "taskType": "single",
  "taskName": "单个信号发射",
  "taskId": "emit-task-1752934407635",
  "signalsCount": 1,
  "isLoopMode": false,
  "modeText": "单次",
  "rawEventData": {
    "task": {
      "id": "emit-task-1752934407635",
      "name": "单个信号发射",
      "type": "single",
      "signals": [
        {
          "id": "signal_71244_1",
          "name": "新学习信号",
          "type": "other",
          "description": "学习的红外信号",
          "signalCode": "0x552aa000000",
          "protocol": "RAW",
          "frequency": "38000",
          "data": "0x552aa000000",
          "isLearned": true,
          "created": 73322,
          "lastSent": 0,
          "sentCount": 0
        }
      ],
      "priority": 2,
      "isManual": true,
      "startTime": 1752934407635,
      "isLoopMode": false
    },
    "source": "ControlModule"
  }
}
  参数2: {
  "taskType": "single",
  "taskName": "单个信号发射",
  "taskId": "emit-task-1752934407635",
  "signalsCount": 1,
  "isLoopMode": false,
  "modeText": "单次",
  "rawEventData": {
    "task": {
      "id": "emit-task-1752934407635",
      "name": "单个信号发射",
      "type": "single",
      "signals": [
        {
          "id": "signal_71244_1",
          "name": "新学习信号",
          "type": "other",
          "description": "学习的红外信号",
          "signalCode": "0x552aa000000",
          "protocol": "RAW",
          "frequency": "38000",
          "data": "0x552aa000000",
          "isLearned": true,
          "created": 73322,
          "lastSent": 0,
          "sentCount": 0
        }
      ],
      "priority": 2,
      "isManual": true,
      "startTime": 1752934407635,
      "isLoopMode": false
    },
    "source": "ControlModule"
  }
}

[447] [+10453ms] [LOG] 2025-07-19T14:13:27.638Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[448] [+10453ms] [LOG] 2025-07-19T14:13:27.638Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[449] [+10453ms] [LOG] 2025-07-19T14:13:27.638Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[450] [+10453ms] [LOG] 2025-07-19T14:13:27.638Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[451] [+10453ms] [LOG] 2025-07-19T14:13:27.638Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[452] [+10453ms] [LOG] 2025-07-19T14:13:27.638Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[453] [+10454ms] [LOG] 2025-07-19T14:13:27.639Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[454] [+10454ms] [LOG] 2025-07-19T14:13:27.639Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[455] [+11307ms] [LOG] 2025-07-19T14:13:28.492Z
⏰ UnifiedTimerManager: 1 个定时器到期

[456] [+11307ms] [LOG] 2025-07-19T14:13:28.492Z
⏰ UnifiedTimerManager: 1 个定时器到期

[457] [+11307ms] [LOG] 2025-07-19T14:13:28.492Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[458] [+11307ms] [LOG] 2025-07-19T14:13:28.492Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[459] [+11307ms] [LOG] 2025-07-19T14:13:28.492Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[460] [+11307ms] [LOG] 2025-07-19T14:13:28.492Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[461] [+11307ms] [LOG] 2025-07-19T14:13:28.492Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:29

[462] [+11307ms] [LOG] 2025-07-19T14:13:28.492Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:29

[463] [+12313ms] [LOG] 2025-07-19T14:13:29.498Z
⏰ UnifiedTimerManager: 1 个定时器到期

[464] [+12313ms] [LOG] 2025-07-19T14:13:29.498Z
⏰ UnifiedTimerManager: 1 个定时器到期

[465] [+12313ms] [LOG] 2025-07-19T14:13:29.498Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 6ms

[466] [+12313ms] [LOG] 2025-07-19T14:13:29.498Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 6ms

[467] [+12313ms] [LOG] 2025-07-19T14:13:29.498Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[468] [+12313ms] [LOG] 2025-07-19T14:13:29.498Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[469] [+12313ms] [LOG] 2025-07-19T14:13:29.498Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:30

[470] [+12313ms] [LOG] 2025-07-19T14:13:29.498Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:30

[471] [+13332ms] [ERROR] 2025-07-19T14:13:30.517Z
🔍 HTTP错误详情: {
  "status": 400,
  "statusText": "Bad Request",
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "responseHeaders": {
    "accept-ranges": "none",
    "access-control-allow-headers": "Content-Type, Authorization",
    "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS",
    "access-control-allow-origin": "*",
    "access-control-max-age": "86400",
    "connection": "close",
    "content-length": "24",
    "content-type": "application/json"
  },
  "errorBody": "{\"error\":\"Missing body\"}"
}
  参数2: {
  "status": 400,
  "statusText": "Bad Request",
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "responseHeaders": {
    "accept-ranges": "none",
    "access-control-allow-headers": "Content-Type, Authorization",
    "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS",
    "access-control-allow-origin": "*",
    "access-control-max-age": "86400",
    "connection": "close",
    "content-length": "24",
    "content-type": "application/json"
  },
  "errorBody": "{\"error\":\"Missing body\"}"
}

[472] [+13333ms] [ERROR] 2025-07-19T14:13:30.518Z
🔍 HTTP错误详情: {
  "status": 400,
  "statusText": "Bad Request",
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "responseHeaders": {
    "accept-ranges": "none",
    "access-control-allow-headers": "Content-Type, Authorization",
    "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS",
    "access-control-allow-origin": "*",
    "access-control-max-age": "86400",
    "connection": "close",
    "content-length": "24",
    "content-type": "application/json"
  },
  "errorBody": "{\"error\":\"Missing body\"}"
}
  参数2: {
  "status": 400,
  "statusText": "Bad Request",
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "responseHeaders": {
    "accept-ranges": "none",
    "access-control-allow-headers": "Content-Type, Authorization",
    "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS",
    "access-control-allow-origin": "*",
    "access-control-max-age": "86400",
    "connection": "close",
    "content-length": "24",
    "content-type": "application/json"
  },
  "errorBody": "{\"error\":\"Missing body\"}"
}

[473] [+13333ms] [ERROR] 2025-07-19T14:13:30.518Z
ControlModule: 信号发射失败: 新学习信号 {}
  参数2: {}

[474] [+13333ms] [ERROR] 2025-07-19T14:13:30.518Z
ControlModule: 信号发射失败: 新学习信号 {}
  参数2: {}

[475] [+13333ms] [ERROR] 2025-07-19T14:13:30.518Z
ControlModule: 速率控制发射失败: {}
  参数2: {}

[476] [+13333ms] [ERROR] 2025-07-19T14:13:30.518Z
ControlModule: 速率控制发射失败: {}
  参数2: {}

[477] [+13333ms] [LOG] 2025-07-19T14:13:30.518Z
✅ ControlModule: 保存了 1 条任务历史

[478] [+13333ms] [LOG] 2025-07-19T14:13:30.518Z
✅ ControlModule: 保存了 1 条任务历史

[479] [+13333ms] [LOG] 2025-07-19T14:13:30.518Z
✅ ControlModule: 任务已添加到历史: 单个信号发射

[480] [+13333ms] [LOG] 2025-07-19T14:13:30.518Z
✅ ControlModule: 任务已添加到历史: 单个信号发射

[481] [+13333ms] [LOG] 2025-07-19T14:13:30.518Z
🔍 [UI_UPDATE] 收到任务完成事件，准备更新UI {
  "taskId": "emit-task-1752934407635",
  "taskName": "单个信号发射",
  "taskType": "single",
  "timestamp": 1752934410518
}
  参数2: {
  "taskId": "emit-task-1752934407635",
  "taskName": "单个信号发射",
  "taskType": "single",
  "timestamp": 1752934410518
}

[482] [+13333ms] [LOG] 2025-07-19T14:13:30.518Z
🔍 [UI_UPDATE] 收到任务完成事件，准备更新UI {
  "taskId": "emit-task-1752934407635",
  "taskName": "单个信号发射",
  "taskType": "single",
  "timestamp": 1752934410518
}
  参数2: {
  "taskId": "emit-task-1752934407635",
  "taskName": "单个信号发射",
  "taskType": "single",
  "timestamp": 1752934410518
}

[483] [+13334ms] [LOG] 2025-07-19T14:13:30.519Z
📊 StatusDisplay: updateSystemStatus 被调用 - status: idle, text: 待机

[484] [+13334ms] [LOG] 2025-07-19T14:13:30.519Z
📊 StatusDisplay: updateSystemStatus 被调用 - status: idle, text: 待机

[485] [+13334ms] [LOG] 2025-07-19T14:13:30.519Z
📊 StatusDisplay: 状态更新完成 - 显示文本: 待机, 颜色: var(--text-secondary)

[486] [+13334ms] [LOG] 2025-07-19T14:13:30.519Z
📊 StatusDisplay: 状态更新完成 - 显示文本: 待机, 颜色: var(--text-secondary)

[487] [+13334ms] [LOG] 2025-07-19T14:13:30.519Z
🔍 [UI_UPDATE] 任务完成UI更新完成，3秒后清空显示 {
  "timestamp": 1752934410519
}
  参数2: {
  "timestamp": 1752934410519
}

[488] [+13334ms] [LOG] 2025-07-19T14:13:30.519Z
🔍 [UI_UPDATE] 任务完成UI更新完成，3秒后清空显示 {
  "timestamp": 1752934410519
}
  参数2: {
  "timestamp": 1752934410519
}

[489] [+13334ms] [LOG] 2025-07-19T14:13:30.519Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[490] [+13334ms] [LOG] 2025-07-19T14:13:30.519Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[491] [+13334ms] [LOG] 2025-07-19T14:13:30.519Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[492] [+13334ms] [LOG] 2025-07-19T14:13:30.519Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[493] [+13334ms] [LOG] 2025-07-19T14:13:30.519Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[494] [+13335ms] [LOG] 2025-07-19T14:13:30.520Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[495] [+13335ms] [LOG] 2025-07-19T14:13:30.520Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[496] [+13335ms] [LOG] 2025-07-19T14:13:30.520Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[497] [+13335ms] [LOG] 2025-07-19T14:13:30.520Z
ControlModule: 所有任务已完成

[498] [+13335ms] [LOG] 2025-07-19T14:13:30.520Z
ControlModule: 所有任务已完成

[499] [+13349ms] [LOG] 2025-07-19T14:13:30.534Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[500] [+13349ms] [LOG] 2025-07-19T14:13:30.534Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[501] [+13349ms] [LOG] 2025-07-19T14:13:30.534Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[502] [+13349ms] [LOG] 2025-07-19T14:13:30.534Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[503] [+13350ms] [LOG] 2025-07-19T14:13:30.535Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[504] [+13350ms] [LOG] 2025-07-19T14:13:30.535Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[505] [+13350ms] [LOG] 2025-07-19T14:13:30.535Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[506] [+13350ms] [LOG] 2025-07-19T14:13:30.535Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[507] [+13350ms] [LOG] 2025-07-19T14:13:30.535Z
🔍 SystemMonitor: 收到 control.signal.emit.failed 事件: {
  "signal": {
    "id": "signal_71244_1",
    "name": "新学习信号",
    "type": "other",
    "description": "学习的红外信号",
    "signalCode": "0x552aa000000",
    "protocol": "RAW",
    "frequency": "38000",
    "data": "0x552aa000000",
    "isLearned": true,
    "created": 73322,
    "lastSent": 0,
    "sentCount": 0
  },
  "error": "ESP32发射失败: HTTP 400: Bad Request - {\"error\":\"Missing body\"}",
  "timestamp": 1752934410518,
  "source": "ControlModule"
}
  参数2: {
  "signal": {
    "id": "signal_71244_1",
    "name": "新学习信号",
    "type": "other",
    "description": "学习的红外信号",
    "signalCode": "0x552aa000000",
    "protocol": "RAW",
    "frequency": "38000",
    "data": "0x552aa000000",
    "isLearned": true,
    "created": 73322,
    "lastSent": 0,
    "sentCount": 0
  },
  "error": "ESP32发射失败: HTTP 400: Bad Request - {\"error\":\"Missing body\"}",
  "timestamp": 1752934410518,
  "source": "ControlModule"
}

[508] [+13350ms] [LOG] 2025-07-19T14:13:30.535Z
🔍 SystemMonitor: 收到 control.signal.emit.failed 事件: {
  "signal": {
    "id": "signal_71244_1",
    "name": "新学习信号",
    "type": "other",
    "description": "学习的红外信号",
    "signalCode": "0x552aa000000",
    "protocol": "RAW",
    "frequency": "38000",
    "data": "0x552aa000000",
    "isLearned": true,
    "created": 73322,
    "lastSent": 0,
    "sentCount": 0
  },
  "error": "ESP32发射失败: HTTP 400: Bad Request - {\"error\":\"Missing body\"}",
  "timestamp": 1752934410518,
  "source": "ControlModule"
}
  参数2: {
  "signal": {
    "id": "signal_71244_1",
    "name": "新学习信号",
    "type": "other",
    "description": "学习的红外信号",
    "signalCode": "0x552aa000000",
    "protocol": "RAW",
    "frequency": "38000",
    "data": "0x552aa000000",
    "isLearned": true,
    "created": 73322,
    "lastSent": 0,
    "sentCount": 0
  },
  "error": "ESP32发射失败: HTTP 400: Bad Request - {\"error\":\"Missing body\"}",
  "timestamp": 1752934410518,
  "source": "ControlModule"
}

[509] [+13351ms] [LOG] 2025-07-19T14:13:30.536Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[510] [+13351ms] [LOG] 2025-07-19T14:13:30.536Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[511] [+13351ms] [LOG] 2025-07-19T14:13:30.536Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[512] [+13351ms] [LOG] 2025-07-19T14:13:30.536Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[513] [+13351ms] [LOG] 2025-07-19T14:13:30.536Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[514] [+13351ms] [LOG] 2025-07-19T14:13:30.536Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[515] [+13351ms] [LOG] 2025-07-19T14:13:30.536Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[516] [+13351ms] [LOG] 2025-07-19T14:13:30.536Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[517] [+13352ms] [WARN] 2025-07-19T14:13:30.537Z
⚠️ SignalManager: 信号发射失败: 新学习信号, 错误: ESP32发射失败: HTTP 400: Bad Request - {"error":"Missing body"}

[518] [+13352ms] [WARN] 2025-07-19T14:13:30.537Z
⚠️ SignalManager: 信号发射失败: 新学习信号, 错误: ESP32发射失败: HTTP 400: Bad Request - {"error":"Missing body"}

[519] [+13405ms] [LOG] 2025-07-19T14:13:30.590Z
⏰ UnifiedTimerManager: 1 个定时器到期

[520] [+13405ms] [LOG] 2025-07-19T14:13:30.590Z
⏰ UnifiedTimerManager: 1 个定时器到期

[521] [+13405ms] [LOG] 2025-07-19T14:13:30.590Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[522] [+13405ms] [LOG] 2025-07-19T14:13:30.590Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[523] [+13405ms] [LOG] 2025-07-19T14:13:30.590Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[524] [+13405ms] [LOG] 2025-07-19T14:13:30.590Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[525] [+13405ms] [LOG] 2025-07-19T14:13:30.590Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:31

[526] [+13405ms] [LOG] 2025-07-19T14:13:30.590Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:31

[527] [+14512ms] [LOG] 2025-07-19T14:13:31.697Z
⏰ UnifiedTimerManager: 1 个定时器到期

[528] [+14513ms] [LOG] 2025-07-19T14:13:31.698Z
⏰ UnifiedTimerManager: 1 个定时器到期

[529] [+14513ms] [LOG] 2025-07-19T14:13:31.698Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 107ms

[530] [+14513ms] [LOG] 2025-07-19T14:13:31.698Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 107ms

[531] [+14513ms] [LOG] 2025-07-19T14:13:31.698Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[532] [+14513ms] [LOG] 2025-07-19T14:13:31.698Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[533] [+14513ms] [LOG] 2025-07-19T14:13:31.698Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:32

[534] [+14513ms] [LOG] 2025-07-19T14:13:31.698Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:32

[535] [+15606ms] [LOG] 2025-07-19T14:13:32.791Z
⏰ UnifiedTimerManager: 1 个定时器到期

[536] [+15606ms] [LOG] 2025-07-19T14:13:32.791Z
⏰ UnifiedTimerManager: 1 个定时器到期

[537] [+15607ms] [LOG] 2025-07-19T14:13:32.792Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[538] [+15607ms] [LOG] 2025-07-19T14:13:32.792Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[539] [+15607ms] [LOG] 2025-07-19T14:13:32.792Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[540] [+15607ms] [LOG] 2025-07-19T14:13:32.792Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[541] [+15607ms] [LOG] 2025-07-19T14:13:32.792Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:33

[542] [+15607ms] [LOG] 2025-07-19T14:13:32.792Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:33

[543] [+16340ms] [LOG] 2025-07-19T14:13:33.525Z
🔍 [UI_UPDATE] 执行延迟清空任务显示 {
  "timestamp": 1752934413525
}
  参数2: {
  "timestamp": 1752934413525
}

[544] [+16340ms] [LOG] 2025-07-19T14:13:33.525Z
🔍 [UI_UPDATE] 执行延迟清空任务显示 {
  "timestamp": 1752934413525
}
  参数2: {
  "timestamp": 1752934413525
}

[545] [+16341ms] [LOG] 2025-07-19T14:13:33.526Z
📊 StatusDisplay: 任务显示已完全清空到初始化状态

[546] [+16341ms] [LOG] 2025-07-19T14:13:33.526Z
📊 StatusDisplay: 任务显示已完全清空到初始化状态

[547] [+16341ms] [LOG] 2025-07-19T14:13:33.526Z
📊 StatusDisplay: updateSystemStatus 被调用 - status: idle, text: 待机

[548] [+16341ms] [LOG] 2025-07-19T14:13:33.526Z
📊 StatusDisplay: updateSystemStatus 被调用 - status: idle, text: 待机

[549] [+16341ms] [LOG] 2025-07-19T14:13:33.526Z
📊 StatusDisplay: 状态更新完成 - 显示文本: 待机, 颜色: var(--text-secondary)

[550] [+16341ms] [LOG] 2025-07-19T14:13:33.526Z
📊 StatusDisplay: 状态更新完成 - 显示文本: 待机, 颜色: var(--text-secondary)

[551] [+16606ms] [LOG] 2025-07-19T14:13:33.791Z
⏰ UnifiedTimerManager: 1 个定时器到期

[552] [+16606ms] [LOG] 2025-07-19T14:13:33.791Z
⏰ UnifiedTimerManager: 1 个定时器到期

[553] [+16606ms] [LOG] 2025-07-19T14:13:33.791Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[554] [+16606ms] [LOG] 2025-07-19T14:13:33.791Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[555] [+16607ms] [LOG] 2025-07-19T14:13:33.792Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[556] [+16607ms] [LOG] 2025-07-19T14:13:33.792Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[557] [+16607ms] [LOG] 2025-07-19T14:13:33.792Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:34

[558] [+16607ms] [LOG] 2025-07-19T14:13:33.792Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:34

[559] [+17711ms] [LOG] 2025-07-19T14:13:34.896Z
⏰ UnifiedTimerManager: 1 个定时器到期

[560] [+17711ms] [LOG] 2025-07-19T14:13:34.896Z
⏰ UnifiedTimerManager: 1 个定时器到期

[561] [+17711ms] [LOG] 2025-07-19T14:13:34.896Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[562] [+17711ms] [LOG] 2025-07-19T14:13:34.896Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[563] [+17712ms] [LOG] 2025-07-19T14:13:34.897Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[564] [+17712ms] [LOG] 2025-07-19T14:13:34.897Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[565] [+17712ms] [LOG] 2025-07-19T14:13:34.897Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:35

[566] [+17712ms] [LOG] 2025-07-19T14:13:34.897Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:35

[567] [+18801ms] [LOG] 2025-07-19T14:13:35.986Z
⏰ UnifiedTimerManager: 1 个定时器到期

[568] [+18801ms] [LOG] 2025-07-19T14:13:35.986Z
⏰ UnifiedTimerManager: 1 个定时器到期

[569] [+18801ms] [LOG] 2025-07-19T14:13:35.986Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 90ms

[570] [+18801ms] [LOG] 2025-07-19T14:13:35.986Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 90ms

[571] [+18801ms] [LOG] 2025-07-19T14:13:35.986Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[572] [+18801ms] [LOG] 2025-07-19T14:13:35.986Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[573] [+18801ms] [LOG] 2025-07-19T14:13:35.986Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:36

[574] [+18801ms] [LOG] 2025-07-19T14:13:35.986Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:36

[575] [+19805ms] [LOG] 2025-07-19T14:13:36.990Z
⏰ UnifiedTimerManager: 1 个定时器到期

[576] [+19805ms] [LOG] 2025-07-19T14:13:36.990Z
⏰ UnifiedTimerManager: 1 个定时器到期

[577] [+19805ms] [LOG] 2025-07-19T14:13:36.990Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[578] [+19805ms] [LOG] 2025-07-19T14:13:36.990Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[579] [+19806ms] [LOG] 2025-07-19T14:13:36.991Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[580] [+19806ms] [LOG] 2025-07-19T14:13:36.991Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[581] [+19806ms] [LOG] 2025-07-19T14:13:36.991Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:37

[582] [+19806ms] [LOG] 2025-07-19T14:13:36.991Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:37

[583] [+20805ms] [LOG] 2025-07-19T14:13:37.990Z
⏰ UnifiedTimerManager: 1 个定时器到期

[584] [+20805ms] [LOG] 2025-07-19T14:13:37.990Z
⏰ UnifiedTimerManager: 1 个定时器到期

[585] [+20806ms] [LOG] 2025-07-19T14:13:37.991Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[586] [+20806ms] [LOG] 2025-07-19T14:13:37.991Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[587] [+20806ms] [LOG] 2025-07-19T14:13:37.991Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[588] [+20806ms] [LOG] 2025-07-19T14:13:37.991Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[589] [+20806ms] [LOG] 2025-07-19T14:13:37.991Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:38

[590] [+20806ms] [LOG] 2025-07-19T14:13:37.991Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:38

[591] [+21808ms] [LOG] 2025-07-19T14:13:38.993Z
⏰ UnifiedTimerManager: 1 个定时器到期

[592] [+21808ms] [LOG] 2025-07-19T14:13:38.993Z
⏰ UnifiedTimerManager: 1 个定时器到期

[593] [+21808ms] [LOG] 2025-07-19T14:13:38.993Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[594] [+21808ms] [LOG] 2025-07-19T14:13:38.993Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[595] [+21808ms] [LOG] 2025-07-19T14:13:38.993Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[596] [+21808ms] [LOG] 2025-07-19T14:13:38.993Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[597] [+21808ms] [LOG] 2025-07-19T14:13:38.993Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:39

[598] [+21808ms] [LOG] 2025-07-19T14:13:38.993Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:39

[599] [+22808ms] [LOG] 2025-07-19T14:13:39.993Z
⏰ UnifiedTimerManager: 1 个定时器到期

[600] [+22808ms] [LOG] 2025-07-19T14:13:39.993Z
⏰ UnifiedTimerManager: 1 个定时器到期

[601] [+22809ms] [LOG] 2025-07-19T14:13:39.994Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[602] [+22809ms] [LOG] 2025-07-19T14:13:39.994Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[603] [+22809ms] [LOG] 2025-07-19T14:13:39.994Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[604] [+22809ms] [LOG] 2025-07-19T14:13:39.994Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[605] [+22809ms] [LOG] 2025-07-19T14:13:39.994Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:40

[606] [+22809ms] [LOG] 2025-07-19T14:13:39.994Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:40

[607] [+23347ms] [LOG] 2025-07-19T14:13:40.532Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 123419
}
  参数2: {
  "type": "ping",
  "timestamp": 123419
}

[608] [+23347ms] [LOG] 2025-07-19T14:13:40.532Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 123419
}
  参数2: {
  "type": "ping",
  "timestamp": 123419
}

[609] [+23347ms] [LOG] 2025-07-19T14:13:40.532Z
🏓 回复pong消息

[610] [+23347ms] [LOG] 2025-07-19T14:13:40.532Z
🏓 回复pong消息

[611] [+23907ms] [LOG] 2025-07-19T14:13:41.092Z
⏰ UnifiedTimerManager: 1 个定时器到期

[612] [+23907ms] [LOG] 2025-07-19T14:13:41.092Z
⏰ UnifiedTimerManager: 1 个定时器到期

[613] [+23907ms] [LOG] 2025-07-19T14:13:41.092Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[614] [+23907ms] [LOG] 2025-07-19T14:13:41.092Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[615] [+23907ms] [LOG] 2025-07-19T14:13:41.092Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[616] [+23908ms] [LOG] 2025-07-19T14:13:41.093Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[617] [+23908ms] [LOG] 2025-07-19T14:13:41.093Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:42

[618] [+23908ms] [LOG] 2025-07-19T14:13:41.093Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:42

[619] [+25007ms] [LOG] 2025-07-19T14:13:42.192Z
⏰ UnifiedTimerManager: 1 个定时器到期

[620] [+25007ms] [LOG] 2025-07-19T14:13:42.192Z
⏰ UnifiedTimerManager: 1 个定时器到期

[621] [+25007ms] [LOG] 2025-07-19T14:13:42.192Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[622] [+25007ms] [LOG] 2025-07-19T14:13:42.192Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[623] [+25007ms] [LOG] 2025-07-19T14:13:42.192Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[624] [+25007ms] [LOG] 2025-07-19T14:13:42.192Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[625] [+25007ms] [LOG] 2025-07-19T14:13:42.192Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:43

[626] [+25007ms] [LOG] 2025-07-19T14:13:42.192Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:43

[627] [+26007ms] [LOG] 2025-07-19T14:13:43.192Z
⏰ UnifiedTimerManager: 1 个定时器到期

[628] [+26007ms] [LOG] 2025-07-19T14:13:43.192Z
⏰ UnifiedTimerManager: 1 个定时器到期

[629] [+26007ms] [LOG] 2025-07-19T14:13:43.192Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[630] [+26007ms] [LOG] 2025-07-19T14:13:43.192Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[631] [+26007ms] [LOG] 2025-07-19T14:13:43.192Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[632] [+26007ms] [LOG] 2025-07-19T14:13:43.192Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[633] [+26007ms] [LOG] 2025-07-19T14:13:43.192Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:44

[634] [+26007ms] [LOG] 2025-07-19T14:13:43.192Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:44

[635] [+27107ms] [LOG] 2025-07-19T14:13:44.292Z
⏰ UnifiedTimerManager: 1 个定时器到期

[636] [+27107ms] [LOG] 2025-07-19T14:13:44.292Z
⏰ UnifiedTimerManager: 1 个定时器到期

[637] [+27107ms] [LOG] 2025-07-19T14:13:44.292Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[638] [+27107ms] [LOG] 2025-07-19T14:13:44.292Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[639] [+27107ms] [LOG] 2025-07-19T14:13:44.292Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[640] [+27108ms] [LOG] 2025-07-19T14:13:44.293Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[641] [+27108ms] [LOG] 2025-07-19T14:13:44.293Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:45

[642] [+27108ms] [LOG] 2025-07-19T14:13:44.293Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:45

[643] [+28203ms] [LOG] 2025-07-19T14:13:45.388Z
⏰ UnifiedTimerManager: 1 个定时器到期

[644] [+28203ms] [LOG] 2025-07-19T14:13:45.388Z
⏰ UnifiedTimerManager: 1 个定时器到期

[645] [+28204ms] [LOG] 2025-07-19T14:13:45.389Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 96ms

[646] [+28204ms] [LOG] 2025-07-19T14:13:45.389Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 96ms

[647] [+28204ms] [LOG] 2025-07-19T14:13:45.389Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[648] [+28204ms] [LOG] 2025-07-19T14:13:45.389Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[649] [+28204ms] [LOG] 2025-07-19T14:13:45.389Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:46

[650] [+28204ms] [LOG] 2025-07-19T14:13:45.389Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:46

[651] [+29203ms] [LOG] 2025-07-19T14:13:46.388Z
⏰ UnifiedTimerManager: 1 个定时器到期

[652] [+29203ms] [LOG] 2025-07-19T14:13:46.388Z
⏰ UnifiedTimerManager: 1 个定时器到期

[653] [+29203ms] [LOG] 2025-07-19T14:13:46.388Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[654] [+29203ms] [LOG] 2025-07-19T14:13:46.388Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[655] [+29203ms] [LOG] 2025-07-19T14:13:46.388Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[656] [+29203ms] [LOG] 2025-07-19T14:13:46.388Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[657] [+29203ms] [LOG] 2025-07-19T14:13:46.388Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:47

[658] [+29203ms] [LOG] 2025-07-19T14:13:46.388Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:47

[659] [+30204ms] [LOG] 2025-07-19T14:13:47.389Z
⏰ UnifiedTimerManager: 1 个定时器到期

[660] [+30204ms] [LOG] 2025-07-19T14:13:47.389Z
⏰ UnifiedTimerManager: 1 个定时器到期

[661] [+30204ms] [LOG] 2025-07-19T14:13:47.389Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[662] [+30204ms] [LOG] 2025-07-19T14:13:47.389Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[663] [+30204ms] [LOG] 2025-07-19T14:13:47.389Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[664] [+30204ms] [LOG] 2025-07-19T14:13:47.389Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[665] [+30205ms] [LOG] 2025-07-19T14:13:47.390Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:48

[666] [+30205ms] [LOG] 2025-07-19T14:13:47.390Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:48

[667] [+31204ms] [LOG] 2025-07-19T14:13:48.389Z
⏰ UnifiedTimerManager: 1 个定时器到期

[668] [+31205ms] [LOG] 2025-07-19T14:13:48.390Z
⏰ UnifiedTimerManager: 1 个定时器到期

[669] [+31205ms] [LOG] 2025-07-19T14:13:48.390Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[670] [+31205ms] [LOG] 2025-07-19T14:13:48.390Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[671] [+31205ms] [LOG] 2025-07-19T14:13:48.390Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[672] [+31205ms] [LOG] 2025-07-19T14:13:48.390Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[673] [+31205ms] [LOG] 2025-07-19T14:13:48.390Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:49

[674] [+31205ms] [LOG] 2025-07-19T14:13:48.390Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:49

[675] [+32304ms] [LOG] 2025-07-19T14:13:49.489Z
⏰ UnifiedTimerManager: 1 个定时器到期

[676] [+32304ms] [LOG] 2025-07-19T14:13:49.489Z
⏰ UnifiedTimerManager: 1 个定时器到期

[677] [+32304ms] [LOG] 2025-07-19T14:13:49.489Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[678] [+32304ms] [LOG] 2025-07-19T14:13:49.489Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[679] [+32304ms] [LOG] 2025-07-19T14:13:49.489Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[680] [+32304ms] [LOG] 2025-07-19T14:13:49.489Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[681] [+32304ms] [LOG] 2025-07-19T14:13:49.489Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:50

[682] [+32304ms] [LOG] 2025-07-19T14:13:49.489Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:50

[683] [+33401ms] [LOG] 2025-07-19T14:13:50.586Z
⏰ UnifiedTimerManager: 1 个定时器到期

[684] [+33402ms] [LOG] 2025-07-19T14:13:50.587Z
⏰ UnifiedTimerManager: 1 个定时器到期

[685] [+33402ms] [LOG] 2025-07-19T14:13:50.587Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[686] [+33402ms] [LOG] 2025-07-19T14:13:50.587Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[687] [+33402ms] [LOG] 2025-07-19T14:13:50.587Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[688] [+33402ms] [LOG] 2025-07-19T14:13:50.587Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[689] [+33402ms] [LOG] 2025-07-19T14:13:50.587Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:51

[690] [+33402ms] [LOG] 2025-07-19T14:13:50.587Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:51

[691] [+34401ms] [LOG] 2025-07-19T14:13:51.586Z
⏰ UnifiedTimerManager: 1 个定时器到期

[692] [+34401ms] [LOG] 2025-07-19T14:13:51.586Z
⏰ UnifiedTimerManager: 1 个定时器到期

[693] [+34401ms] [LOG] 2025-07-19T14:13:51.586Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[694] [+34401ms] [LOG] 2025-07-19T14:13:51.586Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[695] [+34401ms] [LOG] 2025-07-19T14:13:51.586Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[696] [+34401ms] [LOG] 2025-07-19T14:13:51.586Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[697] [+34401ms] [LOG] 2025-07-19T14:13:51.586Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:52

[698] [+34401ms] [LOG] 2025-07-19T14:13:51.586Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:52

[699] [+35402ms] [LOG] 2025-07-19T14:13:52.587Z
⏰ UnifiedTimerManager: 1 个定时器到期

[700] [+35402ms] [LOG] 2025-07-19T14:13:52.587Z
⏰ UnifiedTimerManager: 1 个定时器到期

[701] [+35402ms] [LOG] 2025-07-19T14:13:52.587Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[702] [+35402ms] [LOG] 2025-07-19T14:13:52.587Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[703] [+35402ms] [LOG] 2025-07-19T14:13:52.587Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[704] [+35402ms] [LOG] 2025-07-19T14:13:52.587Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[705] [+35402ms] [LOG] 2025-07-19T14:13:52.587Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:53

[706] [+35402ms] [LOG] 2025-07-19T14:13:52.587Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:53

[707] [+36402ms] [LOG] 2025-07-19T14:13:53.587Z
⏰ UnifiedTimerManager: 1 个定时器到期

[708] [+36402ms] [LOG] 2025-07-19T14:13:53.587Z
⏰ UnifiedTimerManager: 1 个定时器到期

[709] [+36402ms] [LOG] 2025-07-19T14:13:53.587Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[710] [+36402ms] [LOG] 2025-07-19T14:13:53.587Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[711] [+36402ms] [LOG] 2025-07-19T14:13:53.587Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[712] [+36402ms] [LOG] 2025-07-19T14:13:53.587Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[713] [+36402ms] [LOG] 2025-07-19T14:13:53.587Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:54

[714] [+36402ms] [LOG] 2025-07-19T14:13:53.587Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:54

[715] [+37501ms] [LOG] 2025-07-19T14:13:54.686Z
⏰ UnifiedTimerManager: 1 个定时器到期

[716] [+37501ms] [LOG] 2025-07-19T14:13:54.686Z
⏰ UnifiedTimerManager: 1 个定时器到期

[717] [+37502ms] [LOG] 2025-07-19T14:13:54.687Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[718] [+37502ms] [LOG] 2025-07-19T14:13:54.687Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[719] [+37502ms] [LOG] 2025-07-19T14:13:54.687Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[720] [+37502ms] [LOG] 2025-07-19T14:13:54.687Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[721] [+37502ms] [LOG] 2025-07-19T14:13:54.687Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:55

[722] [+37502ms] [LOG] 2025-07-19T14:13:54.687Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:55

[723] [+38501ms] [LOG] 2025-07-19T14:13:55.686Z
⏰ UnifiedTimerManager: 1 个定时器到期

[724] [+38501ms] [LOG] 2025-07-19T14:13:55.686Z
⏰ UnifiedTimerManager: 1 个定时器到期

[725] [+38501ms] [LOG] 2025-07-19T14:13:55.686Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[726] [+38501ms] [LOG] 2025-07-19T14:13:55.686Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[727] [+38502ms] [LOG] 2025-07-19T14:13:55.687Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[728] [+38502ms] [LOG] 2025-07-19T14:13:55.687Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[729] [+38502ms] [LOG] 2025-07-19T14:13:55.687Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:56

[730] [+38502ms] [LOG] 2025-07-19T14:13:55.687Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:56

[731] [+39506ms] [LOG] 2025-07-19T14:13:56.691Z
⏰ UnifiedTimerManager: 1 个定时器到期

[732] [+39506ms] [LOG] 2025-07-19T14:13:56.691Z
⏰ UnifiedTimerManager: 1 个定时器到期

[733] [+39506ms] [LOG] 2025-07-19T14:13:56.691Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 5ms

[734] [+39506ms] [LOG] 2025-07-19T14:13:56.691Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 5ms

[735] [+39506ms] [LOG] 2025-07-19T14:13:56.691Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[736] [+39507ms] [LOG] 2025-07-19T14:13:56.692Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[737] [+39507ms] [LOG] 2025-07-19T14:13:56.692Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:57

[738] [+39507ms] [LOG] 2025-07-19T14:13:56.692Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:57

[739] [+40508ms] [LOG] 2025-07-19T14:13:57.693Z
⏰ UnifiedTimerManager: 1 个定时器到期

[740] [+40508ms] [LOG] 2025-07-19T14:13:57.693Z
⏰ UnifiedTimerManager: 1 个定时器到期

[741] [+40508ms] [LOG] 2025-07-19T14:13:57.693Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[742] [+40508ms] [LOG] 2025-07-19T14:13:57.693Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[743] [+40508ms] [LOG] 2025-07-19T14:13:57.693Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[744] [+40508ms] [LOG] 2025-07-19T14:13:57.693Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[745] [+40509ms] [LOG] 2025-07-19T14:13:57.694Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:58

[746] [+40509ms] [LOG] 2025-07-19T14:13:57.694Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:58

[747] [+41513ms] [LOG] 2025-07-19T14:13:58.698Z
⏰ UnifiedTimerManager: 1 个定时器到期

[748] [+41513ms] [LOG] 2025-07-19T14:13:58.698Z
⏰ UnifiedTimerManager: 1 个定时器到期

[749] [+41513ms] [LOG] 2025-07-19T14:13:58.698Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 5ms

[750] [+41513ms] [LOG] 2025-07-19T14:13:58.698Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 5ms

[751] [+41513ms] [LOG] 2025-07-19T14:13:58.698Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[752] [+41513ms] [LOG] 2025-07-19T14:13:58.698Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[753] [+41514ms] [LOG] 2025-07-19T14:13:58.699Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:59

[754] [+41514ms] [LOG] 2025-07-19T14:13:58.699Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:13:59

[755] [+42514ms] [LOG] 2025-07-19T14:13:59.699Z
⏰ UnifiedTimerManager: 1 个定时器到期

[756] [+42515ms] [LOG] 2025-07-19T14:13:59.700Z
⏰ UnifiedTimerManager: 1 个定时器到期

[757] [+42515ms] [LOG] 2025-07-19T14:13:59.700Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[758] [+42515ms] [LOG] 2025-07-19T14:13:59.700Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[759] [+42515ms] [LOG] 2025-07-19T14:13:59.700Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[760] [+42515ms] [LOG] 2025-07-19T14:13:59.700Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[761] [+42515ms] [LOG] 2025-07-19T14:13:59.700Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:00

[762] [+42515ms] [LOG] 2025-07-19T14:13:59.700Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:00

[763] [+43515ms] [LOG] 2025-07-19T14:14:00.700Z
⏰ UnifiedTimerManager: 1 个定时器到期

[764] [+43515ms] [LOG] 2025-07-19T14:14:00.700Z
⏰ UnifiedTimerManager: 1 个定时器到期

[765] [+43515ms] [LOG] 2025-07-19T14:14:00.700Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[766] [+43515ms] [LOG] 2025-07-19T14:14:00.700Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[767] [+43515ms] [LOG] 2025-07-19T14:14:00.700Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[768] [+43515ms] [LOG] 2025-07-19T14:14:00.700Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[769] [+43516ms] [LOG] 2025-07-19T14:14:00.701Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:01

[770] [+43516ms] [LOG] 2025-07-19T14:14:00.701Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:01

[771] [+44615ms] [LOG] 2025-07-19T14:14:01.800Z
⏰ UnifiedTimerManager: 1 个定时器到期

[772] [+44615ms] [LOG] 2025-07-19T14:14:01.800Z
⏰ UnifiedTimerManager: 1 个定时器到期

[773] [+44615ms] [LOG] 2025-07-19T14:14:01.800Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[774] [+44615ms] [LOG] 2025-07-19T14:14:01.800Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[775] [+44615ms] [LOG] 2025-07-19T14:14:01.800Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[776] [+44615ms] [LOG] 2025-07-19T14:14:01.800Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[777] [+44616ms] [LOG] 2025-07-19T14:14:01.801Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:02

[778] [+44616ms] [LOG] 2025-07-19T14:14:01.801Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:02

[779] [+45709ms] [LOG] 2025-07-19T14:14:02.894Z
⏰ UnifiedTimerManager: 1 个定时器到期

[780] [+45709ms] [LOG] 2025-07-19T14:14:02.894Z
⏰ UnifiedTimerManager: 1 个定时器到期

[781] [+45709ms] [LOG] 2025-07-19T14:14:02.894Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[782] [+45709ms] [LOG] 2025-07-19T14:14:02.894Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[783] [+45709ms] [LOG] 2025-07-19T14:14:02.894Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[784] [+45709ms] [LOG] 2025-07-19T14:14:02.894Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[785] [+45709ms] [LOG] 2025-07-19T14:14:02.894Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:03

[786] [+45709ms] [LOG] 2025-07-19T14:14:02.894Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:03

[787] [+46803ms] [LOG] 2025-07-19T14:14:03.988Z
⏰ UnifiedTimerManager: 1 个定时器到期

[788] [+46803ms] [LOG] 2025-07-19T14:14:03.988Z
⏰ UnifiedTimerManager: 1 个定时器到期

[789] [+46803ms] [LOG] 2025-07-19T14:14:03.988Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[790] [+46803ms] [LOG] 2025-07-19T14:14:03.988Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[791] [+46803ms] [LOG] 2025-07-19T14:14:03.988Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[792] [+46803ms] [LOG] 2025-07-19T14:14:03.988Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[793] [+46804ms] [LOG] 2025-07-19T14:14:03.989Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:04

[794] [+46804ms] [LOG] 2025-07-19T14:14:03.989Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:04

[795] [+47806ms] [LOG] 2025-07-19T14:14:04.991Z
⏰ UnifiedTimerManager: 1 个定时器到期

[796] [+47806ms] [LOG] 2025-07-19T14:14:04.991Z
⏰ UnifiedTimerManager: 1 个定时器到期

[797] [+47806ms] [LOG] 2025-07-19T14:14:04.991Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[798] [+47806ms] [LOG] 2025-07-19T14:14:04.991Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[799] [+47807ms] [LOG] 2025-07-19T14:14:04.992Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[800] [+47807ms] [LOG] 2025-07-19T14:14:04.992Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[801] [+47807ms] [LOG] 2025-07-19T14:14:04.992Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:05

[802] [+47807ms] [LOG] 2025-07-19T14:14:04.992Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:05

[803] [+48810ms] [LOG] 2025-07-19T14:14:05.995Z
⏰ UnifiedTimerManager: 1 个定时器到期

[804] [+48810ms] [LOG] 2025-07-19T14:14:05.995Z
⏰ UnifiedTimerManager: 1 个定时器到期

[805] [+48810ms] [LOG] 2025-07-19T14:14:05.995Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[806] [+48810ms] [LOG] 2025-07-19T14:14:05.995Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[807] [+48810ms] [LOG] 2025-07-19T14:14:05.995Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[808] [+48810ms] [LOG] 2025-07-19T14:14:05.995Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[809] [+48810ms] [LOG] 2025-07-19T14:14:05.995Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:06

[810] [+48810ms] [LOG] 2025-07-19T14:14:05.995Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:06

[811] [+49812ms] [LOG] 2025-07-19T14:14:06.997Z
⏰ UnifiedTimerManager: 1 个定时器到期

[812] [+49812ms] [LOG] 2025-07-19T14:14:06.997Z
⏰ UnifiedTimerManager: 1 个定时器到期

[813] [+49812ms] [LOG] 2025-07-19T14:14:06.997Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[814] [+49812ms] [LOG] 2025-07-19T14:14:06.997Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[815] [+49813ms] [LOG] 2025-07-19T14:14:06.998Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[816] [+49813ms] [LOG] 2025-07-19T14:14:06.998Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[817] [+49813ms] [LOG] 2025-07-19T14:14:06.998Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:07

[818] [+49813ms] [LOG] 2025-07-19T14:14:06.998Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:07

[819] [+50812ms] [LOG] 2025-07-19T14:14:07.997Z
⏰ UnifiedTimerManager: 1 个定时器到期

[820] [+50813ms] [LOG] 2025-07-19T14:14:07.998Z
⏰ UnifiedTimerManager: 1 个定时器到期

[821] [+50813ms] [LOG] 2025-07-19T14:14:07.998Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[822] [+50813ms] [LOG] 2025-07-19T14:14:07.998Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[823] [+50813ms] [LOG] 2025-07-19T14:14:07.998Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[824] [+50813ms] [LOG] 2025-07-19T14:14:07.998Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[825] [+50813ms] [LOG] 2025-07-19T14:14:07.998Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:08

[826] [+50813ms] [LOG] 2025-07-19T14:14:07.998Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:08

[827] [+51904ms] [LOG] 2025-07-19T14:14:09.089Z
⏰ UnifiedTimerManager: 1 个定时器到期

[828] [+51904ms] [LOG] 2025-07-19T14:14:09.089Z
⏰ UnifiedTimerManager: 1 个定时器到期

[829] [+51904ms] [LOG] 2025-07-19T14:14:09.089Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[830] [+51904ms] [LOG] 2025-07-19T14:14:09.089Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[831] [+51904ms] [LOG] 2025-07-19T14:14:09.089Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[832] [+51904ms] [LOG] 2025-07-19T14:14:09.089Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[833] [+51904ms] [LOG] 2025-07-19T14:14:09.089Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:10

[834] [+51904ms] [LOG] 2025-07-19T14:14:09.089Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:10

[835] [+52912ms] [LOG] 2025-07-19T14:14:10.097Z
⏰ UnifiedTimerManager: 1 个定时器到期

[836] [+52912ms] [LOG] 2025-07-19T14:14:10.097Z
⏰ UnifiedTimerManager: 1 个定时器到期

[837] [+52912ms] [LOG] 2025-07-19T14:14:10.097Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 7ms

[838] [+52912ms] [LOG] 2025-07-19T14:14:10.097Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 7ms

[839] [+52912ms] [LOG] 2025-07-19T14:14:10.097Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[840] [+52912ms] [LOG] 2025-07-19T14:14:10.097Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[841] [+52912ms] [LOG] 2025-07-19T14:14:10.097Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:11

[842] [+52912ms] [LOG] 2025-07-19T14:14:10.097Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:11

[843] [+53911ms] [LOG] 2025-07-19T14:14:11.096Z
⏰ UnifiedTimerManager: 1 个定时器到期

[844] [+53911ms] [LOG] 2025-07-19T14:14:11.096Z
⏰ UnifiedTimerManager: 1 个定时器到期

[845] [+53911ms] [LOG] 2025-07-19T14:14:11.096Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[846] [+53911ms] [LOG] 2025-07-19T14:14:11.096Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[847] [+53912ms] [LOG] 2025-07-19T14:14:11.097Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[848] [+53912ms] [LOG] 2025-07-19T14:14:11.097Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[849] [+53912ms] [LOG] 2025-07-19T14:14:11.097Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:12

[850] [+53912ms] [LOG] 2025-07-19T14:14:11.097Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:12

[851] [+54911ms] [LOG] 2025-07-19T14:14:12.096Z
⏰ UnifiedTimerManager: 1 个定时器到期

[852] [+54911ms] [LOG] 2025-07-19T14:14:12.096Z
⏰ UnifiedTimerManager: 1 个定时器到期

[853] [+54912ms] [LOG] 2025-07-19T14:14:12.097Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[854] [+54912ms] [LOG] 2025-07-19T14:14:12.097Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[855] [+54912ms] [LOG] 2025-07-19T14:14:12.097Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[856] [+54912ms] [LOG] 2025-07-19T14:14:12.097Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[857] [+54912ms] [LOG] 2025-07-19T14:14:12.097Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:13

[858] [+54912ms] [LOG] 2025-07-19T14:14:12.097Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:13

[859] [+56010ms] [LOG] 2025-07-19T14:14:13.195Z
⏰ UnifiedTimerManager: 1 个定时器到期

[860] [+56010ms] [LOG] 2025-07-19T14:14:13.195Z
⏰ UnifiedTimerManager: 1 个定时器到期

[861] [+56010ms] [LOG] 2025-07-19T14:14:13.195Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[862] [+56010ms] [LOG] 2025-07-19T14:14:13.195Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[863] [+56010ms] [LOG] 2025-07-19T14:14:13.195Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[864] [+56010ms] [LOG] 2025-07-19T14:14:13.195Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[865] [+56010ms] [LOG] 2025-07-19T14:14:13.195Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:14

[866] [+56010ms] [LOG] 2025-07-19T14:14:13.195Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:14

[867] [+57010ms] [LOG] 2025-07-19T14:14:14.195Z
⏰ UnifiedTimerManager: 1 个定时器到期

[868] [+57010ms] [LOG] 2025-07-19T14:14:14.195Z
⏰ UnifiedTimerManager: 1 个定时器到期

[869] [+57010ms] [LOG] 2025-07-19T14:14:14.195Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[870] [+57010ms] [LOG] 2025-07-19T14:14:14.195Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[871] [+57010ms] [LOG] 2025-07-19T14:14:14.195Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[872] [+57010ms] [LOG] 2025-07-19T14:14:14.195Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[873] [+57010ms] [LOG] 2025-07-19T14:14:14.195Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:15

[874] [+57010ms] [LOG] 2025-07-19T14:14:14.195Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/19 22:14:15

