#include "signal_storage.h"
#include <algorithm>

SignalStorage::SignalStorage() 
    : signalsDirectory(SIGNALS_DIR)
    , indexFileName(SIGNALS_DIR "/index.json")
    , backupDirectory(SIGNALS_DIR "/backups")
    , storageMutex(nullptr)
    , isInitialized(false)
    , lastSaveTime(0)
    , autoSaveInterval(30000)  // 30秒自动保存
    , isDirty(false)
    , totalReads(0)
    , totalWrites(0)
    , cacheHits(0)
    , cacheMisses(0)
    , maxSignals(MAX_SIGNALS)
    , maxFileSize(1024 * 1024) {  // 1MB最大文件大小
}

SignalStorage::~SignalStorage() {
    stop();
}

bool SignalStorage::begin() {
    Serial.println("💾 初始化信号存储管理器...");
    
    // 创建互斥锁
    storageMutex = xSemaphoreCreateMutex();
    if (!storageMutex) {
        Serial.println("❌ 存储互斥锁创建失败");
        return false;
    }
    
    // 初始化存储目录
    if (!initializeDirectories()) {
        Serial.println("❌ 存储目录初始化失败");
        return false;
    }
    
    // 加载信号索引
    if (!loadSignalIndex()) {
        Serial.println("⚠️ 信号索引加载失败，创建新索引");
        signalIndex.clear();
        saveSignalIndex();
    }
    
    // 🔧 修复AP热点问题：只预加载有限数量的信号，避免内存不足
    if (!reloadCacheLimited()) {
        Serial.println("⚠️ 信号缓存加载失败");
    }
    
    isInitialized = true;
    lastSaveTime = millis();
    
    Serial.printf("✅ 信号存储初始化完成 - 已加载 %d 个信号\n", signalCache.size());
    
    return true;
}

void SignalStorage::loop() {
    if (!isInitialized) {
        return;
    }
    
    uint32_t currentTime = millis();
    
    // 自动保存检查
    if (isDirty && (currentTime - lastSaveTime >= autoSaveInterval)) {
        if (xSemaphoreTake(storageMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
            flushCache();
            xSemaphoreGive(storageMutex);
        }
    }
    
    // 定期清理过期备份
    static uint32_t lastCleanupTime = 0;
    if (currentTime - lastCleanupTime >= 3600000) {  // 每小时清理一次
        lastCleanupTime = currentTime;
        cleanupOldBackups();
    }
}

void SignalStorage::stop() {
    if (isInitialized) {
        Serial.println("🛑 停止信号存储管理器");
        
        // 保存所有未保存的更改
        flushCache();
        
        // 清理资源
        clearCache();
        
        if (storageMutex) {
            vSemaphoreDelete(storageMutex);
            storageMutex = nullptr;
        }
        
        isInitialized = false;
    }
}

bool SignalStorage::saveSignal(const SignalData& signal) {
    if (!isInitialized || !validateSignalData(signal)) {
        return false;
    }
    
    if (signalCache.size() >= maxSignals && signalCache.find(signal.id) == signalCache.end()) {
        logStorage("ERROR", "达到最大信号数量限制: " + String(maxSignals));
        return false;
    }
    
    if (xSemaphoreTake(storageMutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        // 添加到缓存
        signalCache[signal.id] = signal;
        
        // 更新索引
        auto it = std::find(signalIndex.begin(), signalIndex.end(), signal.id);
        if (it == signalIndex.end()) {
            signalIndex.push_back(signal.id);
        }
        
        isDirty = true;
        totalWrites++;
        
        xSemaphoreGive(storageMutex);
        
        logStorage("INFO", "信号已保存: " + signal.name + " (" + signal.id + ")");
        return true;
    }
    
    return false;
}

SignalData SignalStorage::getSignal(const String& signalId) {
    if (!isInitialized || signalId.isEmpty()) {
        return SignalData();
    }
    
    if (xSemaphoreTake(storageMutex, pdMS_TO_TICKS(500)) == pdTRUE) {
        totalReads++;
        
        // 检查缓存
        auto it = signalCache.find(signalId);
        if (it != signalCache.end()) {
            cacheHits++;
            SignalData result = it->second;
            xSemaphoreGive(storageMutex);
            return result;
        }
        
        cacheMisses++;
        
        // 从文件加载
        SignalData signal = loadSignalFile(signalId);
        if (!signal.id.isEmpty()) {
            // 添加到缓存
            signalCache[signalId] = signal;
        }
        
        xSemaphoreGive(storageMutex);
        return signal;
    }
    
    return SignalData();
}

bool SignalStorage::updateSignal(const SignalData& signal) {
    if (!isInitialized || !validateSignalData(signal)) {
        return false;
    }
    
    if (xSemaphoreTake(storageMutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        // 检查信号是否存在
        auto it = signalCache.find(signal.id);
        if (it == signalCache.end()) {
            xSemaphoreGive(storageMutex);
            return false;
        }
        
        // 更新缓存
        signalCache[signal.id] = signal;
        isDirty = true;
        totalWrites++;
        
        xSemaphoreGive(storageMutex);
        
        logStorage("INFO", "信号已更新: " + signal.name + " (" + signal.id + ")");
        return true;
    }
    
    return false;
}

bool SignalStorage::deleteSignal(const String& signalId) {
    if (!isInitialized || signalId.isEmpty()) {
        return false;
    }
    
    if (xSemaphoreTake(storageMutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        // 从缓存删除
        auto it = signalCache.find(signalId);
        if (it != signalCache.end()) {
            String signalName = it->second.name;
            signalCache.erase(it);
            
            // 从索引删除
            auto indexIt = std::find(signalIndex.begin(), signalIndex.end(), signalId);
            if (indexIt != signalIndex.end()) {
                signalIndex.erase(indexIt);
            }
            
            // 删除文件
            deleteSignalFile(signalId);
            
            isDirty = true;
            
            xSemaphoreGive(storageMutex);
            
            logStorage("INFO", "信号已删除: " + signalName + " (" + signalId + ")");
            return true;
        }
        
        xSemaphoreGive(storageMutex);
    }
    
    return false;
}

bool SignalStorage::signalExists(const String& signalId) {
    if (!isInitialized || signalId.isEmpty()) {
        return false;
    }
    
    if (xSemaphoreTake(storageMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        bool exists = (signalCache.find(signalId) != signalCache.end());
        xSemaphoreGive(storageMutex);
        return exists;
    }
    
    return false;
}

std::vector<SignalData> SignalStorage::getAllSignals() {
    std::vector<SignalData> signals;
    
    if (!isInitialized) {
        return signals;
    }
    
    if (xSemaphoreTake(storageMutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        signals.reserve(signalCache.size());
        
        for (const auto& pair : signalCache) {
            signals.push_back(pair.second);
        }
        
        xSemaphoreGive(storageMutex);
    }
    
    return signals;
}

std::vector<SignalData> SignalStorage::getSignalsByType(const String& type) {
    std::vector<SignalData> signals;
    
    if (!isInitialized || type.isEmpty()) {
        return signals;
    }
    
    if (xSemaphoreTake(storageMutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        for (const auto& pair : signalCache) {
            if (pair.second.type.equalsIgnoreCase(type)) {
                signals.push_back(pair.second);
            }
        }
        
        xSemaphoreGive(storageMutex);
    }
    
    return signals;
}

std::vector<SignalData> SignalStorage::searchSignals(const String& keyword) {
    std::vector<SignalData> signals;
    
    if (!isInitialized || keyword.isEmpty()) {
        return signals;
    }
    
    String lowerKeyword = keyword;
    lowerKeyword.toLowerCase();
    
    if (xSemaphoreTake(storageMutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        for (const auto& pair : signalCache) {
            const SignalData& signal = pair.second;
            
            String lowerName = signal.name;
            lowerName.toLowerCase();
            String lowerDesc = signal.description;
            lowerDesc.toLowerCase();
            String lowerType = signal.type;
            lowerType.toLowerCase();
            
            if (lowerName.indexOf(lowerKeyword) >= 0 || 
                lowerDesc.indexOf(lowerKeyword) >= 0 || 
                lowerType.indexOf(lowerKeyword) >= 0 ||
                signal.id.indexOf(keyword) >= 0) {
                signals.push_back(signal);
            }
        }
        
        xSemaphoreGive(storageMutex);
    }
    
    return signals;
}

bool SignalStorage::clearAllSignals() {
    if (!isInitialized) {
        return false;
    }
    
    if (xSemaphoreTake(storageMutex, pdMS_TO_TICKS(2000)) == pdTRUE) {
        uint32_t signalCount = signalCache.size();
        
        // 清空缓存
        signalCache.clear();
        signalIndex.clear();
        
        // 删除所有信号文件
        File dir = LittleFS.open(signalsDirectory);
        if (dir && dir.isDirectory()) {
            File file = dir.openNextFile();
            while (file) {
                if (!file.isDirectory() && String(file.name()).endsWith(".json")) {
                    LittleFS.remove(signalsDirectory + "/" + file.name());
                }
                file = dir.openNextFile();
            }
        }
        
        // 保存空索引
        saveSignalIndex();
        isDirty = false;
        
        xSemaphoreGive(storageMutex);
        
        logStorage("INFO", "已清空所有信号 (" + String(signalCount) + " 个)");
        return true;
    }

    return false;
}

JsonDocument SignalStorage::getStorageUsage() const {
    JsonDocument doc;

    doc["signal_count"] = signalCache.size();
    doc["max_signals"] = maxSignals;
    doc["usage_percentage"] = (float)signalCache.size() / maxSignals * 100.0;

    // 计算存储空间使用
    size_t totalSize = 0;
    File dir = LittleFS.open(signalsDirectory);
    if (dir && dir.isDirectory()) {
        File file = dir.openNextFile();
        while (file) {
            if (!file.isDirectory()) {
                totalSize += file.size();
            }
            file = dir.openNextFile();
        }
    }

    doc["storage_used_bytes"] = totalSize;
    doc["storage_used_kb"] = totalSize / 1024;
    doc["max_file_size_bytes"] = maxFileSize;

    return doc;
}

JsonDocument SignalStorage::getStorageStats() const {
    JsonDocument doc;

    doc["total_reads"] = totalReads;
    doc["total_writes"] = totalWrites;
    doc["cache_hits"] = cacheHits;
    doc["cache_misses"] = cacheMisses;
    doc["cache_hit_rate"] = (totalReads > 0) ? ((float)cacheHits / totalReads * 100.0) : 0.0;
    doc["is_dirty"] = isDirty;
    doc["last_save_time"] = lastSaveTime;
    doc["auto_save_interval"] = autoSaveInterval;

    return doc;
}

bool SignalStorage::checkStorageHealth() const {
    // 检查文件系统可用性
    if (!LittleFS.exists(signalsDirectory)) {
        return false;
    }

    // 检查索引文件完整性
    if (!LittleFS.exists(indexFileName)) {
        return false;
    }

    // 检查缓存一致性
    if (signalCache.size() != signalIndex.size()) {
        return false;
    }

    return true;
}

// ==================== 私有方法实现 ====================

bool SignalStorage::initializeDirectories() {
    // 创建信号目录
    if (!LittleFS.exists(signalsDirectory)) {
        if (!LittleFS.mkdir(signalsDirectory)) {
            logStorage("ERROR", "无法创建信号目录: " + signalsDirectory);
            return false;
        }
    }

    // 创建备份目录
    if (!LittleFS.exists(backupDirectory)) {
        if (!LittleFS.mkdir(backupDirectory)) {
            logStorage("ERROR", "无法创建备份目录: " + backupDirectory);
            return false;
        }
    }

    return true;
}

bool SignalStorage::loadSignalIndex() {
    if (!LittleFS.exists(indexFileName)) {
        return false;
    }

    File indexFile = LittleFS.open(indexFileName, "r");
    if (!indexFile) {
        logStorage("ERROR", "无法打开索引文件");
        return false;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, indexFile);
    indexFile.close();

    if (error) {
        logStorage("ERROR", "索引文件JSON解析失败: " + String(error.c_str()));
        return false;
    }

    signalIndex.clear();
    JsonArray indexArray = doc["signals"];
    for (JsonVariant signalId : indexArray) {
        signalIndex.push_back(signalId.as<String>());
    }

    logStorage("INFO", "已加载信号索引: " + String(signalIndex.size()) + " 个信号");
    return true;
}

bool SignalStorage::saveSignalIndex() {
    JsonDocument doc;
    JsonArray indexArray = doc["signals"].to<JsonArray>();

    for (const String& signalId : signalIndex) {
        indexArray.add(signalId);
    }

    doc["version"] = "1.0";
    doc["timestamp"] = millis();
    doc["count"] = signalIndex.size();

    File indexFile = LittleFS.open(indexFileName, "w");
    if (!indexFile) {
        logStorage("ERROR", "无法创建索引文件");
        return false;
    }

    size_t bytesWritten = serializeJson(doc, indexFile);
    indexFile.close();

    if (bytesWritten == 0) {
        logStorage("ERROR", "索引文件写入失败");
        return false;
    }

    return true;
}

// ==================== 私有方法实现 ====================

bool SignalStorage::flushCache() {
    if (!isInitialized) {
        return false;
    }

    if (xSemaphoreTake(storageMutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        bool success = saveSignalIndex();
        xSemaphoreGive(storageMutex);

        if (success) {
            logStorage("INFO", "缓存刷新成功");
        } else {
            logStorage("ERROR", "缓存刷新失败");
        }

        return success;
    }

    return false;
}

bool SignalStorage::reloadCache() {
    if (!isInitialized) {
        return false;
    }

    if (xSemaphoreTake(storageMutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        // 清空当前缓存
        signalCache.clear();
        signalIndex.clear();

        // 重新加载索引
        bool success = loadSignalIndex();

        // 🔧 修复：只加载有限数量的信号到缓存
        if (success) {
            uint32_t loadedCount = 0;
            const uint32_t maxCacheLoad = SIGNAL_CACHE_SIZE; // 限制为100个

            for (const String& signalId : signalIndex) {
                if (loadedCount >= maxCacheLoad) {
                    Serial.printf("📦 缓存已满，已加载 %d 个信号，剩余 %d 个按需加载\n",
                                loadedCount, signalIndex.size() - loadedCount);
                    break;
                }

                SignalData signal = loadSignalFile(signalId);
                if (!signal.id.isEmpty()) {
                    signalCache[signalId] = signal;
                    loadedCount++;
                }
            }
        }

        xSemaphoreGive(storageMutex);

        if (success) {
            logStorage("INFO", "缓存重新加载成功，信号数量: " + String(signalCache.size()));
        } else {
            logStorage("ERROR", "缓存重新加载失败");
        }

        return success;
    }

    return false;
}

void SignalStorage::clearCache() {
    if (!isInitialized) {
        return;
    }

    if (xSemaphoreTake(storageMutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        signalCache.clear();
        signalIndex.clear();
        xSemaphoreGive(storageMutex);

        logStorage("INFO", "缓存已清空");
    }
}

SignalData SignalStorage::loadSignalFile(const String& signalId) {
    SignalData signal;

    if (signalId.isEmpty()) {
        return signal;
    }

    String filePath = getSignalFilePath(signalId);

    if (!LittleFS.exists(filePath)) {
        logStorage("WARNING", "信号文件不存在: " + signalId);
        return signal;
    }

    File file = LittleFS.open(filePath, "r");
    if (!file) {
        logStorage("ERROR", "无法打开信号文件: " + signalId);
        return signal;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        logStorage("ERROR", "解析信号文件失败: " + signalId + " - " + String(error.c_str()));
        return signal;
    }

    // 从JSON恢复信号数据
    signal = SignalData::fromJson(doc);

    if (signal.id.isEmpty()) {
        logStorage("ERROR", "信号数据无效: " + signalId);
    }

    return signal;
}

bool SignalStorage::deleteSignalFile(const String& signalId) {
    if (signalId.isEmpty()) {
        return false;
    }

    String filePath = getSignalFilePath(signalId);

    if (!LittleFS.exists(filePath)) {
        logStorage("WARNING", "要删除的信号文件不存在: " + signalId);
        return true; // 文件不存在也算删除成功
    }

    bool success = LittleFS.remove(filePath);

    if (success) {
        logStorage("INFO", "信号文件删除成功: " + signalId);
    } else {
        logStorage("ERROR", "信号文件删除失败: " + signalId);
    }

    return success;
}

bool SignalStorage::reloadCacheLimited() {
    if (xSemaphoreTake(storageMutex, pdMS_TO_TICKS(5000)) != pdTRUE) {
        logStorage("ERROR", "获取存储锁失败");
        return false;
    }

    // 清空当前缓存
    signalCache.clear();

    // 重新加载索引
    bool success = loadSignalIndex();

    // 🔧 只加载有限数量的信号到缓存，避免内存不足导致AP热点不稳定
    if (success) {
        uint32_t loadedCount = 0;
        const uint32_t maxCacheLoad = SIGNAL_CACHE_SIZE; // 限制为100个

        Serial.printf("📦 开始加载信号缓存，索引中共有 %d 个信号，限制加载 %d 个\n",
                    signalIndex.size(), maxCacheLoad);

        for (const String& signalId : signalIndex) {
            if (loadedCount >= maxCacheLoad) {
                Serial.printf("📦 缓存已满，已加载 %d 个信号，剩余 %d 个按需加载\n",
                            loadedCount, signalIndex.size() - loadedCount);
                break;
            }

            SignalData signal = loadSignalFile(signalId);
            if (!signal.id.isEmpty()) {
                signalCache[signalId] = signal;
                loadedCount++;

                // 每加载10个信号喂一次看门狗，避免超时
                if (loadedCount % 10 == 0) {
                    yield(); // 让出CPU时间
                }
            }
        }

        Serial.printf("✅ 信号缓存加载完成，已加载 %d 个信号\n", loadedCount);
    }

    xSemaphoreGive(storageMutex);

    if (success) {
        logStorage("INFO", "限制性缓存加载成功，信号数量: " + String(signalCache.size()));
    } else {
        logStorage("ERROR", "限制性缓存加载失败");
    }

    return success;
}

bool SignalStorage::validateSignalData(const SignalData& signal) const {
    // 检查必需字段
    if (signal.id.isEmpty()) {
        return false;
    }

    if (signal.name.isEmpty()) {
        return false;
    }

    if (signal.signalCode.isEmpty()) {
        return false;
    }

    if (signal.protocol.isEmpty()) {
        return false;
    }

    // 检查ID格式（只允许字母、数字、下划线、连字符）
    for (int i = 0; i < signal.id.length(); i++) {
        char c = signal.id.charAt(i);
        if (!isalnum(c) && c != '_' && c != '-') {
            return false;
        }
    }

    // 检查信号代码长度
    if (signal.signalCode.length() < 10 || signal.signalCode.length() > 1000) {
        return false;
    }

    return true;
}

void SignalStorage::cleanupOldBackups() {
    if (!isInitialized) {
        return;
    }

    // 获取备份目录中的所有文件
    File backupDir = LittleFS.open(backupDirectory);
    if (!backupDir || !backupDir.isDirectory()) {
        return;
    }

    std::vector<String> backupFiles;
    File file = backupDir.openNextFile();

    while (file) {
        if (!file.isDirectory()) {
            backupFiles.push_back(file.name());
        }
        file = backupDir.openNextFile();
    }

    backupDir.close();

    // 如果备份文件数量超过最大限制，删除最旧的
    const size_t maxBackups = 10; // 最多保留10个备份
    if (backupFiles.size() > maxBackups) {
        // 按文件名排序（文件名包含时间戳）
        std::sort(backupFiles.begin(), backupFiles.end());

        size_t filesToDelete = backupFiles.size() - maxBackups;
        for (size_t i = 0; i < filesToDelete; i++) {
            String filePath = backupDirectory + "/" + backupFiles[i];
            if (LittleFS.remove(filePath)) {
                logStorage("INFO", "删除旧备份: " + backupFiles[i]);
            }
        }
    }
}

String SignalStorage::getSignalFilePath(const String& signalId) const {
    if (signalId.isEmpty()) {
        return "";
    }

    return signalsDirectory + "/" + signalId + ".json";
}

void SignalStorage::logStorage(const String& level, const String& message) {
    String logMessage = "[STORAGE] " + message;

    if (level == "ERROR") {
        Serial.println("❌ " + logMessage);
    } else if (level == "WARN") {
        Serial.println("⚠️ " + logMessage);
    } else if (level == "INFO") {
        Serial.println("ℹ️ " + logMessage);
    } else if (level == "DEBUG" && DEBUG_LEVEL >= 4) {
        Serial.println("🔍 " + logMessage);
    }
}
