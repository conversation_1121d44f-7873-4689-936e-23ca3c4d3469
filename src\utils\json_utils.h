#ifndef JSON_UTILS_H
#define JSON_UTILS_H

/**
 * @file json_utils.h
 * @brief JSON工具类 - JSON数据处理和验证
 * @details 提供JSON数据的解析、验证、转换等工具方法
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>

/**
 * @brief JSON工具类
 * @details 提供JSON数据处理的静态工具方法
 */
class JsonUtils {
public:
    // ==================== JSON验证 ====================
    
    /**
     * @brief 验证JSON字符串格式
     * @param jsonString JSON字符串
     * @return bool 是否有效
     */
    static bool isValidJson(const String& jsonString);
    
    /**
     * @brief 验证JSON对象是否包含必需字段
     * @param doc JSON文档
     * @param requiredFields 必需字段列表
     * @return bool 是否包含所有必需字段
     */
    static bool hasRequiredFields(const JsonDocument& doc, const std::vector<String>& requiredFields);
    
    /**
     * @brief 验证JSON字段类型
     * @param doc JSON文档
     * @param fieldName 字段名
     * @param expectedType 期望类型 ("string", "number", "boolean", "array", "object")
     * @return bool 类型是否匹配
     */
    static bool validateFieldType(const JsonDocument& doc, const String& fieldName, const String& expectedType);
    
    // ==================== JSON转换 ====================
    
    /**
     * @brief 字符串转JSON文档
     * @param jsonString JSON字符串
     * @param doc 输出的JSON文档
     * @return bool 转换是否成功
     */
    static bool stringToJson(const String& jsonString, JsonDocument& doc);
    
    /**
     * @brief JSON文档转字符串
     * @param doc JSON文档
     * @param prettyPrint 是否格式化输出
     * @return String JSON字符串
     */
    static String jsonToString(const JsonDocument& doc, bool prettyPrint = false);
    
    /**
     * @brief 合并两个JSON对象
     * @param target 目标JSON对象
     * @param source 源JSON对象
     * @param overwrite 是否覆盖已存在的字段
     * @return bool 合并是否成功
     */
    static bool mergeJsonObjects(JsonDocument& target, const JsonDocument& source, bool overwrite = true);
    
    // ==================== JSON查询 ====================
    
    /**
     * @brief 获取嵌套字段值
     * @param doc JSON文档
     * @param path 字段路径 (如 "user.profile.name")
     * @return JsonVariant 字段值
     */
    static JsonVariantConst getNestedValue(const JsonDocument& doc, const String& path);
    
    /**
     * @brief 设置嵌套字段值
     * @param doc JSON文档
     * @param path 字段路径
     * @param value 字段值
     * @return bool 设置是否成功
     */
    static bool setNestedValue(JsonDocument& doc, const String& path, const JsonVariant& value);
    
    /**
     * @brief 检查字段是否存在
     * @param doc JSON文档
     * @param path 字段路径
     * @return bool 字段是否存在
     */
    static bool hasField(const JsonDocument& doc, const String& path);
    
    // ==================== JSON数组操作 ====================
    
    /**
     * @brief 向JSON数组添加元素
     * @param array JSON数组
     * @param value 要添加的值
     * @return bool 添加是否成功
     */
    static bool addToArray(JsonArray& array, const JsonVariant& value);
    
    /**
     * @brief 从JSON数组删除元素
     * @param array JSON数组
     * @param index 要删除的索引
     * @return bool 删除是否成功
     */
    static bool removeFromArray(JsonArray& array, size_t index);
    
    /**
     * @brief 在JSON数组中查找元素
     * @param array JSON数组
     * @param value 要查找的值
     * @return int 元素索引 (-1表示未找到)
     */
    static int findInArray(const JsonArray& array, const JsonVariant& value);
    
    // ==================== JSON过滤和排序 ====================
    
    /**
     * @brief 过滤JSON对象字段
     * @param source 源JSON对象
     * @param allowedFields 允许的字段列表
     * @return JsonDocument 过滤后的JSON对象
     */
    static JsonDocument filterFields(const JsonDocument& source, const std::vector<String>& allowedFields);
    
    /**
     * @brief 排除JSON对象字段
     * @param source 源JSON对象
     * @param excludedFields 要排除的字段列表
     * @return JsonDocument 排除后的JSON对象
     */
    static JsonDocument excludeFields(const JsonDocument& source, const std::vector<String>& excludedFields);
    
    // ==================== JSON格式化 ====================
    
    /**
     * @brief 压缩JSON字符串
     * @param jsonString JSON字符串
     * @return String 压缩后的JSON字符串
     */
    static String compactJson(const String& jsonString);
    
    /**
     * @brief 格式化JSON字符串
     * @param jsonString JSON字符串
     * @param indent 缩进空格数
     * @return String 格式化后的JSON字符串
     */
    static String formatJson(const String& jsonString, int indent = 2);
    
    // ==================== JSON统计 ====================
    
    /**
     * @brief 计算JSON对象大小
     * @param doc JSON文档
     * @return size_t 对象大小(字节)
     */
    static size_t calculateSize(const JsonDocument& doc);
    
    /**
     * @brief 计算JSON对象字段数量
     * @param doc JSON文档
     * @param recursive 是否递归计算嵌套对象
     * @return size_t 字段数量
     */
    static size_t countFields(const JsonDocument& doc, bool recursive = false);
    
    /**
     * @brief 获取JSON对象深度
     * @param doc JSON文档
     * @return size_t 对象深度
     */
    static size_t getDepth(const JsonDocument& doc);
    
    // ==================== JSON安全性 ====================
    
    /**
     * @brief 清理JSON字符串中的危险字符
     * @param jsonString JSON字符串
     * @return String 清理后的JSON字符串
     */
    static String sanitizeJsonString(const String& jsonString);
    
    /**
     * @brief 验证JSON字符串长度
     * @param jsonString JSON字符串
     * @param maxLength 最大长度
     * @return bool 长度是否合法
     */
    static bool validateLength(const String& jsonString, size_t maxLength);
    
    /**
     * @brief 验证JSON对象深度
     * @param doc JSON文档
     * @param maxDepth 最大深度
     * @return bool 深度是否合法
     */
    static bool validateDepth(const JsonDocument& doc, size_t maxDepth);
    
    // ==================== 错误处理 ====================
    
    /**
     * @brief 获取JSON解析错误信息
     * @param error 解析错误
     * @return String 错误信息
     */
    static String getErrorMessage(DeserializationError error);
    
    /**
     * @brief 创建错误响应JSON
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param details 错误详情
     * @return JsonDocument 错误响应
     */
    static JsonDocument createErrorResponse(const String& errorCode, 
                                          const String& errorMessage, 
                                          const String& details = "");
    
    /**
     * @brief 创建成功响应JSON
     * @param data 响应数据
     * @param message 响应消息
     * @return JsonDocument 成功响应
     */
    static JsonDocument createSuccessResponse(const JsonDocument& data, 
                                            const String& message = "");

private:
    // 私有构造函数，防止实例化
    JsonUtils() = delete;
    ~JsonUtils() = delete;
    JsonUtils(const JsonUtils&) = delete;
    JsonUtils& operator=(const JsonUtils&) = delete;
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * @brief 递归计算JSON深度
     * @param value JSON值
     * @param currentDepth 当前深度
     * @return size_t 最大深度
     */
    static size_t calculateDepthRecursive(const JsonVariantConst& value, size_t currentDepth);
    
    /**
     * @brief 递归计算字段数量
     * @param value JSON值
     * @return size_t 字段数量
     */
    static size_t countFieldsRecursive(const JsonVariantConst& value);
    
    /**
     * @brief 分割路径字符串
     * @param path 路径字符串
     * @return std::vector<String> 路径组件
     */
    static std::vector<String> splitPath(const String& path);
};

#endif // JSON_UTILS_H
