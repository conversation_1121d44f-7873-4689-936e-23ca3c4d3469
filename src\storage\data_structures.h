#ifndef DATA_STRUCTURES_H
#define DATA_STRUCTURES_H

/**
 * @file data_structures.h
 * @brief 与前端完全匹配的数据结构定义
 * @details 确保前后端数据结构100%一致，支持JSON序列化
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include "config/constants.h"

// 前向声明时间管理器函数
uint64_t getCurrentUnixMs();

// ==================== 前向声明 ====================
struct SignalData;
struct TaskData;
struct APIResponse;
struct WebSocketMessage;
struct SystemStatus;

// ==================== 信号数据结构 ====================
/**
 * @brief 信号数据结构 - 与前端SignalData完全匹配
 * @details 包含12个字段，与前端JavaScript对象一一对应
 */
struct SignalData {
    String id;              // signal_12345678格式
    String name;            // 信号名称
    String type;            // 信号类型 (tv/ac/fan/light/other)
    String description;     // 信号描述
    String signalCode;      // 信号代码 (必需字段，不可为空)
    String protocol;        // 协议类型 (NEC/RC5/SONY/RAW等)
    String frequency;       // 载波频率 (字符串格式，如"38000")
    String data;            // 红外数据
    bool isLearned;         // 是否已学习
    uint64_t created;       // 13位时间戳 (创建时间)
    uint64_t lastSent;      // 最后发送时间
    uint32_t sentCount;     // 发送次数
    
    // 构造函数
    SignalData() : isLearned(false), created(0), lastSent(0), sentCount(0) {}
    
    // 验证信号数据完整性
    bool isValid() const {
        return !id.isEmpty() && !name.isEmpty() && !type.isEmpty() && 
               !signalCode.isEmpty() && !protocol.isEmpty() && !frequency.isEmpty();
    }
    
    // 转换为JSON对象
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["id"] = id;
        doc["name"] = name;
        doc["type"] = type;
        doc["description"] = description;
        doc["signalCode"] = signalCode;
        doc["protocol"] = protocol;
        doc["frequency"] = frequency;
        doc["data"] = data;
        doc["isLearned"] = isLearned;
        doc["created"] = created;
        doc["lastSent"] = lastSent;
        doc["sentCount"] = sentCount;
        return doc;
    }
    
    // 从JSON对象创建
    static SignalData fromJson(const JsonDocument& doc) {
        SignalData signal;
        signal.id = doc["id"].as<String>();
        signal.name = doc["name"].as<String>();
        signal.type = doc["type"].as<String>();
        signal.description = doc["description"].as<String>();
        signal.signalCode = doc["signalCode"].as<String>();
        signal.protocol = doc["protocol"].as<String>();
        signal.frequency = doc["frequency"].as<String>();
        signal.data = doc["data"].as<String>();
        signal.isLearned = doc["isLearned"].as<bool>();
        signal.created = doc["created"].as<uint64_t>();
        signal.lastSent = doc["lastSent"].as<uint64_t>();
        signal.sentCount = doc["sentCount"].as<uint32_t>();
        return signal;
    }
};

// ==================== 任务数据结构 ====================
/**
 * @brief 任务数据结构 - 与前端TaskData完全匹配
 * @details 包含9个字段，支持任务调度和管理
 */
struct TaskData {
    String id;              // task_12345678格式
    String name;            // 任务名称
    String type;            // 任务类型
    uint8_t priority;       // 优先级1-4
    String status;          // 状态 (pending/running/paused/completed/failed)
    std::vector<String> signals;  // 信号ID数组
    JsonDocument config;    // 任务配置
    uint64_t created;       // 创建时间
    uint64_t started;       // 开始时间
    uint64_t completed;     // 完成时间
    
    // 构造函数
    TaskData() : priority(1), created(0), started(0), completed(0) {
        status = "pending";
    }
    
    // 验证任务数据完整性
    bool isValid() const {
        return !id.isEmpty() && !name.isEmpty() && !type.isEmpty() && 
               priority >= 1 && priority <= 4;
    }
    
    // 转换为JSON对象
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["id"] = id;
        doc["name"] = name;
        doc["type"] = type;
        doc["priority"] = priority;
        doc["status"] = status;
        
        JsonArray signalsArray = doc["signals"].to<JsonArray>();
        for (const String& signalId : signals) {
            signalsArray.add(signalId);
        }
        
        doc["config"] = config;
        doc["created"] = created;
        doc["started"] = started;
        doc["completed"] = completed;
        return doc;
    }
    
    // 从JSON对象创建
    static TaskData fromJson(const JsonDocument& doc) {
        TaskData task;
        task.id = doc["id"].as<String>();
        task.name = doc["name"].as<String>();
        task.type = doc["type"].as<String>();
        task.priority = doc["priority"].as<uint8_t>();
        task.status = doc["status"].as<String>();
        
        JsonArrayConst signalsArray = doc["signals"].as<JsonArrayConst>();
        for (JsonVariantConst signalId : signalsArray) {
            task.signals.push_back(signalId.as<String>());
        }
        
        task.config = doc["config"];
        task.created = doc["created"].as<uint64_t>();
        task.started = doc["started"].as<uint64_t>();
        task.completed = doc["completed"].as<uint64_t>();
        return task;
    }
};

// ==================== API响应结构 ====================
/**
 * @brief 统一API响应格式 - 与前端APIResponse完全匹配
 * @details 包含5个字段，确保前后端响应格式一致
 */
struct APIResponse {
    bool success;           // 操作是否成功
    JsonDocument data;      // 响应数据 (成功时)
    String error;           // 错误信息 (失败时)
    String message;         // 操作消息
    uint64_t timestamp;     // 响应时间戳
    uint32_t responseTime;  // 响应时间(ms) - 符合前端架构期望

    // 构造函数
    APIResponse() : success(false), timestamp(0), responseTime(0) {}
    
    // 创建成功响应
    static APIResponse createSuccess(const JsonDocument& responseData, const String& msg = "", uint32_t processingTime = 0) {
        APIResponse response;
        response.success = true;
        response.data = responseData;
        response.message = msg;
        response.timestamp = getCurrentUnixMs();
        response.responseTime = processingTime;
        return response;
    }
    
    // 创建错误响应
    static APIResponse createError(const String& errorMsg, const String& msg = "", uint32_t processingTime = 0) {
        APIResponse response;
        response.success = false;
        response.error = errorMsg;
        response.message = msg;
        response.timestamp = getCurrentUnixMs();
        response.responseTime = processingTime;
        return response;
    }
    
    // 转换为JSON字符串
    String toJsonString() const {
        JsonDocument doc;
        doc["success"] = success;

        if (success) {
            doc["data"] = data;
            if (!message.isEmpty()) {
                doc["message"] = message;
            }
        } else {
            doc["error"] = error;
            if (!message.isEmpty()) {
                doc["message"] = message;
            }
        }

        doc["timestamp"] = timestamp;
        doc["responseTime"] = responseTime;  // 后端API处理时间 - 真实的后端性能指标

        String result;
        serializeJson(doc, result);
        return result;
    }
};

// ==================== WebSocket消息结构 ====================
/**
 * @brief WebSocket消息格式 - 与前端WebSocketMessage完全匹配
 * @details 包含3个必需字段：type, payload, timestamp
 */
struct WebSocketMessage {
    String type;            // 消息类型
    JsonDocument payload;   // 消息数据
    uint64_t timestamp;     // 毫秒时间戳
    
    // 构造函数
    WebSocketMessage() : timestamp(0) {}
    
    // 创建WebSocket消息
    static WebSocketMessage create(const String& msgType, const JsonDocument& msgPayload) {
        WebSocketMessage message;
        message.type = msgType;
        message.payload = msgPayload;
        message.timestamp = getCurrentUnixMs();
        return message;
    }
    
    // 转换为JSON字符串
    String toJsonString() const {
        JsonDocument doc;
        doc["type"] = type;
        doc["payload"] = payload;
        doc["timestamp"] = timestamp;
        
        String result;
        serializeJson(doc, result);
        return result;
    }
    
    // 验证消息格式
    bool isValid() const {
        return !type.isEmpty() && timestamp > 0;
    }
};

// ==================== 系统状态结构 ====================
/**
 * @brief 系统状态数据 - 与前端期望的status API响应匹配
 */
struct SystemStatus {
    uint32_t uptime;            // 运行时间(秒)
    float memory_usage;         // 内存使用率(%)
    uint32_t signal_count;      // 信号数量
    int32_t wifi_strength;      // WiFi信号强度(dBm)
    uint32_t free_heap;         // 可用堆内存(字节)
    float chip_temperature;     // 芯片温度(°C)

    // 构造函数
    SystemStatus() : uptime(0), memory_usage(0.0), signal_count(0),
                    wifi_strength(0), free_heap(0), chip_temperature(0.0) {}

    /**
     * @brief 转换为JSON格式
     * @return JsonDocument JSON文档
     */
    JsonDocument toJson() const {
        JsonDocument doc;

        doc["uptime"] = uptime;
        doc["memory_usage"] = memory_usage;
        doc["signal_count"] = signal_count;
        doc["wifi_strength"] = wifi_strength;
        doc["free_heap"] = free_heap;
        doc["chip_temperature"] = chip_temperature;
        doc["timestamp"] = millis();

        return doc;
    }
};

// ==================== 信号分组结构 ====================

/**
 * @brief 信号分组结构
 */
struct SignalGroup {
    String id;                          // 分组ID
    String name;                        // 分组名称
    String description;                 // 分组描述
    std::vector<String> signalIds;      // 包含的信号ID列表
    String color;                       // 分组颜色（前端显示用）
    String icon;                        // 分组图标（前端显示用）
    uint64_t createdTime;               // 创建时间
    uint64_t modifiedTime;              // 修改时间
    bool isActive;                      // 是否激活

    SignalGroup() : createdTime(0), modifiedTime(0), isActive(true) {}

    /**
     * @brief 转换为JSON格式
     * @return JsonDocument JSON文档
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        doc["id"] = id;
        doc["name"] = name;
        doc["description"] = description;
        doc["color"] = color;
        doc["icon"] = icon;
        doc["created_time"] = createdTime;
        doc["modified_time"] = modifiedTime;
        doc["is_active"] = isActive;

        JsonArray signals = doc["signal_ids"].to<JsonArray>();
        for (const String& signalId : signalIds) {
            signals.add(signalId);
        }

        return doc;
    }

    /**
     * @brief 从JSON创建信号分组
     * @param doc JSON文档
     * @return SignalGroup 信号分组对象
     */
    static SignalGroup fromJson(const JsonDocument& doc) {
        SignalGroup group;
        group.id = doc["id"].as<String>();
        group.name = doc["name"].as<String>();
        group.description = doc["description"].as<String>();
        group.color = doc["color"].as<String>();
        group.icon = doc["icon"].as<String>();
        group.createdTime = doc["created_time"].as<uint64_t>();
        group.modifiedTime = doc["modified_time"].as<uint64_t>();
        group.isActive = doc["is_active"].as<bool>();

        if (doc["signal_ids"].is<JsonArray>()) {
            JsonArrayConst signals = doc["signal_ids"];
            for (JsonVariantConst signal : signals) {
                group.signalIds.push_back(signal.as<String>());
            }
        }

        return group;
    }
};

#endif // DATA_STRUCTURES_H
