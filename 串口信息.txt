 *  正在执行任务: C:\Users\<USER>\.platformio\penv\Scripts\platformio.exe device monitor --environment esp32-s3-devkitc-1 


Please build project in debug configuration to get more details about an exception.
See https://docs.platformio.org/page/projectconf/build_configurations.html


--- Terminal on COM6 | 115200 8-N-1
--- Available filters and text transformations: colorize, debug, default, direct, esp32_exception_decoder, hexlify, log2file, nocontrol, printable, send_on_enter, time
--- More details at https://bit.ly/pio-monitor-filters
--- Quit: Ctrl+C | Menu: Ctrl+T | Help: Ctrl+T followed by Ctrl+H
02:06:00.718 > [ 43834][E][vfs_api.cpp:105] open(): /littlefs/index.html.gz does not exist, no permits for creation
02:06:00.828 > [ 43944][E][vfs_api.cpp:105] open(): /littlefs/css/main.css.gz does not exist, no permits for creation
02:06:00.903 > [ 44020][E][vfs_api.cpp:105] open(): /littlefs/css/modules.css.gz does not exist, no permits for creation
02:06:00.982 > [ 44098][E][vfs_api.cpp:105] open(): /littlefs/js/utils.js.gz does not exist, no permits for creation
02:06:01.070 > [ 44185][E][vfs_api.cpp:105] open(): /littlefs/js/data-validator.js.gz does not exist, no permits for creation
02:06:01.192 > [ 44309][E][vfs_api.cpp:105] open(): /littlefs/js/core.js.gz does not exist, no permits for creation
02:06:01.318 > [ 44435][E][vfs_api.cpp:105] open(): /littlefs/js/dom-update-manager.js.gz does not exist, no permits for creation
02:06:01.709 > [ 44825][E][vfs_api.cpp:105] open(): /littlefs/js/optimized-signal-storage.js.gz does not exist, no permits for creation
02:06:01.822 > [ 44939][E][vfs_api.cpp:105] open(): /littlefs/js/unified-timer-manager.js.gz does not exist, no permits for creation
02:06:01.922 > [ 45039][E][vfs_api.cpp:105] open(): /littlefs/js/virtual-scroll-list.js.gz does not exist, no permits for creation
02:06:02.014 > [ 45130][E][vfs_api.cpp:105] open(): /littlefs/js/signal-virtual-list.js.gz does not exist, no permits for creation
02:06:02.306 > [ 45422][E][vfs_api.cpp:105] open(): /littlefs/js/signal-manager.js.gz does not exist, no permits for creation
02:06:02.416 > [ 45533][E][vfs_api.cpp:105] open(): /littlefs/js/control-module.js.gz does not exist, no permits for creation
02:06:02.555 > [ 45671][E][vfs_api.cpp:105] open(): /littlefs/js/timer-settings.js.gz does not exist, no permits for creation
02:06:02.651 > [ 45768][E][vfs_api.cpp:105] open(): /littlefs/js/status-display.js.gz does not exist, no permits for creation
02:06:02.755 > [ 45871][E][vfs_api.cpp:105] open(): /littlefs/js/system-monitor.js.gz does not exist, no permits for creation
02:06:03.301 > [ 46418][E][vfs_api.cpp:105] open(): /littlefs/js/main.js.gz does not exist, no permits for creation
02:06:03.793 > 📤 [API] 发送成功响应: 系统状态获取成功 (处理时间: 1 ms)
02:06:03.799 > 📤 [API] 响应JSON: {"success":true,"data":{"uptime":45,"memory_usage":36.07907,"signal_count":0,"wifi_strength":0,"free_heap":221440,"chip_temperature":56.1,"timestamp":46910},"message":"系统状态获取成功","timestamp":1735689646910,"responseTime":1}
02:06:03.821 > 🚀 [API] 正在发送200状态码响应...
02:06:03.826 > 📤 [API] 200响应已发送到客户端
02:06:03.827 > 📝 GET /api/status - 200 (33 ms) [***********]
02:06:03.866 > [ 46982][E][vfs_api.cpp:105] open(): /littlefs/favicon.ico.gz does not exist, no permits for creation
02:06:03.877 > [ 46994][E][vfs_api.cpp:105] open(): /littlefs/favicon.ico does not exist, no permits for creation
02:06:03.890 > [ 47006][E][vfs_api.cpp:105] open(): /littlefs/favicon.ico/index.html.gz does not exist, no permits for creation
02:06:03.903 > [ 47019][E][vfs_api.cpp:105] open(): /littlefs/favicon.ico/index.html does not exist, no permits for creation
02:06:03.913 > 📝 GET /favicon.ico - 404 (0 ms) [***********]
02:06:03.957 > 📋 [API] 从系统配置读取硬件设置: 接收=14, 发射=21, 频率=38000
02:06:03.962 > 📤 [API] 发送成功响应: 用户配置获取成功 (处理时间: 1 ms)
02:06:03.968 > 📤 [API] 响应JSON: {"success":true,"data":{"ui_settings":{"theme":"light","language":"zh-CN","default_view":"grid","auto_refresh":true,"refresh_interval":30,"show_notifications":true,"notification_duration":5,"enable_sound":false,"compact_mode":false},"signal_settings":{"default_protocol":"NEC","default_frequency":38000,"learning_timeout":30,"emit_retry_count":3,"emit_retry_delay":100,"auto_save_learned":true,"save_dialog_timeout":15,"show_raw_data":false},"timer_settings":{"enabled":true,"default_interval":60,"max_concurrent_tasks":5,"task_timeout":300,"auto_cleanup_completed":true,"cleanup_after_days":7},"system_settings":{"debug_level":2,"log_retention_days":30,"max_log_size_mb":10,"enable_websocket":true,"websocket_heartbeat":30,"api_rate_limit":100,"enable_cors":true},"network_settings":{"wifi_auto_reconnect":true,"connection_timeout":10,"max_reconnect_attempts":5,"ap_mode_timeout":300},"hardware_settings":{"ir_recv_pin":14,"ir_send_pin":21,"ir_frequency":38000,"ir_duty_cycle":33,"status_led_pin":2,"status_led_enabled":true},"version":"1.0.0","last_modified":1735689647074,"created":1735689647074},"message":"用户配置获取成功","timestamp":1735689647084,"responseTime":1}
02:06:04.074 > 🚀 [API] 正在发送200状态码响应...
02:06:04.080 > 📤 [API] 200响应已发送到客户端
02:06:04.080 > ℹ️ [WS] 客户端连接 (客户端: 1)
02:06:04.087 > ℹ️ [WS] 发送连接事件 (客户端: 1)
02:06:04.098 > 📤 [API] 发送成功响应: 信号列表获取成功 (处理时间: 0 ms)
02:06:04.103 > 📤 [API] 响应JSON: {"success":true,"data":{"signals":[],"total":0},"message":"信号列表获取成功","timestamp":1735689647215,"responseTime":0}
02:06:04.114 > 🚀 [API] 正在发送200状态码响应...
02:06:04.120 > 📤 [API] 200响应已发送到客户端
02:06:04.124 > 📝 GET /api/signals - 200 (22 ms) [***********]
02:06:04.434 > 📝 GET /api/system/stats - 200 (311 ms) [***********]
02:06:04.442 > 📝 GET /api/system/memory - 200 (5 ms) [***********]
02:06:04.450 > 📝 GET /api/system/performance - 200 (5 ms) [***********]
02:06:04.450 > 📤 [API] 发送成功响应: 信号列表获取成功 (处理时间: 1 ms)
02:06:04.461 > 📤 [API] 响应JSON: {"success":true,"data":{"signals":[],"total":0},"message":"信号列表获取成功","timestamp":1735689647570,"responseTime":1}
02:06:04.472 > 🚀 [API] 正在发送200状态码响应...
02:06:04.477 > 📤 [API] 200响应已发送到客户端
02:06:04.477 > 📝 GET /api/signals - 200 (22 ms) [***********]
02:06:04.488 > [HTTP] GET /api/timer/tasks - 200 (1ms)
02:06:04.800 > 📝 GET /api/system/stats - 200 (308 ms) [***********]
02:06:04.808 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
02:06:04.814 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:06:20.347 > ⚠️ [WS] 未知消息类型: pong (客户端: 1)
02:06:34.429 > 🎯 [Learning API Body Handler] 被调用: len=61, index=0, total=61
02:06:34.435 > 📝 [Learning API] 请求体内容: {"command":"start","timeout":30000,"timestamp":1752948394407}
02:06:34.440 > ✅ [Learning API] 完整请求体已接收，开始处理
02:06:34.446 > 🔄 [Learning API] 执行开始学习命令
02:06:34.452 > 🎯 [IR学习] 开始学习请求: 超时=30000 ms, 信号ID=
02:06:34.456 > 📡 [IR学习] 当前引脚配置: 接收引脚=GPIO14, 发射引脚=GPIO21
02:06:34.463 > 🧹 [IR学习] 清空接收缓冲区: 0 个旧信号
02:06:34.469 > ✅ [IR学习] 学习模式已启动: ID=signal_77579_1, 超时=30000 ms
02:06:34.473 > ⏳ [IR学习] 等待信号中... (已等待: 8 ms / 30000 ms)
02:06:34.479 > ℹ️ [IR] 开始信号学习 (超时: 30000ms)
02:06:34.485 > 🔍 [Learning API] startLearning()返回: true
02:06:34.490 > ℹ️ [WS] 发送学习开始事件，超时: 30000ms
02:06:34.491 > ✅ [Learning API] 准备发送成功响应
02:06:34.496 > 📤 [API] 发送成功响应: 学习模式已启动 (处理时间: 65 ms)
02:06:34.502 > 📤 [API] 响应JSON: {"success":true,"data":{"timeout":30000,"status":"learning"},"message":"学习模式已启动","timestamp":1735689677613,"responseTime":65}
02:06:34.518 > 🚀 [API] 正在发送200状态码响应...
02:06:34.524 > 📤 [API] 200响应已发送到客户端
02:06:34.524 > ✅ [Learning API] 成功响应已发送
02:06:34.881 > 📝 GET /api/system/stats - 200 (306 ms) [***********]
02:06:34.882 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
02:06:34.894 > 📝 GET /api/system/performance - 200 (5 ms) [***********]
02:06:35.209 > 📝 GET /api/system/stats - 200 (304 ms) [***********]
02:06:35.209 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
02:06:35.223 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:06:35.535 > 📝 GET /api/system/stats - 200 (309 ms) [***********]
02:06:35.535 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
02:06:35.549 > 📝 GET /api/system/performance - 200 (3 ms) [***********]
02:06:37.315 > 📡 [IR学习] 检测到信号! 协议: GOODWEATHER, 数据: 0x552A0A000000, 位数: 48
02:06:37.342 > 🧠 [IR学习] 当前可用内存: 220196 bytes
02:06:37.342 > 📋 [IR学习] 开始创建信号数据结构
02:06:37.342 > ✅ [IR学习] 信号数据创建成功: 新学习信号
02:06:37.342 > ℹ️ [STORAGE] 信号已保存: 新学习信号 (signal_77579_1)
02:06:37.342 > 🎉 [IR学习] 信号学习完成: 新学习信号
02:06:37.343 > ℹ️ [IR] 信号学习完成: 新学习信号
02:06:37.348 > 🔍 [IR学习] WebSocket管理器状态: 可用
02:06:37.354 > 📡 [IR学习] 准备发送WebSocket消息: {"type":"learning_complete","signal":{"id":"signal_77579_1","name":"新学习信号","signalCode":"0x552aa000000","protocol":"RAW","frequency":"38000","type":"other","description":"学习的红外信号","isLearned":true,"sentCount":0,"lastSent":0,"created":80433,"data":"0x552aa000000"},"auto_save_timeout":15}
02:06:37.399 > 📡 [IR学习] 已发送WebSocket学习完成事件
02:06:37.673 > 📝 GET /api/system/stats - 200 (305 ms) [***********]
02:06:37.680 > 📝 GET /api/system/memory - 200 (3 ms) [***********]
02:06:37.680 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:06:54.588 > 🎯 [Learning API Body Handler] 被调用: len=60, index=0, total=60
02:06:54.594 > 📝 [Learning API] 请求体内容: {"command":"stop","timeout":30000,"timestamp":1752948414574}
02:06:54.600 > ✅ [Learning API] 完整请求体已接收，开始处理
02:06:54.605 > 🔄 [Learning API] 执行停止学习命令
02:06:54.612 > 🔍 [Learning API] stopLearning()返回: true
02:06:54.617 > ✅ [Learning API] 准备发送成功响应
02:06:54.617 > 📤 [API] 发送成功响应: 学习模式已停止 (处理时间: 21 ms)
02:06:54.622 > 📤 [API] 响应JSON: {"success":true,"data":{"status":"stopped"},"message":"学习模式已停止","timestamp":1735689697738,"responseTime":21}
02:06:54.639 > 🚀 [API] 正在发送200状态码响应...
02:06:54.639 > 📤 [API] 200响应已发送到客户端
02:06:54.644 > ✅ [Learning API] 成功响应已发送
02:06:54.995 > 📝 GET /api/system/stats - 200 (304 ms) [***********]
02:06:54.996 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
02:06:55.000 > 📝 GET /api/system/performance - 200 (1 ms) [***********]
02:06:55.029 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:06:55.030 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
02:06:55.346 > 📝 GET /api/system/stats - 200 (310 ms) [***********]
02:06:55.352 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:06:55.352 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
02:06:55.674 > 📝 GET /api/system/stats - 200 (302 ms) [***********]
02:07:19.159 > 🎯 [Learning API Body Handler] 被调用: len=60, index=0, total=60
02:07:19.165 > 📝 [Learning API] 请求体内容: {"command":"stop","timeout":30000,"timestamp":1752948439149}
02:07:19.170 > ✅ [Learning API] 完整请求体已接收，开始处理
02:07:19.176 > 🔄 [Learning API] 执行停止学习命令
02:07:19.181 > 🔍 [Learning API] stopLearning()返回: true
02:07:19.187 > ✅ [Learning API] 准备发送成功响应
02:07:19.187 > 📤 [API] 发送成功响应: 学习模式已停止 (处理时间: 21 ms)
02:07:19.193 > 📤 [API] 响应JSON: {"success":true,"data":{"status":"stopped"},"message":"学习模式已停止","timestamp":1735689722310,"responseTime":21}
02:07:19.208 > 🚀 [API] 正在发送200状态码响应...
02:07:19.208 > 📤 [API] 200响应已发送到客户端
02:07:19.215 > ✅ [Learning API] 成功响应已发送
02:07:19.220 > [122340][E][vfs_api.cpp:105] open(): /littlefs/index.html.gz does not exist, no permits for creation
02:07:19.584 > 📝 GET /api/system/stats - 200 (311 ms) [0.0.0.0]
02:07:19.585 > ℹ️ [WS] 客户端断开 (客户端: 1)
02:07:19.589 > 📤 [API] 发送成功响应: 系统状态获取成功 (处理时间: 0 ms)
02:07:19.595 > 📤 [API] 响应JSON: {"success":true,"data":{"uptime":121,"memory_usage":36.2417,"signal_count":1,"wifi_strength":0,"free_heap":220744,"chip_temperature":55.1,"timestamp":122706},"message":"系统状态获取成功","timestamp":1735689722717,"responseTime":0}
02:07:19.617 > 🚀 [API] 正在发送200状态码响应...
02:07:19.623 > 📤 [API] 200响应已发送到客户端
02:07:19.628 > 📝 GET /api/status - 200 (32 ms) [***********]
02:07:19.658 > 📋 [API] 从系统配置读取硬件设置: 接收=14, 发射=21, 频率=38000
02:07:19.663 > 📤 [API] 发送成功响应: 用户配置获取成功 (处理时间: 1 ms)
02:07:19.669 > 📤 [API] 响应JSON: {"success":true,"data":{"ui_settings":{"theme":"light","language":"zh-CN","default_view":"grid","auto_refresh":true,"refresh_interval":30,"show_notifications":true,"notification_duration":5,"enable_sound":false,"compact_mode":false},"signal_settings":{"default_protocol":"NEC","default_frequency":38000,"learning_timeout":30,"emit_retry_count":3,"emit_retry_delay":100,"auto_save_learned":true,"save_dialog_timeout":15,"show_raw_data":false},"timer_settings":{"enabled":true,"default_interval":60,"max_concurrent_tasks":5,"task_timeout":300,"auto_cleanup_completed":true,"cleanup_after_days":7},"system_settings":{"debug_level":2,"log_retention_days":30,"max_log_size_mb":10,"enable_websocket":true,"websocket_heartbeat":30,"api_rate_limit":100,"enable_cors":true},"network_settings":{"wifi_auto_reconnect":true,"connection_timeout":10,"max_reconnect_attempts":5,"ap_mode_timeout":300},"hardware_settings":{"ir_recv_pin":14,"ir_send_pin":21,"ir_frequency":38000,"ir_duty_cycle":33,"status_led_pin":2,"status_led_enabled":true},"version":"1.0.0","last_modified":1735689722778,"created":1735689722778},"message":"用户配置获取成功","timestamp":1735689722788,"responseTime":1}
02:07:19.775 > 🚀 [API] 正在发送200状态码响应...
02:07:19.781 > 📤 [API] 200响应已发送到客户端
02:07:19.781 > ℹ️ [WS] 客户端连接 (客户端: 2)
02:07:19.786 > ℹ️ [WS] 发送连接事件 (客户端: 2)
02:07:20.518 > 📤 [API] 发送成功响应: 信号列表获取成功 (处理时间: 681 ms)
02:07:20.523 > 📤 [API] 响应JSON: {"success":true,"data":{"signals":[{"id":"signal_77579_1","name":"新学习信号","type":"other","description":"学习的红外信号","signalCode":"0x552aa000000","protocol":"RAW","frequency":"38000","data":"0x552aa000000","isLearned":true,"created":80433,"lastSent":0,"sentCount":0}],"total":1},"message":"信号列表获取成功","timestamp":1735689723638,"responseTime":681}
02:07:20.594 > 🚀 [API] 正在发送200状态码响应...
02:07:20.594 > 📤 [API] 200响应已发送到客户端
02:07:20.594 > 📝 GET /api/signals - 200 (723 ms) [***********]
02:07:20.594 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
02:07:20.890 > 📝 GET /api/system/stats - 200 (312 ms) [***********]
02:07:20.896 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:07:20.896 > [HTTP] GET /api/timer/tasks - 200 (1ms)
02:07:21.718 > 📤 [API] 发送成功响应: 信号列表获取成功 (处理时间: 813 ms)
02:07:21.724 > 📤 [API] 响应JSON: {"success":true,"data":{"signals":[{"id":"signal_77579_1","name":"新学习信号","type":"other","description":"学习的红外信号","signalCode":"0x552aa000000","protocol":"RAW","frequency":"38000","data":"0x552aa000000","isLearned":true,"created":80433,"lastSent":0,"sentCount":0}],"total":1},"message":"信号列表获取成功","timestamp":1735689724838,"responseTime":813}
02:07:21.777 > 🚀 [API] 正在发送200状态码响应...
02:07:21.777 > 📤 [API] 200响应已发送到客户端
02:07:21.777 > 📝 GET /api/signals - 200 (855 ms) [***********]
02:07:21.777 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
02:07:22.083 > 📝 GET /api/system/stats - 200 (305 ms) [***********]
02:07:22.089 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:07:22.097 > ⚠️ [WS] 未知消息类型: pong (客户端: 2)
02:07:25.483 > 📤 [API] 处理信号发射请求: len=29, index=0, total=29
02:07:25.489 > 📤 [IR发射] 开始发射信号: signal_77579_1
02:07:25.489 > 🧠 [IR发射] 当前可用内存: 216508 bytes
02:07:25.495 > 🔍 [IR发射] 正在查找信号: signal_77579_1
02:07:25.500 > ✅ [IR发射] 找到信号: 新学习信号, 协议: RAW
02:07:25.506 > ℹ️ [IR] 发射信号: 新学习信号 (RAW)
02:07:25.512 > ℹ️ [IR] 发射RAW协议信号: 新学习信号
02:07:25.512 > 📡 [RAW发射] 发送RAW数据: 0x552AA000000
02:07:25.627 > ✅ [RAW发射] RAW数据发送完成: 新学习信号
02:07:25.627 > ℹ️ [IR] 信号发射成功: 新学习信号
02:07:25.632 > 🎉 [IR发射] 信号发射成功: signal_77579_1
02:07:26.517 > ℹ️ [STORAGE] 信号已更新: 新学习信号 (signal_77579_1)
02:07:26.523 > 📤 [API] 发送成功响应: 信号发射成功 (处理时间: 1034 ms)
02:07:26.529 > 📤 [API] 响应JSON: {"success":true,"data":{"signal_id":"signal_77579_1","signal_name":"新学习信号","sent_count":1,"last_sent":128758},"message":"信号发射成功","timestamp":1735689729648,"responseTime":1034}
02:07:26.546 > 🚀 [API] 正在发送200状态码响应...
02:07:26.551 > 📤 [API] 200响应已发送到客户端
02:07:26.557 > 📝 POST /api/emit/signal - 200 (1067 ms) [***********]
02:07:26.867 > 📝 GET /api/system/stats - 200 (303 ms) [***********]
02:07:26.867 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
02:07:26.873 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:07:27.223 > 📝 GET /api/system/stats - 200 (305 ms) [***********]
02:07:27.223 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
02:07:27.234 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:07:27.549 > 📝 GET /api/system/stats - 200 (304 ms) [***********]
02:07:27.549 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
02:07:27.566 > 📝 GET /api/system/performance - 200 (6 ms) [***********]
02:07:27.880 > 📝 GET /api/system/stats - 200 (310 ms) [***********]
02:07:27.880 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
02:07:27.886 > 📤 [API] 处理信号发射请求: len=29, index=0, total=29
02:07:27.892 > 📤 [IR发射] 开始发射信号: signal_77579_1
02:07:27.898 > 🧠 [IR发射] 当前可用内存: 205692 bytes
02:07:27.902 > 🔍 [IR发射] 正在查找信号: signal_77579_1
02:07:27.909 > ✅ [IR发射] 找到信号: 新学习信号, 协议: RAW
02:07:27.914 > ℹ️ [IR] 发射信号: 新学习信号 (RAW)
02:07:27.914 > ℹ️ [IR] 发射RAW协议信号: 新学习信号
02:07:27.919 > 📡 [RAW发射] 发送RAW数据: 0x552AA000000
02:07:28.030 > ✅ [RAW发射] RAW数据发送完成: 新学习信号
02:07:28.030 > ℹ️ [IR] 信号发射成功: 新学习信号
02:07:28.037 > 🎉 [IR发射] 信号发射成功: signal_77579_1
02:07:28.917 > ℹ️ [STORAGE] 信号已更新: 新学习信号 (signal_77579_1)
02:07:28.923 > 📤 [API] 发送成功响应: 信号发射成功 (处理时间: 1029 ms)
02:07:28.929 > 📤 [API] 响应JSON: {"success":true,"data":{"signal_id":"signal_77579_1","signal_name":"新学习信号","sent_count":2,"last_sent":131162},"message":"信号发射成功","timestamp":1735689732048,"responseTime":1029}
02:07:28.946 > 🚀 [API] 正在发送200状态码响应...
02:07:28.950 > 📤 [API] 200响应已发送到客户端
02:07:28.956 > 📝 POST /api/emit/signal - 200 (1062 ms) [***********]
02:07:28.961 > 📝 GET /api/system/performance - 200 (4 ms) [***********]
02:07:28.961 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
02:07:29.276 > 📝 GET /api/system/stats - 200 (311 ms) [***********]
02:07:29.285 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
02:07:29.285 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:07:29.611 > 📝 GET /api/system/stats - 200 (310 ms) [***********]
02:07:29.611 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
02:07:29.621 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:07:29.636 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
02:07:29.948 > 📝 GET /api/system/stats - 200 (307 ms) [***********]
02:07:29.949 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:07:29.954 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
02:07:30.300 > 📝 GET /api/system/stats - 200 (306 ms) [***********]
02:07:30.300 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
02:07:30.305 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:07:30.629 > 📝 GET /api/system/stats - 200 (303 ms) [***********]
02:07:30.636 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:07:30.950 > 📝 GET /api/system/stats - 200 (303 ms) [***********]
02:07:30.956 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
02:08:20.401 > ⚠️ [WS] 未知消息类型: pong (客户端: 2)
02:09:20.388 > ⚠️ [WS] 未知消息类型: pong (客户端: 2)
02:10:20.392 > ⚠️ [WS] 未知消息类型: pong (客户端: 2)
02:11:20.404 > ⚠️ [WS] 未知消息类型: pong (客户端: 2)
02:12:20.385 > ⚠️ [WS] 未知消息类型: pong (客户端: 2)
02:13:20.382 > ⚠️ [WS] 未知消息类型: pong (客户端: 2)