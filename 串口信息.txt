--- Terminal on COM5 | 115200 8-N-1
--- Available filters and text transformations: colorize, debug, default, direct, esp32_exception_decoder, hexlify, log2file, nocontrol, printable, send_on_enter, time
--- More details at https://bit.ly/pio-monitor-filters
--- Quit: Ctrl+C | Menu: Ctrl+T | Help: Ctrl+T followed by Ctrl+H
04:04:27.421 > ESP-ROM:esp32s3-20210327
04:04:27.429 > Build:Mar 27 2021
04:04:27.429 > rst:0x1 (POWERON),boot:0x8 (SPI_FAST_FLASH_BOOT)
04:04:27.435 > SPIWP:0xee
04:04:27.435 > mode:DIO, clock div:1
04:04:27.435 > load:0x3fce3808,len:0x4bc
04:04:27.495 > load:0x403c9700,len:0xbd8
04:04:27.495 > load:0x403cc700,len:0x2a0c
04:04:27.495 > entry 0x403c98d0
04:04:27.695 > E (273) esp_core_dump_flash: No core dump partition found!
04:04:27.696 > E (273) esp_core_dump_flash: No core dump partition found!
04:04:27.702 > [   274][I][esp32-hal-psram.c:96] psramInit(): PSRAM enabled
04:04:28.723 > [  1299][W][WiFiGeneric.cpp:1408] setTxPower(): Neither AP or STA has been started
04:04:28.729 >
04:04:28.729 > ==================================================
04:04:28.733 > 🚀 ESP32-S3红外控制系统启动
04:04:28.733 > 版本: 1.0.0
04:04:28.739 > 构建日期: Jul 20 2025 03:46:54
04:04:28.739 > 芯片型号: ESP32-S3
04:04:28.745 > 芯片版本: 0
04:04:28.745 > Flash大小: 16MB
04:04:28.745 > SRAM大小: 345KB
04:04:28.745 > CPU频率: 240MHz
04:04:28.751 > MAC地址: 24:EC:4A:01:D2:50
04:04:28.751 > ==================================================
04:04:28.756 >
04:04:28.756 > 🕐 初始化时间管理器...
04:04:28.762 > 🌐 NTP服务器: pool.ntp.org, time.nist.gov, time.cloudflare.com
04:04:28.767 > 🌍 时区: CST-8
04:04:28.767 > ✅ 时间管理器初始化完成，等待WiFi连接后同步NTP
04:04:28.773 > 🚀 ESP32-S3红外控制系统启动中...
04:04:29.775 > 🔧 初始化硬件...
04:04:29.775 > ✅ 硬件初始化完成 - CPU: 240MHz, Flash: 16MB
04:04:29.780 > 💾 初始化文件系统...
04:04:29.785 > ✅ LittleFS挂载成功
04:04:29.853 > 🧪 测试文件创建权限...
04:04:29.877 > ✅ 文件创建权限测试通过
04:04:30.084 > ✅ 文件系统初始化完成 - 总空间: 2031616 bytes, 已用: 786432 bytes
04:04:30.142 > [INFO] 2719 - 配置加载成功 (SystemManager::loadConfig)
04:04:30.148 > 🔌 初始化组件...
04:04:30.148 > 📶 初始化WiFi管理器...
04:04:30.148 > [  2719][W][WiFiGeneric.cpp:1408] setTxPower(): Neither AP or STA has been started  
04:04:30.204 > 📶 WiFi STA 启动
04:04:30.262 > ✅ 加载了 0 个网络配置
04:04:30.262 > 📶 没有配置的网络，启动AP模式
04:04:30.267 > 📡 启动AP模式: ESP32-IR-Control
04:04:30.374 > 📡 WiFi AP 启动
04:04:30.840 > ✅ AP模式启动成功
04:04:30.840 > 📡 SSID: ESP32-IR-Control
04:04:30.840 > 📡 WiFi AP 启动
04:04:30.845 > 📍 IP地址: ***********
04:04:30.845 > 📻 信道: 6
04:04:30.845 > ✅ WiFi管理器初始化完成
04:04:30.851 > 💾 初始化信号存储管理器...
04:04:30.937 > ℹ️ [STORAGE] 已加载信号索引: 0 个信号
04:04:30.984 > ℹ️ [STORAGE] 已加载信号索引: 0 个信号
04:04:30.984 > 📦 开始加载信号缓存，索引中共有 0 个信号，限制加载 100 个
04:04:30.996 > ✅ 信号缓存加载完成，已加载 0 个信号
04:04:30.996 > ℹ️ [STORAGE] 限制性缓存加载成功，信号数量: 0
04:04:31.002 > ✅ 信号存储初始化完成 - 已加载 0 个信号
04:04:31.007 > 🔌 初始化WebSocket服务器 (端口: 8001)...
04:04:31.012 > ✅ WebSocket服务器启动成功: ws://***********:8001/ws
04:04:31.018 > 📋 从配置加载硬件设置: 发射引脚=21, 接收引脚=14, 频率=38000, 占空比=33
04:04:31.029 > 📡 初始化红外管理器...
04:04:31.029 > ✅ 红外管理器初始化完成
04:04:31.034 > 📡 发射引脚: GPIO21, 接收引脚: GPIO14
04:04:31.034 > 📶 频率: 38000 Hz, 占空比: 33%
04:04:31.039 > 🌐 初始化HTTP服务器 (端口: 8000)...
04:04:31.046 > 🛣️ 设置API路由...
04:04:31.046 > 🔧 注册路由: POST /api/learning
04:04:31.051 > ✅ 路由已注册: POST /api/learning
04:04:31.051 > 🔧 注册路由: POST /api/learning/detect
04:04:31.057 > ✅ 路由已注册: POST /api/learning/detect
04:04:31.062 > 🔧 注册路由: POST /api/emit/signal
04:04:31.068 > ✅ 路由已注册: POST /api/emit/signal
04:04:31.068 > ✅ API路由设置完成
04:04:31.093 > 📁 静态文件服务已启用 (根目录) - 调试模式
04:04:31.093 > ✅ HTTP服务器启动成功: http://0.0.0.0:8000
04:04:31.097 > 📊 初始化系统监控器...
04:04:31.309 > ✅ 系统监控器初始化完成
04:04:31.310 > 🕐 初始化定时器管理器...
04:04:31.416 > [  3993][E][vfs_api.cpp:105] open(): /littlefs/timer_tasks.json does not exist, no permits for creation
04:04:31.422 > 📂 TimerManagerBackend: 任务文件不存在，使用默认配置
04:04:31.427 > ✅ 定时器管理器初始化完成，加载了 0 个任务
04:04:31.433 > ✅ 组件初始化完成
04:04:31.438 > ⚙️ 创建系统任务...
04:04:31.438 > ✅ 系统任务创建完成
04:04:31.442 > 🔍 执行系统自检...
04:04:31.457 > ⚠️ WiFi未连接，但系统可以继续运行
04:04:31.457 > ✅ 系统自检通过
04:04:31.462 > ✅ ESP32-S3红外控制系统启动完成
04:04:31.462 > 🌐 HTTP服务器 (AP模式): http://***********:8000
04:04:31.469 > 🔌 WebSocket服务器 (AP模式): ws://***********:8001/ws
04:04:31.473 > 💾 可用内存: 225824 bytes
04:04:31.480 > 🔥 芯片温度: 53.9°C
04:04:31.480 > ⚠️ WiFi未连接，无法同步NTP时间
04:04:31.485 > 🎉 系统初始化完成，进入主循环
04:04:35.187 > 📡 客户端连接到AP
04:04:47.694 > [ 20271][E][vfs_api.cpp:105] open(): /littlefs/index.html.gz does not exist, no permits for creation
04:04:57.188 > [ 29766][E][vfs_api.cpp:105] open(): /littlefs/index.html.gz does not exist, no permits for creation
04:06:00.741 > [ 93322][E][vfs_api.cpp:105] open(): /littlefs/index.html.gz does not exist, no permits for creation
04:06:01.176 > [ 93757][E][vfs_api.cpp:105] open(): /littlefs/css/main.css.gz does not exist, no permits for creation
04:06:01.285 > [ 93866][E][vfs_api.cpp:105] open(): /littlefs/css/modules.css.gz does not exist, no permits for creation
04:06:02.322 > [ 94903][E][vfs_api.cpp:105] open(): /littlefs/js/utils.js.gz does not exist, no permits for creation
04:06:02.426 > [ 95007][E][vfs_api.cpp:105] open(): /littlefs/js/data-validator.js.gz does not exist, no permits for creation
04:06:02.567 > [ 95148][E][vfs_api.cpp:105] open(): /littlefs/js/core.js.gz does not exist, no permits for creation
04:06:02.896 > [ 95477][E][vfs_api.cpp:105] open(): /littlefs/js/dom-update-manager.js.gz does not exist, no permits for creation
04:06:03.173 > [ 95755][E][vfs_api.cpp:105] open(): /littlefs/js/optimized-signal-storage.js.gz does not exist, no permits for creation
04:06:03.556 > [ 96138][E][vfs_api.cpp:105] open(): /littlefs/js/unified-timer-manager.js.gz does not exist, no permits for creation
04:06:03.666 > [ 96246][E][vfs_api.cpp:105] open(): /littlefs/js/virtual-scroll-list.js.gz does not exist, no permits for creation
04:06:03.777 > [ 96358][E][vfs_api.cpp:105] open(): /littlefs/js/signal-virtual-list.js.gz does not exist, no permits for creation
04:06:04.158 > [ 96740][E][vfs_api.cpp:105] open(): /littlefs/js/signal-manager.js.gz does not exist, no permits for creation
04:06:04.678 > [ 97259][E][vfs_api.cpp:105] open(): /littlefs/js/control-module.js.gz does not exist, no permits for creation
04:06:04.832 > [ 97412][E][vfs_api.cpp:105] open(): /littlefs/js/timer-settings.js.gz does not exist, no permits for creation
04:06:05.595 > [ 98176][E][vfs_api.cpp:105] open(): /littlefs/js/status-display.js.gz does not exist, no permits for creation
04:06:07.321 > [ 99903][E][vfs_api.cpp:105] open(): /littlefs/js/system-monitor.js.gz does not exist, no permits for creation
04:06:10.691 > [103272][E][vfs_api.cpp:105] open(): /littlefs/js/main.js.gz does not exist, no permits for creation
04:06:16.632 > 📤 [API] 发送成功响应: 系统状态获取成功 (处理时间: 0 ms)
04:06:16.638 > 📤 [API] 响应JSON: {"success":true,"data":{"uptime":107,"memory_usage":40.88224,"signal_count":0,"wifi_strength":0,"free_heap":204564,"chip_temperature":54.9,"timestamp":109214},"message":"系统状态获取成功","timestamp":1735689709215,"responseTime":0}
04:06:16.660 > 🚀 [API] 正在发送200状态码响应...
04:06:16.667 > 📤 [API] 200响应已发送到客户端
04:06:16.667 > 📝 GET /api/status - 200 (33 ms) [***********]
04:06:16.748 > [109329][E][vfs_api.cpp:105] open(): /littlefs/favicon.ico.gz does not exist, no permits for creation
04:06:16.760 > [109343][E][vfs_api.cpp:105] open(): /littlefs/favicon.ico does not exist, no permits for creation
04:06:16.773 > [109356][E][vfs_api.cpp:105] open(): /littlefs/favicon.ico/index.html.gz does not exist, no permits for creation
04:06:16.789 > [109371][E][vfs_api.cpp:105] open(): /littlefs/favicon.ico/index.html does not exist, no permits for creation
04:06:16.799 > 📝 GET /favicon.ico - 404 (0 ms) [***********]
04:06:16.945 > 📋 [API] 从系统配置读取硬件设置: 接收=14, 发射=21, 频率=38000
04:06:16.950 > 📤 [API] 发送成功响应: 用户配置获取成功 (处理时间: 1 ms)
04:06:16.955 > 📤 [API] 响应JSON: {"success":true,"data":{"ui_settings":{"theme":"light","language":"zh-CN","default_view":"grid","auto_refresh":true,"refresh_interval":30,"show_notifications":true,"notification_duration":5,"enable_sound":false,"compact_mode":false},"signal_settings":{"default_protocol":"NEC","default_frequency":38000,"learning_timeout":30,"emit_retry_count":3,"emit_retry_delay":100,"auto_save_learned":true,"save_dialog_timeout":15,"show_raw_data":false},"timer_settings":{"enabled":true,"default_interval":60,"max_concurrent_tasks":5,"task_timeout":300,"auto_cleanup_completed":true,"cleanup_after_days":7},"system_settings":{"debug_level":2,"log_retention_days":30,"max_log_size_mb":10,"enable_websocket":true,"websocket_heartbeat":30,"api_rate_limit":100,"enable_cors":true},"network_settings":{"wifi_auto_reconnect":true,"connection_timeout":10,"max_reconnect_attempts":5,"ap_mode_timeout":300},"hardware_settings":{"ir_recv_pin":14,"ir_send_pin":21,"ir_frequency":38000,"ir_duty_cycle":33,"status_led_pin":2,"status_led_enabled":true},"version":"1.0.0","last_modified":1735689709526,"created":1735689709526},"message":"用户配置获取成功","timestamp":1735689709537,"responseTime":1}
04:06:17.061 > 🚀 [API] 正在发送200状态码响应...
04:06:17.066 > 📤 [API] 200响应已发送到客户端
04:06:17.066 > ℹ️ [WS] 客户端连接 (客户端: 1)
04:06:17.072 > ℹ️ [WS] 发送连接事件 (客户端: 1)
04:06:26.458 > 📤 [API] 发送成功响应: 信号列表获取成功 (处理时间: 0 ms)
04:06:26.462 > 📤 [API] 响应JSON: {"success":true,"data":{"signals":[],"total":0},"message":"信号列表获取成功","timestamp":1735689719040,"responseTime":0}
04:06:26.475 > 🚀 [API] 正在发送200状态码响应...
04:06:26.480 > 📤 [API] 200响应已发送到客户端
04:06:26.485 > 📝 GET /api/signals - 200 (22 ms) [***********]
04:06:26.934 > 📝 GET /api/system/stats - 200 (339 ms) [***********]
04:06:26.934 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
04:06:26.940 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:06:31.842 > ⚠️ [WS] 未知消息类型: pong (客户端: 1)
04:06:42.180 > 🎯 [Learning API Body Handler] 被调用: len=61, index=0, total=61
04:06:42.186 > 📝 [Learning API] 请求体内容: {"command":"start","timeout":30000,"timestamp":1752955601239}
04:06:42.191 > ✅ [Learning API] 完整请求体已接收，开始处理
04:06:42.197 > 🔄 [Learning API] 执行开始学习命令
04:06:42.203 > 🎯 [IR学习] 开始学习请求: 超时=30000 ms, 信号ID=
04:06:42.209 > 📡 [IR学习] 当前引脚配置: 接收引脚=GPIO14, 发射引脚=GPIO21
04:06:42.213 > 🧹 [IR学习] 清空接收缓冲区: 1 个旧信号
04:06:42.219 > ✅ [IR学习] 学习模式已启动: ID=signal_134794_1, 超时=30000 ms
04:06:42.226 > ⏳ [IR学习] 等待信号中... (已等待: 9 ms / 30000 ms)
04:06:42.231 > ℹ️ [IR] 开始信号学习 (超时: 30000ms)
04:06:42.236 > 🔍 [Learning API] startLearning()返回: true
04:06:42.241 > ℹ️ [WS] 发送学习开始事件，超时: 30000ms
04:06:42.242 > ✅ [Learning API] 准备发送成功响应
04:06:42.247 > 📤 [API] 发送成功响应: 学习模式已启动 (处理时间: 69 ms)
04:06:42.252 > 📤 [API] 响应JSON: {"success":true,"data":{"timeout":30000,"status":"learning"},"message":"学习模式已启动","timestamp":1735689734832,"responseTime":69}
04:06:42.270 > 🚀 [API] 正在发送200状态码响应...
04:06:42.276 > 📤 [API] 200响应已发送到客户端
04:06:42.276 > ✅ [Learning API] 成功响应已发送
04:06:43.138 > 📝 GET /api/system/stats - 200 (343 ms) [***********]
04:06:43.138 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
04:06:43.144 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:06:43.427 > 📡 [IR学习] 检测到信号! 协议: GOODWEATHER, 数据: 0x552AC8000000, 位数: 48
04:06:43.509 > 🧠 [IR学习] 当前可用内存: 197572 bytes
04:06:43.510 > 📋 [IR学习] 开始创建信号数据结构
04:06:43.510 > ✅ [IR学习] 信号数据创建成功: 新学习信号
04:06:43.510 > ℹ️ [STORAGE] 信号已保存: 新学习信号 (signal_134794_1)
04:06:43.510 > 🎉 [IR学习] 信号学习完成: 新学习信号
04:06:43.510 > ℹ️ [IR] 信号学习完成: 新学习信号
04:06:43.510 > 🔍 [IR学习] WebSocket管理器状态: 可用
04:06:43.510 > 📡 [IR学习] 发送学习完成事件（标准格式）
04:06:43.510 > ℹ️ [WS] 发送自定义事件: learning_complete
04:06:43.510 > 📡 [IR学习] 已发送WebSocket学习完成事件
04:06:43.549 > 📝 GET /api/system/stats - 200 (338 ms) [***********]
04:06:43.932 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
04:06:43.965 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:06:44.411 > 📝 GET /api/system/stats - 200 (346 ms) [***********]
04:06:44.411 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
04:06:44.417 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:06:44.893 > 📝 GET /api/system/stats - 200 (342 ms) [***********]
04:06:44.893 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
04:06:44.914 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:06:46.165 > 📝 GET /api/system/stats - 200 (332 ms) [***********]
04:06:46.418 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
04:06:46.536 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:07:04.748 > 📤 [API] 发送错误响应: 信号不存在 (状态码: 404)
04:07:04.754 > 📤 [API] 错误响应JSON: {"success":false,"error":"信号不存在","timestamp":1735689757332,"responseTime":1}
04:07:04.759 > 📤 [API] 错误响应已发送到客户端
04:07:07.496 > 🎯 [Learning API Body Handler] 被调用: len=60, index=0, total=60
04:07:07.502 > 📝 [Learning API] 请求体内容: {"command":"stop","timeout":30000,"timestamp":1752955625038}
04:07:07.508 > ✅ [Learning API] 完整请求体已接收，开始处理
04:07:07.513 > 🔄 [Learning API] 执行停止学习命令
04:07:07.519 > 🔍 [Learning API] stopLearning()返回: true
04:07:07.525 > ✅ [Learning API] 准备发送成功响应
04:07:07.525 > 📤 [API] 发送成功响应: 学习模式已停止 (处理时间: 21 ms)
04:07:07.529 > 📤 [API] 响应JSON: {"success":true,"data":{"status":"stopped"},"message":"学习模式已停止","timestamp":1735689760112,"responseTime":21}
04:07:07.547 > 🚀 [API] 正在发送200状态码响应...
04:07:07.547 > 📤 [API] 200响应已发送到客户端
04:07:07.552 > ✅ [Learning API] 成功响应已发送
04:07:09.481 > 📝 GET /api/system/stats - 200 (335 ms) [***********]
04:07:09.481 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
04:07:09.486 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:07:10.812 > 📝 GET /api/system/stats - 200 (338 ms) [***********]
04:07:10.812 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
04:07:10.818 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:07:11.544 > 📝 GET /api/system/stats - 200 (339 ms) [***********]
04:07:11.673 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
04:07:11.779 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:07:12.606 > 📝 GET /api/system/stats - 200 (341 ms) [***********]
04:07:12.606 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
04:07:12.612 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:07:31.116 > ⚠️ [WS] 未知消息类型: pong (客户端: 1)
04:07:37.060 > 📤 [API] 处理信号发射请求: len=30, index=0, total=30
04:07:37.429 > 📤 [IR发射] 开始发射信号: signal_134794_1
04:07:37.429 > 🧠 [IR发射] 当前可用内存: 215688 bytes
04:07:37.435 > 🔍 [IR发射] 正在查找信号: signal_134794_1
04:07:37.441 > ✅ [IR发射] 找到信号: 新学习信号, 协议: RAW
04:07:37.446 > ℹ️ [IR] 发射信号: 新学习信号 (RAW)
04:07:37.452 > ℹ️ [IR] 发射RAW协议信号: 新学习信号
04:07:37.452 > 📡 [RAW发射] 发送RAW数据: 0x552AC8000000
04:07:37.558 > ✅ [RAW发射] RAW数据发送完成: 新学习信号
04:07:37.558 > ℹ️ [IR] 信号发射成功: 新学习信号
04:07:37.564 > 🎉 [IR发射] 信号发射成功: signal_134794_1
04:07:37.569 > ℹ️ [STORAGE] 信号已更新: 新学习信号 (signal_134794_1)
04:07:37.574 > 📤 [API] 发送成功响应: 信号发射成功 (处理时间: 512 ms)
04:07:37.580 > 📤 [API] 响应JSON: {"success":true,"data":{"signal_id":"signal_134794_1","signal_name":"新学习信号","sent_count":1,"last_sent":190155},"message":"信号发射成功","timestamp":1735689790166,"responseTime":512}
04:07:37.603 > 🚀 [API] 正在发送200状态码响应...
04:07:37.603 > 📤 [API] 200响应已发送到客户端
04:07:37.608 > 📝 POST /api/emit/signal - 200 (544 ms) [***********]
04:07:37.954 > 📝 GET /api/system/stats - 200 (337 ms) [***********]
04:07:37.954 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
04:07:37.959 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:07:38.617 > 📝 GET /api/system/stats - 200 (340 ms) [***********]
04:07:38.618 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
04:07:38.623 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:07:39.115 > 📝 GET /api/system/stats - 200 (335 ms) [***********]
04:07:39.115 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
04:07:39.121 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:07:39.806 > 📝 GET /api/system/stats - 200 (340 ms) [***********]
04:07:39.806 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
04:07:39.812 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:07:40.435 > 📝 GET /api/system/stats - 200 (340 ms) [***********]
04:07:40.435 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
04:07:40.515 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
04:07:41.545 > 📝 GET /api/system/stats - 200 (342 ms) [***********]
04:07:41.545 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
04:07:41.550