--- Terminal on COM6 | 115200 8-N-1
--- Available filters and text transformations: colorize, debug, default, direct, esp32_exception_decoder, hexlify, log2file, nocontrol, printable, send_on_enter, time
--- More details at https://bit.ly/pio-monitor-filters
--- Quit: Ctrl+C | Menu: Ctrl+T | Help: Ctrl+T followed by Ctrl+H
22:11:37.088 > ESP-ROM:esp32s3-20210327
22:11:37.095 > Build:Mar 27 2021
22:11:37.096 > rst:0x1 (POWERON),boot:0x8 (SPI_FAST_FLASH_BOOT)
22:11:37.102 > SPIWP:0xee
22:11:37.102 > mode:DIO, clock div:1
22:11:37.102 > load:0x3fce3808,len:0x4bc
22:11:37.189 > load:0x403c9700,len:0xbd8
22:11:37.189 > load:0x403cc700,len:0x2a0c
22:11:37.189 > entry 0x403c98d0
22:11:37.363 > E (272) esp_core_dump_flash: No core dump partition found!
22:11:37.363 > E (272) esp_core_dump_flash: No core dump partition found!
22:11:37.368 > [   274][I][esp32-hal-psram.c:96] psramInit(): PSRAM enabled
22:11:38.388 > [  1298][W][WiFiGeneric.cpp:1408] setTxPower(): Neither AP or STA has been started
22:11:38.394 >
22:11:38.394 > ==================================================
22:11:38.400 > 🚀 ESP32-S3红外控制系统启动
22:11:38.400 > 版本: 1.0.0
22:11:38.405 > 构建日期: Jul 19 2025 16:26:47
22:11:38.405 > 芯片型号: ESP32-S3
22:11:38.411 > 芯片版本: 0
22:11:38.411 > Flash大小: 16MB
22:11:38.411 > SRAM大小: 345KB
22:11:38.411 > CPU频率: 240MHz
22:11:38.416 > MAC地址: 24:EC:4A:38:B8:04
22:11:38.416 > ==================================================
22:11:38.422 >
22:11:38.422 > 🕐 初始化时间管理器...
22:11:38.428 > 🌐 NTP服务器: pool.ntp.org, time.nist.gov, time.cloudflare.com
22:11:38.433 > 🌍 时区: CST-8
22:11:38.433 > ✅ 时间管理器初始化完成，等待WiFi连接后同步NTP
22:11:38.439 > 🚀 ESP32-S3红外控制系统启动中...
22:11:39.440 > 🔧 初始化硬件...
22:11:39.440 > ✅ 硬件初始化完成 - CPU: 240MHz, Flash: 16MB
22:11:39.446 > 💾 初始化文件系统...
22:11:39.446 > ✅ LittleFS挂载成功
22:11:39.482 > 🧪 测试文件创建权限...
22:11:39.500 > ✅ 文件创建权限测试通过
22:11:39.662 > ✅ 文件系统初始化完成 - 总空间: 2031616 bytes, 已用: 778240 bytes
22:11:39.694 > [INFO] 2604 - 配置加载成功 (SystemManager::loadConfig)
22:11:39.699 > 🔌 初始化组件...
22:11:39.700 > 📶 初始化WiFi管理器...
22:11:39.700 > [  2604][W][WiFiGeneric.cpp:1408] setTxPower(): Neither AP or STA has been started
22:11:39.749 > 📶 WiFi STA 启动
22:11:39.781 > ✅ 加载了 0 个网络配置
22:11:39.781 > 📶 没有配置的网络，启动AP模式
22:11:39.787 > 📡 启动AP模式: ESP32-IR-Control
22:11:39.888 > 📡 WiFi AP 启动
22:11:40.354 > ✅ AP模式启动成功
22:11:40.354 > 📡 SSID: ESP32-IR-Control
22:11:40.354 > 📡 WiFi AP 启动
22:11:40.360 > 📍 IP地址: ***********
22:11:40.360 > 📻 信道: 1
22:11:40.360 > ✅ WiFi管理器初始化完成
22:11:40.365 > 💾 初始化信号存储管理器...
22:11:40.418 > ℹ️ [STORAGE] 已加载信号索引: 0 个信号
22:11:40.448 > ℹ️ [STORAGE] 已加载信号索引: 0 个信号
22:11:40.448 > 📦 开始加载信号缓存，索引中共有 0 个信号，限制加载 100 个
22:11:40.459 > ✅ 信号缓存加载完成，已加载 0 个信号
22:11:40.459 > ℹ️ [STORAGE] 限制性缓存加载成功，信号数量: 0
22:11:40.465 > ✅ 信号存储初始化完成 - 已加载 0 个信号
22:11:40.470 > 🔌 初始化WebSocket服务器 (端口: 8001)...
22:11:40.476 > ✅ WebSocket服务器启动成功: ws://***********:8001/ws
22:11:40.482 > 📋 从配置加载硬件设置: 发射引脚=21, 接收引脚=14, 频率=38000, 占空比=33
22:11:40.492 > 📡 初始化红外管理器...
22:11:40.492 > ✅ 红外管理器初始化完成
22:11:40.498 > 📡 发射引脚: GPIO21, 接收引脚: GPIO14
22:11:40.498 > 📶 频率: 38000 Hz, 占空比: 33%
22:11:40.504 > 🌐 初始化HTTP服务器 (端口: 8000)...
22:11:40.509 > 🛣️ 设置API路由...
22:11:40.509 > 🔧 注册路由: POST /api/learning
22:11:40.515 > ✅ 路由已注册: POST /api/learning
22:11:40.515 > 🔧 注册路由: POST /api/learning/detect
22:11:40.520 > ✅ 路由已注册: POST /api/learning/detect
22:11:40.526 > 🔧 注册路由: POST /api/emit/signal
22:11:40.532 > ✅ 路由已注册: POST /api/emit/signal
22:11:40.532 > ✅ API路由设置完成
22:11:40.546 > 📁 静态文件服务已启用 (根目录) - 调试模式
22:11:40.548 > ✅ HTTP服务器启动成功: http://0.0.0.0:8000
22:11:40.552 > 📊 初始化系统监控器...
22:11:40.719 > ✅ 系统监控器初始化完成
22:11:40.719 > 🕐 初始化定时器管理器...
22:11:40.801 > [  3711][E][vfs_api.cpp:105] open(): /littlefs/timer_tasks.json does not exist, no permits for creation
22:11:40.807 > 📂 TimerManagerBackend: 任务文件不存在，使用默认配置
22:11:40.813 > ✅ 定时器管理器初始化完成，加载了 0 个任务
22:11:40.817 > ✅ 组件初始化完成
22:11:40.823 > ⚙️ 创建系统任务...
22:11:40.823 > ✅ 系统任务创建完成
22:11:40.828 > 🔍 执行系统自检...
22:11:40.833 > ⚠️ WiFi未连接，但系统可以继续运行
22:11:40.833 > ✅ 系统自检通过
22:11:40.838 > ✅ ESP32-S3红外控制系统启动完成
22:11:40.838 > 🌐 HTTP服务器 (AP模式): http://***********:8000
22:11:40.844 > 🔌 WebSocket服务器 (AP模式): ws://***********:8001/ws
22:11:40.850 > 💾 可用内存: 225972 bytes
22:11:40.856 > 🔥 芯片温度: 54.1°C
22:11:40.856 > ⚠️ WiFi未连接，无法同步NTP时间
22:11:40.861 > 🎉 系统初始化完成，进入主循环
22:11:55.210 > 📡 客户端连接到AP
22:12:01.942 > [ 24853][E][vfs_api.cpp:105] open(): /littlefs/index.html.gz does not exist, no permits for creation
22:12:02.081 > [ 24992][E][vfs_api.cpp:105] open(): /littlefs/css/main.css.gz does not exist, no permits for creation
22:12:02.172 > [ 25084][E][vfs_api.cpp:105] open(): /littlefs/css/modules.css.gz does not exist, no permits for creation
22:12:04.087 > [ 26998][E][vfs_api.cpp:105] open(): /littlefs/js/utils.js.gz does not exist, no permits for creation
22:12:04.164 > [ 27076][E][vfs_api.cpp:105] open(): /littlefs/js/data-validator.js.gz does not exist, no permits for creation
22:12:04.279 > [ 27190][E][vfs_api.cpp:105] open(): /littlefs/js/core.js.gz does not exist, no permits for creation
22:12:04.403 > [ 27315][E][vfs_api.cpp:105] open(): /littlefs/js/dom-update-manager.js.gz does not exist, no permits for creation
22:12:04.824 > [ 27735][E][vfs_api.cpp:105] open(): /littlefs/js/optimized-signal-storage.js.gz does not exist, no permits for creation
22:12:05.929 > [ 28840][E][vfs_api.cpp:105] open(): /littlefs/js/unified-timer-manager.js.gz does not exist, no permits for creation
22:12:06.318 > [ 29230][E][vfs_api.cpp:105] open(): /littlefs/js/virtual-scroll-list.js.gz does not exist, no permits for creation
22:12:06.396 > [ 29307][E][vfs_api.cpp:105] open(): /littlefs/js/signal-virtual-list.js.gz does not exist, no permits for creation
22:12:06.579 > [ 29490][E][vfs_api.cpp:105] open(): /littlefs/js/signal-manager.js.gz does not exist, no permits for creation
22:12:07.547 > [ 30458][E][vfs_api.cpp:105] open(): /littlefs/js/control-module.js.gz does not exist, no permits for creation
22:12:07.893 > [ 30804][E][vfs_api.cpp:105] open(): /littlefs/js/timer-settings.js.gz does not exist, no permits for creation
22:12:08.115 > [ 31026][E][vfs_api.cpp:105] open(): /littlefs/js/status-display.js.gz does not exist, no permits for creation
22:12:09.162 > [ 32073][E][vfs_api.cpp:105] open(): /littlefs/js/system-monitor.js.gz does not exist, no permits for creation
22:12:14.325 > [ 37236][E][vfs_api.cpp:105] open(): /littlefs/js/main.js.gz does not exist, no permits for creation
22:12:21.946 > 📤 [API] 发送成功响应: 系统状态获取成功 (处理时间: 0 ms)
22:12:21.952 > 📤 [API] 响应JSON: {"success":true,"data":{"uptime":43,"memory_usage":36.06406,"signal_count":0,"wifi_strength":0,"free_heap":221492,"chip_temperature":58.1,"timestamp":44858},"message":"系统状态获取成功","timestamp":1735689644859,"responseTime":0}
22:12:21.974 > 🚀 [API] 正在发送200状态码响应...
22:12:21.980 > 📤 [API] 200响应已发送到客户端
22:12:21.980 > 📝 GET /api/status - 200 (32 ms) [***********]
22:12:22.136 > 📋 [API] 从系统配置读取硬件设置: 接收=14, 发射=21, 频率=38000
22:12:22.141 > 📤 [API] 发送成功响应: 用户配置获取成功 (处理时间: 0 ms)
22:12:22.146 > 📤 [API] 响应JSON: {"success":true,"data":{"ui_settings":{"theme":"light","language":"zh-CN","default_view":"grid","auto_refresh":true,"refresh_interval":30,"show_notifications":true,"notification_duration":5,"enable_sound":false,"compact_mode":false},"signal_settings":{"default_protocol":"NEC","default_frequency":38000,"learning_timeout":30,"emit_retry_count":3,"emit_retry_delay":100,"auto_save_learned":true,"save_dialog_timeout":15,"show_raw_data":false},"timer_settings":{"enabled":true,"default_interval":60,"max_concurrent_tasks":5,"task_timeout":300,"auto_cleanup_completed":true,"cleanup_after_days":7},"system_settings":{"debug_level":2,"log_retention_days":30,"max_log_size_mb":10,"enable_websocket":true,"websocket_heartbeat":30,"api_rate_limit":100,"enable_cors":true},"network_settings":{"wifi_auto_reconnect":true,"connection_timeout":10,"max_reconnect_attempts":5,"ap_mode_timeout":300},"hardware_settings":{"ir_recv_pin":14,"ir_send_pin":21,"ir_frequency":38000,"ir_duty_cycle":33,"status_led_pin":2,"status_led_enabled":true},"version":"1.0.0","last_modified":1735689645047,"created":1735689645047},"message":"用户配置获取成功","timestamp":1735689645058,"responseTime":0}
22:12:22.253 > 🚀 [API] 正在发送200状态码响应...
22:12:22.258 > 📤 [API] 200响应已发送到客户端
22:12:22.258 > ℹ️ [WS] 客户端连接 (客户端: 1)
22:12:22.263 > ℹ️ [WS] 发送连接事件 (客户端: 1)
22:12:22.385 > 📤 [API] 发送成功响应: 信号列表获取成功 (处理时间: 0 ms)
22:12:22.390 > 📤 [API] 响应JSON: {"success":true,"data":{"signals":[],"total":0},"message":"信号列表获取成功","timestamp":1735689645297,"responseTime":0}
22:12:22.402 > 🚀 [API] 正在发送200状态码响应...
22:12:22.407 > 📤 [API] 200响应已发送到客户端
22:12:22.411 > 📝 GET /api/signals - 200 (22 ms) [***********]
22:12:22.707 > 📝 GET /api/system/stats - 200 (297 ms) [***********]
22:12:22.708 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
22:12:22.713 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
22:12:22.719 > 📤 [API] 发送成功响应: 信号列表获取成功 (处理时间: 0 ms)
22:12:22.724 > 📤 [API] 响应JSON: {"success":true,"data":{"signals":[],"total":0},"message":"信号列表获取成功","timestamp":1735689645633,"responseTime":0}
22:12:22.741 > 🚀 [API] 正在发送200状态码响应...
22:12:22.746 > 📤 [API] 200响应已发送到客户端
22:12:22.746 > 📝 GET /api/signals - 200 (22 ms) [***********]
22:12:22.754 > [HTTP] GET /api/timer/tasks - 200 (0ms)
22:12:23.173 > 📝 GET /api/system/stats - 200 (297 ms) [***********]
22:12:23.173 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
22:12:23.178 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
22:12:40.501 > ⚠️ [WS] 未知消息类型: pong (客户端: 1)
22:12:48.299 > 🎯 [Learning API Body Handler] 被调用: len=61, index=0, total=61
22:12:48.306 > 📝 [Learning API] 请求体内容: {"command":"start","timeout":30000,"timestamp":1752934368211}
22:12:48.311 > ✅ [Learning API] 完整请求体已接收，开始处理
22:12:48.316 > 🔄 [Learning API] 执行开始学习命令
22:12:48.321 > 🎯 [IR学习] 开始学习请求: 超时=30000 ms, 信号ID=
22:12:48.328 > 📡 [IR学习] 当前引脚配置: 接收引脚=GPIO14, 发射引脚=GPIO21
22:12:48.333 > 🧹 [IR学习] 清空接收缓冲区: 1 个旧信号
22:12:48.339 > ✅ [IR学习] 学习模式已启动: ID=signal_71244_1, 超时=30000 ms
22:12:48.344 > ⏳ [IR学习] 等待信号中... (已等待: 8 ms / 30000 ms)
22:12:48.350 > ℹ️ [IR] 开始信号学习 (超时: 30000ms)
22:12:48.356 > 🔍 [Learning API] startLearning()返回: true
22:12:48.357 > ℹ️ [WS] 发送学习开始事件，超时: 30000ms
22:12:48.363 > ✅ [Learning API] 准备发送成功响应
22:12:48.368 > 📤 [API] 发送成功响应: 学习模式已启动 (处理时间: 68 ms)
22:12:48.373 > 📤 [API] 响应JSON: {"success":true,"data":{"timeout":30000,"status":"learning"},"message":"学习模式已启动","timestamp":1735689671282,"responseTime":68}
22:12:48.390 > 🚀 [API] 正在发送200状态码响应...
22:12:48.390 > 📤 [API] 200响应已发送到客户端
22:12:48.396 > ✅ [Learning API] 成功响应已发送
22:12:48.868 > 📝 GET /api/system/stats - 200 (294 ms) [***********]
22:12:48.870 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
22:12:48.875 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
22:12:49.272 > 📝 GET /api/system/stats - 200 (292 ms) [***********]
22:12:49.272 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
22:12:49.277 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
22:12:49.776 > 📝 GET /api/system/stats - 200 (293 ms) [***********]
22:12:49.777 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
22:12:49.782 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
22:12:50.409 > 📡 [IR学习] 检测到信号! 协议: GOODWEATHER, 数据: 0x552A0A000000, 位数: 48
22:12:50.460 > 🧠 [IR学习] 当前可用内存: 220216 bytes
22:12:50.462 > 📋 [IR学习] 开始创建信号数据结构
22:12:50.462 > ✅ [IR学习] 信号数据创建成功: 新学习信号
22:12:50.462 > ℹ️ [STORAGE] 信号已保存: 新学习信号 (signal_71244_1)
22:12:50.462 > 🎉 [IR学习] 信号学习完成: 新学习信号
22:12:50.462 > ℹ️ [IR] 信号学习完成: 新学习信号
22:12:50.462 > 🔍 [IR学习] WebSocket管理器状态: 可用
22:12:50.462 > 📡 [IR学习] 准备发送WebSocket消息: {"type":"learning_complete","signal":{"id":"signal_71244_1","name":"新学习信号","signalCode":"0x552aa000000","protocol":"RAW","frequency":"38000","type":"other","description":"学习的红外信号","isLearned":true,"sentCount":0,"lastSent":0,"created":73322,"data":"0x552aa000000"},"auto_save_timeout":15}
22:12:50.495 > 📡 [IR学习] 已发送WebSocket学习完成事件
22:12:50.829 > 📝 GET /api/system/stats - 200 (299 ms) [***********]
22:12:50.829 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
22:12:50.835 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
22:13:08.614 > 🎯 [Learning API Body Handler] 被调用: len=60, index=0, total=60
22:13:08.619 > 📝 [Learning API] 请求体内容: {"command":"stop","timeout":30000,"timestamp":1752934388530}
22:13:08.625 > ✅ [Learning API] 完整请求体已接收，开始处理
22:13:08.630 > 🔄 [Learning API] 执行停止学习命令
22:13:08.636 > 🔍 [Learning API] stopLearning()返回: true
22:13:08.642 > ✅ [Learning API] 准备发送成功响应
22:13:08.642 > 📤 [API] 发送成功响应: 学习模式已停止 (处理时间: 22 ms)
22:13:08.647 > 📤 [API] 响应JSON: {"success":true,"data":{"status":"stopped"},"message":"学习模式已停止","timestamp":1735689691559,"responseTime":22}
22:13:08.664 > 🚀 [API] 正在发送200状态码响应...
22:13:08.664 > 📤 [API] 200响应已发送到客户端
22:13:08.670 > ✅ [Learning API] 成功响应已发送
22:13:09.114 > 📝 GET /api/system/stats - 200 (288 ms) [***********]
22:13:09.114 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
22:13:09.119 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
22:13:09.533 > 📝 GET /api/system/stats - 200 (290 ms) [***********]
22:13:09.533 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
22:13:09.538 > 📝 GET /api/system/performance - 200 (3 ms) [***********]
22:13:09.917 > 📝 GET /api/system/stats - 200 (294 ms) [***********]
22:13:09.917 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
22:13:09.922 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
22:13:17.062 > [ 99975][E][vfs_api.cpp:105] open(): /littlefs/index.html.gz does not exist, no permits for creation
22:13:17.105 > 🎯 [Learning API Body Handler] 被调用: len=60, index=0, total=60
22:13:17.110 > 📝 [Learning API] 请求体内容: {"command":"stop","timeout":30000,"timestamp":1752934396974}
22:13:17.117 > ✅ [Learning API] 完整请求体已接收，开始处理
22:13:17.122 > 🔄 [Learning API] 执行停止学习命令
22:13:17.127 > 🔍 [Learning API] stopLearning()返回: true
22:13:17.133 > ✅ [Learning API] 准备发送成功响应
22:13:17.133 > 📤 [API] 发送成功响应: 学习模式已停止 (处理时间: 21 ms)
22:13:17.139 > 📤 [API] 响应JSON: {"success":true,"data":{"status":"stopped"},"message":"学习模式已停止","timestamp":1735689700051,"responseTime":21}
22:13:17.155 > 🚀 [API] 正在发送200状态码响应...
22:13:17.155 > 📤 [API] 200响应已发送到客户端
22:13:17.161 > ✅ [Learning API] 成功响应已发送
22:13:17.218 > ℹ️ [WS] 客户端断开 (客户端: 1)
22:13:17.535 > 📤 [API] 发送成功响应: 系统状态获取成功 (处理时间: 0 ms)
22:13:17.540 > 📤 [API] 响应JSON: {"success":true,"data":{"uptime":99,"memory_usage":36.2313,"signal_count":1,"wifi_strength":0,"free_heap":220780,"chip_temperature":57.1,"timestamp":100448},"message":"系统状态获取成功","timestamp":1735689700449,"responseTime":0}
22:13:17.562 > 🚀 [API] 正在发送200状态码响应...
22:13:17.568 > 📤 [API] 200响应已发送到客户端
22:13:17.568 > 📝 GET /api/status - 200 (32 ms) [***********]
22:13:17.957 > ℹ️ [WS] 客户端连接 (客户端: 2)
22:13:17.958 > ℹ️ [WS] 发送连接事件 (客户端: 2)
22:13:18.008 > 📋 [API] 从系统配置读取硬件设置: 接收=14, 发射=21, 频率=38000
22:13:18.015 > 📤 [API] 发送成功响应: 用户配置获取成功 (处理时间: 1 ms)
22:13:18.020 > 📤 [API] 响应JSON: {"success":true,"data":{"ui_settings":{"theme":"light","language":"zh-CN","default_view":"grid","auto_refresh":true,"refresh_interval":30,"show_notifications":true,"notification_duration":5,"enable_sound":false,"compact_mode":false},"signal_settings":{"default_protocol":"NEC","default_frequency":38000,"learning_timeout":30,"emit_retry_count":3,"emit_retry_delay":100,"auto_save_learned":true,"save_dialog_timeout":15,"show_raw_data":false},"timer_settings":{"enabled":true,"default_interval":60,"max_concurrent_tasks":5,"task_timeout":300,"auto_cleanup_completed":true,"cleanup_after_days":7},"system_settings":{"debug_level":2,"log_retention_days":30,"max_log_size_mb":10,"enable_websocket":true,"websocket_heartbeat":30,"api_rate_limit":100,"enable_cors":true},"network_settings":{"wifi_auto_reconnect":true,"connection_timeout":10,"max_reconnect_attempts":5,"ap_mode_timeout":300},"hardware_settings":{"ir_recv_pin":14,"ir_send_pin":21,"ir_frequency":38000,"ir_duty_cycle":33,"status_led_pin":2,"status_led_enabled":true},"version":"1.0.0","last_modified":1735689700923,"created":1735689700923},"message":"用户配置获取成功","timestamp":1735689700934,"responseTime":1}
22:13:18.125 > 🚀 [API] 正在发送200状态码响应...
22:13:18.130 > 📤 [API] 200响应已发送到客户端
22:13:19.019 > 📤 [API] 发送成功响应: 信号列表获取成功 (处理时间: 608 ms)
22:13:19.026 > 📤 [API] 响应JSON: {"success":true,"data":{"signals":[{"id":"signal_71244_1","name":"新学习信号","type":"other","description":"学习的红外信号","signalCode":"0x552aa000000","protocol":"RAW","frequency":"38000","data":"0x552aa000000","isLearned":true,"created":73322,"lastSent":0,"sentCount":0}],"total":1},"message":"信号列表获取成功","timestamp":1735689701934,"responseTime":608}
22:13:19.089 > 🚀 [API] 正在发送200状态码响应...
22:13:19.091 > 📤 [API] 200响应已发送到客户端
22:13:19.091 > 📝 GET /api/signals - 200 (650 ms) [***********]
22:13:19.362 > 📝 GET /api/system/stats - 200 (286 ms) [***********]
22:13:19.362 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
22:13:19.366 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
22:13:27.783 > 📤 [API] 处理信号发射请求: len=29, index=0, total=29
22:13:28.282 > 📤 [API] 发送错误响应: 信号不存在 (状态码: 404)
22:13:28.288 > 📤 [API] 错误响应JSON: {"success":false,"error":"信号不存在","timestamp":1735689711198,"responseTime":1}
22:13:28.295 > 📤 [API] 错误响应已发送到客户端
22:13:28.581 > 📝 GET /api/system/stats - 200 (283 ms) [***********]
22:13:28.581 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
22:13:28.591 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
22:13:30.897 > 📝 GET /api/system/stats - 200 (292 ms) [***********]
22:13:30.898 > 📝 GET /api/system/memory - 200 (2 ms) [***********]
22:13:30.903 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
22:13:31.329 > 📝 GET /api/system/stats - 200 (300 ms) [***********]
22:13:31.329 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
22:13:31.334 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
22:13:31.743 > 📝 GET /api/system/stats - 200 (293 ms) [***********]
22:13:31.749 > 📝 GET /api/system/performance - 200 (2 ms) [***********]
22:13:31.749 > 📝 GET /api/system/memory - 200 (1 ms) [***********]
22:13:40.538 > ⚠️ [WS] 未知消息类型: pong (客户端: 2)
