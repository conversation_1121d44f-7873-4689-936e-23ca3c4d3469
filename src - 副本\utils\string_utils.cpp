/**
 * @file string_utils.cpp
 * @brief 字符串工具函数实现
 * @details 提供常用的字符串处理功能
 * @version 1.0.0
 * @date 2025-01-07
 */

#include "string_utils.h"
#include <ArduinoJson.h>
#include <cstdarg>

namespace StringUtils {
    
    std::vector<String> split(const String& str, char delimiter) {
        std::vector<String> result;
        int start = 0;
        int end = str.indexOf(delimiter);
        
        while (end != -1) {
            result.push_back(str.substring(start, end));
            start = end + 1;
            end = str.indexOf(delimiter, start);
        }
        
        result.push_back(str.substring(start));
        return result;
    }
    
    String trim(const String& str) {
        int start = 0;
        int end = str.length() - 1;
        
        // 找到第一个非空白字符
        while (start <= end && isspace(str.charAt(start))) {
            start++;
        }
        
        // 找到最后一个非空白字符
        while (end >= start && isspace(str.charAt(end))) {
            end--;
        }
        
        return str.substring(start, end + 1);
    }
    
    String toLowerCase(const String& str) {
        String result = str;
        result.toLowerCase();
        return result;
    }
    
    String toUpperCase(const String& str) {
        String result = str;
        result.toUpperCase();
        return result;
    }
    
    bool isBlank(const String& str) {
        return trim(str).length() == 0;
    }
    
    String replace(const String& str, const String& from, const String& to) {
        String result = str;
        int index = 0;
        
        while ((index = result.indexOf(from, index)) != -1) {
            result = result.substring(0, index) + to + result.substring(index + from.length());
            index += to.length();
        }
        
        return result;
    }
    
    String format(const char* format, ...) {
        va_list args;
        va_start(args, format);
        
        char buffer[512];
        vsnprintf(buffer, sizeof(buffer), format, args);
        
        va_end(args);
        return String(buffer);
    }
    
    String generateRandom(int length, bool includeNumbers, bool includeSymbols) {
        String chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        
        if (includeNumbers) {
            chars += "0123456789";
        }
        
        if (includeSymbols) {
            chars += "!@#$%^&*()_+-=[]{}|;:,.<>?";
        }
        
        String result = "";
        for (int i = 0; i < length; i++) {
            result += chars.charAt(random(chars.length()));
        }
        
        return result;
    }
    
    uint32_t hash(const String& str) {
        uint32_t hash = 5381;
        
        for (int i = 0; i < str.length(); i++) {
            hash = ((hash << 5) + hash) + str.charAt(i);
        }
        
        return hash;
    }
    
    bool isValidJson(const String& str) {
        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, str);
        return error == DeserializationError::Ok;
    }
    
    String escapeJson(const String& str) {
        String result = "";
        
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            
            switch (c) {
                case '"':
                    result += "\\\"";
                    break;
                case '\\':
                    result += "\\\\";
                    break;
                case '\b':
                    result += "\\b";
                    break;
                case '\f':
                    result += "\\f";
                    break;
                case '\n':
                    result += "\\n";
                    break;
                case '\r':
                    result += "\\r";
                    break;
                case '\t':
                    result += "\\t";
                    break;
                default:
                    if (c < 0x20) {
                        char buffer[8];
                        sprintf(buffer, "\\u%04x", c);
                        result += buffer;
                    } else {
                        result += c;
                    }
                    break;
            }
        }
        
        return result;
    }
    
    String unescapeJson(const String& str) {
        String result = "";
        
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            
            if (c == '\\' && i + 1 < str.length()) {
                char next = str.charAt(i + 1);
                
                switch (next) {
                    case '"':
                        result += '"';
                        i++;
                        break;
                    case '\\':
                        result += '\\';
                        i++;
                        break;
                    case 'b':
                        result += '\b';
                        i++;
                        break;
                    case 'f':
                        result += '\f';
                        i++;
                        break;
                    case 'n':
                        result += '\n';
                        i++;
                        break;
                    case 'r':
                        result += '\r';
                        i++;
                        break;
                    case 't':
                        result += '\t';
                        i++;
                        break;
                    case 'u':
                        if (i + 5 < str.length()) {
                            String hex = str.substring(i + 2, i + 6);
                            char unicode = (char)strtol(hex.c_str(), NULL, 16);
                            result += unicode;
                            i += 5;
                        } else {
                            result += c;
                        }
                        break;
                    default:
                        result += c;
                        break;
                }
            } else {
                result += c;
            }
        }
        
        return result;
    }
}
