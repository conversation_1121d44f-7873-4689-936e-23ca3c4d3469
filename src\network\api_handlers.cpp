#include "api_handlers.h"
#include "core/system_manager.h"
#include "core/time_manager.h"
#include "core/task_scheduler.h"
#include "core/timer_manager_backend.h"
#include "ir/ir_manager.h"
#include "storage/signal_storage.h"
#include "storage/timer_data.h"
#include "network/websocket_manager.h"

// ==================== 信号发射API ====================

// ==================== 信号管理API ====================

void APIHandlers::handleCreateSignal(AsyncWebServerRequest *request, uint8_t *data,
                                    size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (index + len == total) {
        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, (char*)data);

        if (error) {
            sendErrorResponse(request, "JSON解析失败", 400);
            return;
        }

        JsonObject signal = doc.as<JsonObject>();

        // 验证信号数据
        if (!validateSignalData(signal)) {
            sendErrorResponse(request, "信号数据格式无效", 400);
            return;
        }

        // 获取信号存储管理器
        SignalStorage* storage = getSignalStorage();
        if (!storage) {
            sendErrorResponse(request, "信号存储系统未初始化", 500);
            return;
        }

        // 创建信号数据结构
        SignalData signalData;
        signalData.id = signal["id"].as<String>();
        signalData.name = signal["name"].as<String>();
        signalData.type = signal["type"].as<String>();
        signalData.protocol = signal["protocol"].as<String>();
        signalData.data = signal["data"].as<String>();
        signalData.signalCode = signal["signalCode"].as<String>();
        signalData.frequency = signal["frequency"].as<String>();
        signalData.description = signal["description"].as<String>();
        signalData.isLearned = signal["isLearned"].as<bool>() || true;
        signalData.created = getCurrentUnixMs();
        signalData.lastSent = 0;
        signalData.sentCount = 0;

        // 保存信号
        if (storage->saveSignal(signalData)) {
            JsonDocument responseData;
            responseData["signalId"] = signalData.id;
            responseData["name"] = signalData.name;

            sendSuccessResponse(request, responseData, "信号创建成功", startTime);

            // 通知WebSocket客户端
            SystemManager* systemManager = getSystemManager();
            if (systemManager) {
                JsonDocument eventData;
                eventData["signalId"] = signalData.id;
                eventData["name"] = signalData.name;
                systemManager->sendSystemEvent("signal_created", eventData);
            }
        } else {
            sendErrorResponse(request, "信号保存失败", 500);
        }
    }
}

void APIHandlers::handleUpdateSignal(AsyncWebServerRequest *request, uint8_t *data,
                                    size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (index + len == total) {
        // 获取信号ID
        String signalId = request->pathArg(0);
        if (signalId.isEmpty()) {
            sendErrorResponse(request, "信号ID不能为空", 400);
            return;
        }

        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, (char*)data);

        if (error) {
            sendErrorResponse(request, "JSON解析失败", 400);
            return;
        }

        JsonObject updateData = doc.as<JsonObject>();

        // 获取信号存储管理器
        SignalStorage* storage = getSignalStorage();
        if (!storage) {
            sendErrorResponse(request, "信号存储系统未初始化", 500);
            return;
        }

        // 获取现有信号
        SignalData existingSignal = storage->getSignal(signalId);
        if (existingSignal.id.isEmpty()) {
            sendErrorResponse(request, "信号不存在", 404);
            return;
        }

        // 更新信号数据
        if (updateData["name"].is<const char*>()) {
            existingSignal.name = updateData["name"].as<String>();
        }
        if (updateData["type"].is<const char*>()) {
            existingSignal.type = updateData["type"].as<String>();
        }
        if (updateData["description"].is<const char*>()) {
            existingSignal.description = updateData["description"].as<String>();
        }
        if (updateData["protocol"].is<const char*>()) {
            existingSignal.protocol = updateData["protocol"].as<String>();
        }
        if (updateData["data"].is<const char*>()) {
            existingSignal.data = updateData["data"].as<String>();
        }
        if (updateData["signalCode"].is<const char*>()) {
            existingSignal.signalCode = updateData["signalCode"].as<String>();
        }
        if (updateData["frequency"].is<int>()) {
            existingSignal.frequency = updateData["frequency"].as<String>();
        }

        // 保存更新后的信号
        if (storage->saveSignal(existingSignal)) {
            JsonDocument responseData;
            responseData["signalId"] = existingSignal.id;
            responseData["name"] = existingSignal.name;

            sendSuccessResponse(request, responseData, "信号更新成功", startTime);

            // 通知WebSocket客户端
            SystemManager* systemManager = getSystemManager();
            if (systemManager) {
                JsonDocument eventData;
                eventData["signalId"] = existingSignal.id;
                eventData["name"] = existingSignal.name;
                systemManager->sendSystemEvent("signal_updated", eventData);
            }
        } else {
            sendErrorResponse(request, "信号更新失败", 500);
        }
    }
}

void APIHandlers::handleDeleteSignal(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    // 获取信号ID
    String signalId = request->pathArg(0);
    if (signalId.isEmpty()) {
        sendErrorResponse(request, "信号ID不能为空", 400);
        return;
    }

    // 获取信号存储管理器
    SignalStorage* storage = getSignalStorage();
    if (!storage) {
        sendErrorResponse(request, "信号存储系统未初始化", 500);
        return;
    }

    // 检查信号是否存在
    SignalData existingSignal = storage->getSignal(signalId);
    if (existingSignal.id.isEmpty()) {
        sendErrorResponse(request, "信号不存在", 404);
        return;
    }

    // 删除信号
    if (storage->deleteSignal(signalId)) {
        JsonDocument responseData;
        responseData["signalId"] = signalId;
        responseData["name"] = existingSignal.name;

        sendSuccessResponse(request, responseData, "信号删除成功", startTime);

        // 通知WebSocket客户端
        SystemManager* systemManager = getSystemManager();
        if (systemManager) {
            JsonDocument eventData;
            eventData["signalId"] = signalId;
            eventData["name"] = existingSignal.name;
            systemManager->sendSystemEvent("signal_deleted", eventData);
        }
    } else {
        sendErrorResponse(request, "信号删除失败", 500);
    }
}

void APIHandlers::handleExportSignals(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    // 获取信号存储管理器
    SignalStorage* storage = getSignalStorage();
    if (!storage) {
        sendErrorResponse(request, "信号存储系统未初始化", 500);
        return;
    }

    // 获取查询参数
    String format = "json";  // 默认JSON格式
    String signalIds = "";   // 空表示导出所有信号

    if (request->hasParam("format")) {
        format = request->getParam("format")->value();
    }

    if (request->hasParam("signal_ids")) {
        signalIds = request->getParam("signal_ids")->value();
    }

    // 创建导出数据
    JsonDocument exportDoc;
    exportDoc["version"] = "1.0";
    exportDoc["export_time"] = getCurrentUnixMs();
    exportDoc["format"] = format;

    JsonArray signalsArray = exportDoc["signals"].to<JsonArray>();

    if (signalIds.isEmpty()) {
        // 导出所有信号
        std::vector<SignalData> allSignals = storage->getAllSignals();
        for (const SignalData& signal : allSignals) {
            JsonObject signalObj = signalsArray.add<JsonObject>();
            signalObj["id"] = signal.id;
            signalObj["name"] = signal.name;
            signalObj["type"] = signal.type;
            signalObj["protocol"] = signal.protocol;
            signalObj["data"] = signal.data;
            signalObj["signalCode"] = signal.signalCode;
            signalObj["frequency"] = signal.frequency;
            signalObj["description"] = signal.description;
            signalObj["isLearned"] = signal.isLearned;
            signalObj["created"] = signal.created;
            signalObj["lastSent"] = signal.lastSent;
            signalObj["sentCount"] = signal.sentCount;
        }
    } else {
        // 导出指定信号
        // 解析信号ID列表 (逗号分隔)
        int startIndex = 0;
        int commaIndex = signalIds.indexOf(',');

        while (startIndex < signalIds.length()) {
            String signalId;
            if (commaIndex == -1) {
                signalId = signalIds.substring(startIndex);
                startIndex = signalIds.length();
            } else {
                signalId = signalIds.substring(startIndex, commaIndex);
                startIndex = commaIndex + 1;
                commaIndex = signalIds.indexOf(',', startIndex);
            }

            signalId.trim();
            if (!signalId.isEmpty()) {
                SignalData signal = storage->getSignal(signalId);
                if (!signal.id.isEmpty()) {
                    JsonObject signalObj = signalsArray.add<JsonObject>();
                    signalObj["id"] = signal.id;
                    signalObj["name"] = signal.name;
                    signalObj["type"] = signal.type;
                    signalObj["protocol"] = signal.protocol;
                    signalObj["data"] = signal.data;
                    signalObj["signalCode"] = signal.signalCode;
                    signalObj["frequency"] = signal.frequency;
                    signalObj["description"] = signal.description;
                    signalObj["isLearned"] = signal.isLearned;
                    signalObj["created"] = signal.created;
                    signalObj["lastSent"] = signal.lastSent;
                    signalObj["sentCount"] = signal.sentCount;
                }
            }
        }
    }

    // 生成文件名
    String timestamp = String(getCurrentUnixMs());
    String filename = "signals_export_" + timestamp + ".json";

    // 序列化JSON
    String jsonString;
    serializeJsonPretty(exportDoc, jsonString);

    // 设置响应头
    AsyncWebServerResponse *response = request->beginResponse(200, "application/json", jsonString);
    response->addHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
    response->addHeader("Access-Control-Allow-Origin", "*");

    request->send(response);

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);

    // 记录导出日志
    Serial.printf("✅ 信号导出完成: %d个信号, 文件: %s (%d ms)\n",
                  signalsArray.size(), filename.c_str(), processingTime);
}

void APIHandlers::handleImportSignals(AsyncWebServerRequest *request, uint8_t *data,
                                     size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (index + len == total) {
        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, (char*)data);

        if (error) {
            sendErrorResponse(request, "JSON解析失败: " + String(error.c_str()), 400);
            return;
        }

        // 验证导入数据格式
        if (!doc["signals"].is<JsonArray>()) {
            sendErrorResponse(request, "导入数据格式错误：缺少signals数组", 400);
            return;
        }

        JsonArray signalsArray = doc["signals"].as<JsonArray>();
        if (signalsArray.size() == 0) {
            sendErrorResponse(request, "导入数据为空", 400);
            return;
        }

        // 获取信号存储管理器
        SignalStorage* storage = getSignalStorage();
        if (!storage) {
            sendErrorResponse(request, "信号存储系统未初始化", 500);
            return;
        }

        // 导入统计
        int successCount = 0;
        int failureCount = 0;
        int duplicateCount = 0;
        JsonArray importResults = JsonDocument().to<JsonArray>();

        // 处理每个信号
        for (JsonVariant signalVariant : signalsArray) {
            JsonObject signalObj = signalVariant.as<JsonObject>();

            // 验证信号数据
            if (!validateSignalData(signalObj)) {
                failureCount++;
                JsonObject result = importResults.add<JsonObject>();
                result["id"] = signalObj["id"].as<String>();
                result["name"] = signalObj["name"].as<String>();
                result["status"] = "failed";
                result["reason"] = "数据格式验证失败";
                continue;
            }

            String signalId = signalObj["id"].as<String>();

            // 检查是否已存在
            SignalData existingSignal = storage->getSignal(signalId);
            bool isUpdate = !existingSignal.id.isEmpty();

            // 创建信号数据结构
            SignalData signalData;
            signalData.id = signalId;
            signalData.name = signalObj["name"].as<String>();
            signalData.type = signalObj["type"].as<String>();
            signalData.protocol = signalObj["protocol"].as<String>();
            signalData.data = signalObj["data"].as<String>();
            signalData.signalCode = signalObj["signalCode"].as<String>();
            signalData.frequency = signalObj["frequency"].as<String>();
            signalData.description = signalObj["description"].as<String>();
            signalData.isLearned = signalObj["isLearned"].as<bool>() || true;

            // 时间戳处理
            if (signalObj["created"].is<unsigned long>()) {
                signalData.created = signalObj["created"];
            } else {
                signalData.created = getCurrentUnixMs();
            }

            if (signalObj["lastSent"].is<unsigned long>()) {
                signalData.lastSent = signalObj["lastSent"];
            } else {
                signalData.lastSent = 0;
            }

            if (signalObj["sentCount"].is<int>()) {
                signalData.sentCount = signalObj["sentCount"];
            } else {
                signalData.sentCount = 0;
            }

            // 保存信号
            if (storage->saveSignal(signalData)) {
                if (isUpdate) {
                    duplicateCount++;
                } else {
                    successCount++;
                }

                JsonObject result = importResults.add<JsonObject>();
                result["id"] = signalId;
                result["name"] = signalData.name;
                result["status"] = isUpdate ? "updated" : "imported";
                result["reason"] = isUpdate ? "信号已存在，已更新" : "导入成功";
            } else {
                failureCount++;
                JsonObject result = importResults.add<JsonObject>();
                result["id"] = signalId;
                result["name"] = signalData.name;
                result["status"] = "failed";
                result["reason"] = "保存失败";
            }
        }

        // 构建响应
        JsonDocument responseDoc;
        responseDoc["success"] = true;
        responseDoc["data"]["total"] = signalsArray.size();
        responseDoc["data"]["imported"] = successCount;
        responseDoc["data"]["updated"] = duplicateCount;
        responseDoc["data"]["failed"] = failureCount;
        responseDoc["data"]["results"] = importResults;

        String message = "导入完成: " + String(successCount) + "个新增, " +
                        String(duplicateCount) + "个更新, " +
                        String(failureCount) + "个失败";

        sendSuccessResponse(request, responseDoc, message, startTime);

        // 通知WebSocket客户端
        SystemManager* systemManager = getSystemManager();
        if (systemManager) {
            JsonDocument eventData;
            eventData["imported"] = successCount;
            eventData["updated"] = duplicateCount;
            eventData["failed"] = failureCount;
            systemManager->sendSystemEvent("signals_imported", eventData);
        }

        // 记录导入日志
        Serial.printf("✅ 信号导入完成: %d个导入, %d个更新, %d个失败 (%d ms)\n",
                      successCount, duplicateCount, failureCount, millis() - startTime);
    }
}

// ==================== 信号分组管理API ====================

void APIHandlers::handleGetSignalGroups(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    // 获取信号存储管理器
    SignalStorage* storage = getSignalStorage();
    if (!storage) {
        sendErrorResponse(request, "信号存储系统未初始化", 500);
        return;
    }

    // 创建默认分组（基于信号类型）
    JsonDocument responseDoc;
    responseDoc["success"] = true;

    JsonArray groupsArray = responseDoc["data"]["groups"].to<JsonArray>();

    // 获取所有信号
    std::vector<SignalData> allSignals = storage->getAllSignals();

    // 统计各类型信号数量
    std::map<String, int> typeCounts;
    for (const SignalData& signal : allSignals) {
        typeCounts[signal.type]++;
    }

    // 创建类型分组
    const char* typeNames[] = {"tv", "ac", "fan", "light", "other"};
    const char* typeDisplayNames[] = {"电视", "空调", "风扇", "灯光", "其他"};
    const char* typeIcons[] = {"📺", "❄️", "🌀", "💡", "🔧"};
    const char* typeColors[] = {"#3b82f6", "#06b6d4", "#10b981", "#f59e0b", "#6b7280"};

    for (int i = 0; i < 5; i++) {
        JsonObject groupObj = groupsArray.add<JsonObject>();
        groupObj["id"] = String("type_") + typeNames[i];
        groupObj["name"] = typeDisplayNames[i];
        groupObj["description"] = String("所有") + typeDisplayNames[i] + "信号";
        groupObj["type"] = "system";  // 系统分组
        groupObj["icon"] = typeIcons[i];
        groupObj["color"] = typeColors[i];
        groupObj["signal_count"] = typeCounts[typeNames[i]];
        groupObj["is_active"] = true;
        groupObj["created_time"] = getCurrentUnixMs();

        // 添加该类型的信号ID列表
        JsonArray signalIds = groupObj["signal_ids"].to<JsonArray>();
        for (const SignalData& signal : allSignals) {
            if (signal.type == typeNames[i]) {
                signalIds.add(signal.id);
            }
        }
    }

    // 添加"全部信号"分组
    JsonObject allGroupObj = groupsArray.add<JsonObject>();
    allGroupObj["id"] = "all_signals";
    allGroupObj["name"] = "全部信号";
    allGroupObj["description"] = "系统中的所有信号";
    allGroupObj["type"] = "system";
    allGroupObj["icon"] = "📡";
    allGroupObj["color"] = "#8b5cf6";
    allGroupObj["signal_count"] = allSignals.size();
    allGroupObj["is_active"] = true;
    allGroupObj["created_time"] = getCurrentUnixMs();

    JsonArray allSignalIds = allGroupObj["signal_ids"].to<JsonArray>();
    for (const SignalData& signal : allSignals) {
        allSignalIds.add(signal.id);
    }

    responseDoc["data"]["total"] = groupsArray.size();

    sendSuccessResponse(request, responseDoc, "分组列表获取成功", startTime);
}

void APIHandlers::handleCreateSignalGroup(AsyncWebServerRequest *request, uint8_t *data,
                                         size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (index + len == total) {
        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, (char*)data);

        if (error) {
            sendErrorResponse(request, "JSON解析失败", 400);
            return;
        }

        // 验证必需字段
        if (!doc["name"].is<const char*>() || doc["name"].as<String>().isEmpty()) {
            sendErrorResponse(request, "分组名称不能为空", 400);
            return;
        }

        // 生成分组ID
        String groupId = "group_" + String(getCurrentUnixMs());

        // 创建分组数据
        JsonDocument responseDoc;
        responseDoc["success"] = true;
        responseDoc["data"]["id"] = groupId;
        responseDoc["data"]["name"] = doc["name"].as<String>();
        responseDoc["data"]["description"] = doc["description"].as<String>();
        responseDoc["data"]["type"] = "custom";  // 自定义分组
        String iconValue = doc["icon"].as<String>();
        responseDoc["data"]["icon"] = iconValue.isEmpty() ? "📁" : iconValue;
        String colorValue = doc["color"].as<String>();
        responseDoc["data"]["color"] = colorValue.isEmpty() ? "#6b7280" : colorValue;
        responseDoc["data"]["is_active"] = true;
        responseDoc["data"]["created_time"] = getCurrentUnixMs();
        responseDoc["data"]["signal_count"] = 0;

        // 处理信号ID列表
        JsonArray signalIds = responseDoc["data"]["signal_ids"].to<JsonArray>();
        if (doc["signal_ids"].is<JsonArray>()) {
            JsonArray inputSignalIds = doc["signal_ids"].as<JsonArray>();
            for (JsonVariant signalId : inputSignalIds) {
                signalIds.add(signalId.as<String>());
            }
            responseDoc["data"]["signal_count"] = signalIds.size();
        }

        sendSuccessResponse(request, responseDoc, "自定义分组创建成功", startTime);

        // 通知WebSocket客户端
        SystemManager* systemManager = getSystemManager();
        if (systemManager) {
            JsonDocument eventData;
            eventData["group_id"] = groupId;
            eventData["name"] = doc["name"].as<String>();
            systemManager->sendSystemEvent("signal_group_created", eventData);
        }

        // 记录日志
        Serial.printf("✅ 信号分组创建: %s (%s) - %d个信号\n",
                      doc["name"].as<String>().c_str(), groupId.c_str(), signalIds.size());
    }
}

void APIHandlers::handleUpdateSignalGroup(AsyncWebServerRequest *request, uint8_t *data,
                                         size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (index + len == total) {
        // 获取分组ID
        String groupId = request->pathArg(0);
        if (groupId.isEmpty()) {
            sendErrorResponse(request, "分组ID不能为空", 400);
            return;
        }

        // 检查是否为系统分组（不允许修改）
        if (groupId.startsWith("type_") || groupId == "all_signals") {
            sendErrorResponse(request, "系统分组不允许修改", 403);
            return;
        }

        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, (char*)data);

        if (error) {
            sendErrorResponse(request, "JSON解析失败", 400);
            return;
        }

        // 构建更新后的分组数据
        JsonDocument responseDoc;
        responseDoc["success"] = true;
        responseDoc["data"]["id"] = groupId;
        responseDoc["data"]["type"] = "custom";
        responseDoc["data"]["modified_time"] = getCurrentUnixMs();

        // 更新字段
        if (doc["name"].is<const char*>()) {
            responseDoc["data"]["name"] = doc["name"].as<String>();
        }
        if (doc["description"].is<const char*>()) {
            responseDoc["data"]["description"] = doc["description"].as<String>();
        }
        if (doc["icon"].is<const char*>()) {
            responseDoc["data"]["icon"] = doc["icon"].as<String>();
        }
        if (doc["color"].is<const char*>()) {
            responseDoc["data"]["color"] = doc["color"].as<String>();
        }
        if (doc["is_active"].is<bool>()) {
            responseDoc["data"]["is_active"] = doc["is_active"];
        }

        // 更新信号ID列表
        JsonArray signalIds = responseDoc["data"]["signal_ids"].to<JsonArray>();
        if (doc["signal_ids"].is<JsonArray>()) {
            JsonArray inputSignalIds = doc["signal_ids"].as<JsonArray>();
            for (JsonVariant signalId : inputSignalIds) {
                signalIds.add(signalId.as<String>());
            }
        }
        responseDoc["data"]["signal_count"] = signalIds.size();

        sendSuccessResponse(request, responseDoc, "分组更新成功", startTime);

        // 通知WebSocket客户端
        SystemManager* systemManager = getSystemManager();
        if (systemManager) {
            JsonDocument eventData;
            eventData["group_id"] = groupId;
            eventData["name"] = responseDoc["data"]["name"].as<String>();
            systemManager->sendSystemEvent("signal_group_updated", eventData);
        }
    }
}

void APIHandlers::handleDeleteSignalGroup(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    // 获取分组ID
    String groupId = request->pathArg(0);
    if (groupId.isEmpty()) {
        sendErrorResponse(request, "分组ID不能为空", 400);
        return;
    }

    // 检查是否为系统分组（不允许删除）
    if (groupId.startsWith("type_") || groupId == "all_signals") {
        sendErrorResponse(request, "系统分组不允许删除", 403);
        return;
    }

    JsonDocument responseDoc;
    responseDoc["success"] = true;
    responseDoc["data"]["id"] = groupId;
    responseDoc["data"]["deleted_time"] = getCurrentUnixMs();

    sendSuccessResponse(request, responseDoc, "分组删除成功", startTime);

    // 通知WebSocket客户端
    SystemManager* systemManager = getSystemManager();
    if (systemManager) {
        JsonDocument eventData;
        eventData["group_id"] = groupId;
        systemManager->sendSystemEvent("signal_group_deleted", eventData);
    }

    // 记录日志
    Serial.printf("✅ 信号分组删除: %s\n", groupId.c_str());
}

// ==================== 用户配置管理API ====================

void APIHandlers::handleGetUserConfig(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    // 创建配置数据（不包装success和data，让sendSuccessResponse处理）
    JsonDocument configData;

    // 界面设置
    JsonObject uiSettings = configData["ui_settings"].to<JsonObject>();
    uiSettings["theme"] = "light";  // light/dark
    uiSettings["language"] = "zh-CN";  // zh-CN/en-US
    uiSettings["default_view"] = "grid";  // grid/list
    uiSettings["auto_refresh"] = true;
    uiSettings["refresh_interval"] = 30;  // 秒
    uiSettings["show_notifications"] = true;
    uiSettings["notification_duration"] = 5;  // 秒
    uiSettings["enable_sound"] = false;
    uiSettings["compact_mode"] = false;

    // 信号设置
    JsonObject signalSettings = configData["signal_settings"].to<JsonObject>();
    signalSettings["default_protocol"] = "NEC";
    signalSettings["default_frequency"] = 38000;
    signalSettings["learning_timeout"] = 30;  // 秒
    signalSettings["emit_retry_count"] = 3;
    signalSettings["emit_retry_delay"] = 100;  // 毫秒
    signalSettings["auto_save_learned"] = true;  // 启用自动保存保护机制
    signalSettings["save_dialog_timeout"] = 15;  // 保存对话框超时时间(秒)
    signalSettings["show_raw_data"] = false;

    // 定时器设置
    JsonObject timerSettings = configData["timer_settings"].to<JsonObject>();
    timerSettings["enabled"] = true;
    timerSettings["default_interval"] = 60;  // 分钟
    timerSettings["max_concurrent_tasks"] = 5;
    timerSettings["task_timeout"] = 300;  // 秒
    timerSettings["auto_cleanup_completed"] = true;
    timerSettings["cleanup_after_days"] = 7;

    // 系统设置
    JsonObject systemSettings = configData["system_settings"].to<JsonObject>();
    systemSettings["debug_level"] = 2;  // 0-4
    systemSettings["log_retention_days"] = 30;
    systemSettings["max_log_size_mb"] = 10;
    systemSettings["enable_websocket"] = true;
    systemSettings["websocket_heartbeat"] = 30;  // 秒
    systemSettings["api_rate_limit"] = 100;  // 请求/分钟
    systemSettings["enable_cors"] = true;

    // 网络设置
    JsonObject networkSettings = configData["network_settings"].to<JsonObject>();
    networkSettings["wifi_auto_reconnect"] = true;
    networkSettings["connection_timeout"] = 10;  // 秒
    networkSettings["max_reconnect_attempts"] = 5;
    networkSettings["ap_mode_timeout"] = 300;  // 秒

    // 硬件设置 - 从系统配置中读取实际值
    JsonObject hardwareSettings = configData["hardware_settings"].to<JsonObject>();

    // 获取系统管理器实例来读取当前配置
    SystemManager& systemManager = SystemManager::getInstance();
    if (systemManager.getIsConfigLoaded()) {
        JsonDocument& sysConfig = systemManager.getSystemConfig();
        if (sysConfig["hardware_settings"].is<JsonObject>()) {
            JsonObject sysHwSettings = sysConfig["hardware_settings"];
            hardwareSettings["ir_recv_pin"] = sysHwSettings["ir_recv_pin"] | 4;
            hardwareSettings["ir_send_pin"] = sysHwSettings["ir_send_pin"] | 5;
            hardwareSettings["ir_frequency"] = sysHwSettings["ir_frequency"] | 38000;
            hardwareSettings["ir_duty_cycle"] = sysHwSettings["ir_duty_cycle"] | 33;
            hardwareSettings["status_led_pin"] = sysHwSettings["status_led_pin"] | 2;
            hardwareSettings["status_led_enabled"] = sysHwSettings["status_led_enabled"] | true;

            Serial.printf("📋 [API] 从系统配置读取硬件设置: 接收=%d, 发射=%d, 频率=%d\n",
                         (int)hardwareSettings["ir_recv_pin"],
                         (int)hardwareSettings["ir_send_pin"],
                         (int)hardwareSettings["ir_frequency"]);
        } else {
            Serial.println("⚠️ [API] 系统配置中没有hardware_settings，使用默认值");
            // 使用默认值
            hardwareSettings["ir_recv_pin"] = 4;
            hardwareSettings["ir_send_pin"] = 5;
            hardwareSettings["ir_frequency"] = 38000;
            hardwareSettings["ir_duty_cycle"] = 33;
            hardwareSettings["status_led_pin"] = 2;
            hardwareSettings["status_led_enabled"] = true;
        }
    } else {
        Serial.println("⚠️ [API] 系统配置未加载，使用默认值");
        // 使用默认值
        hardwareSettings["ir_recv_pin"] = 4;
        hardwareSettings["ir_send_pin"] = 5;
        hardwareSettings["ir_frequency"] = 38000;
        hardwareSettings["ir_duty_cycle"] = 33;
        hardwareSettings["status_led_pin"] = 2;
        hardwareSettings["status_led_enabled"] = true;
    }

    // 配置元数据
    configData["version"] = "1.0.0";
    configData["last_modified"] = getCurrentUnixMs();
    configData["created"] = getCurrentUnixMs();

    sendSuccessResponse(request, configData, "用户配置获取成功", startTime);
}

void APIHandlers::handleUpdateUserConfig(AsyncWebServerRequest *request, uint8_t *data,
                                        size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (index + len == total) {
        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, (char*)data);

        if (error) {
            sendErrorResponse(request, "JSON解析失败", 400);
            return;
        }

        // 验证配置数据
        JsonObject configData = doc.as<JsonObject>();

        // 创建响应数据（不包装success和data，让sendSuccessResponse处理）
        JsonDocument responseDoc;
        JsonObject updatedConfig = responseDoc.to<JsonObject>();

        // 更新界面设置
        if (configData["ui_settings"].is<JsonObject>()) {
            JsonObject uiSettings = configData["ui_settings"].as<JsonObject>();
            JsonObject responseUiSettings = updatedConfig["ui_settings"].to<JsonObject>();

            if (uiSettings["theme"].is<const char*>()) {
                String theme = uiSettings["theme"].as<String>();
                if (theme == "light" || theme == "dark") {
                    responseUiSettings["theme"] = theme;
                }
            }

            if (uiSettings["language"].is<const char*>()) {
                String language = uiSettings["language"].as<String>();
                if (language == "zh-CN" || language == "en-US") {
                    responseUiSettings["language"] = language;
                }
            }

            if (uiSettings["default_view"].is<const char*>()) {
                String view = uiSettings["default_view"].as<String>();
                if (view == "grid" || view == "list") {
                    responseUiSettings["default_view"] = view;
                }
            }

            if (uiSettings["auto_refresh"].is<bool>()) {
                responseUiSettings["auto_refresh"] = uiSettings["auto_refresh"];
            }

            if (uiSettings["refresh_interval"].is<int>()) {
                int interval = uiSettings["refresh_interval"];
                if (interval >= 5 && interval <= 300) {
                    responseUiSettings["refresh_interval"] = interval;
                }
            }

            if (uiSettings["show_notifications"].is<bool>()) {
                responseUiSettings["show_notifications"] = uiSettings["show_notifications"];
            }

            if (uiSettings["notification_duration"].is<int>()) {
                int duration = uiSettings["notification_duration"];
                if (duration >= 1 && duration <= 30) {
                    responseUiSettings["notification_duration"] = duration;
                }
            }

            if (uiSettings["enable_sound"].is<bool>()) {
                responseUiSettings["enable_sound"] = uiSettings["enable_sound"];
            }

            if (uiSettings["compact_mode"].is<bool>()) {
                responseUiSettings["compact_mode"] = uiSettings["compact_mode"];
            }
        }

        // 更新信号设置
        if (configData["signal_settings"].is<JsonObject>()) {
            JsonObject signalSettings = configData["signal_settings"].as<JsonObject>();
            JsonObject responseSignalSettings = updatedConfig["signal_settings"].to<JsonObject>();

            if (signalSettings["default_protocol"].is<const char*>()) {
                String protocol = signalSettings["default_protocol"].as<String>();
                if (protocol == "NEC" || protocol == "RC5" || protocol == "SONY" || protocol == "RAW") {
                    responseSignalSettings["default_protocol"] = protocol;
                }
            }

            if (signalSettings["default_frequency"].is<int>()) {
                int frequency = signalSettings["default_frequency"];
                if (frequency >= 30000 && frequency <= 60000) {
                    responseSignalSettings["default_frequency"] = frequency;
                }
            }

            if (signalSettings["learning_timeout"].is<int>()) {
                int timeout = signalSettings["learning_timeout"];
                if (timeout >= 5 && timeout <= 120) {
                    responseSignalSettings["learning_timeout"] = timeout;
                }
            }

            if (signalSettings["emit_retry_count"].is<int>()) {
                int retryCount = signalSettings["emit_retry_count"];
                if (retryCount >= 1 && retryCount <= 10) {
                    responseSignalSettings["emit_retry_count"] = retryCount;
                }
            }

            if (signalSettings["emit_retry_delay"].is<int>()) {
                int retryDelay = signalSettings["emit_retry_delay"];
                if (retryDelay >= 50 && retryDelay <= 1000) {
                    responseSignalSettings["emit_retry_delay"] = retryDelay;
                }
            }

            if (signalSettings["auto_save_learned"].is<bool>()) {
                responseSignalSettings["auto_save_learned"] = signalSettings["auto_save_learned"];
            }

            if (signalSettings["show_raw_data"].is<bool>()) {
                responseSignalSettings["show_raw_data"] = signalSettings["show_raw_data"];
            }
        }

        // 更新定时器设置
        if (configData["timer_settings"].is<JsonObject>()) {
            JsonObject timerSettings = configData["timer_settings"].as<JsonObject>();
            JsonObject responseTimerSettings = updatedConfig["timer_settings"].to<JsonObject>();

            if (timerSettings["enabled"].is<bool>()) {
                responseTimerSettings["enabled"] = timerSettings["enabled"];
            }

            if (timerSettings["default_interval"].is<int>()) {
                int interval = timerSettings["default_interval"];
                if (interval >= 1 && interval <= 1440) {  // 1分钟到24小时
                    responseTimerSettings["default_interval"] = interval;
                }
            }

            if (timerSettings["max_concurrent_tasks"].is<int>()) {
                int maxTasks = timerSettings["max_concurrent_tasks"];
                if (maxTasks >= 1 && maxTasks <= 20) {
                    responseTimerSettings["max_concurrent_tasks"] = maxTasks;
                }
            }

            if (timerSettings["task_timeout"].is<int>()) {
                int timeout = timerSettings["task_timeout"];
                if (timeout >= 60 && timeout <= 3600) {  // 1分钟到1小时
                    responseTimerSettings["task_timeout"] = timeout;
                }
            }

            if (timerSettings["auto_cleanup_completed"].is<bool>()) {
                responseTimerSettings["auto_cleanup_completed"] = timerSettings["auto_cleanup_completed"];
            }

            if (timerSettings["cleanup_after_days"].is<int>()) {
                int days = timerSettings["cleanup_after_days"];
                if (days >= 1 && days <= 365) {
                    responseTimerSettings["cleanup_after_days"] = days;
                }
            }
        }

        // 处理硬件设置
        if (configData["hardware_settings"].is<JsonObject>()) {
            JsonObject hwSettings = configData["hardware_settings"].as<JsonObject>();
            JsonObject responseHwSettings = updatedConfig["hardware_settings"].to<JsonObject>();

            // 验证并保存硬件配置
            if (hwSettings["ir_send_pin"].is<int>()) {
                int sendPin = hwSettings["ir_send_pin"];
                if (sendPin >= 0 && sendPin <= 48) {
                    responseHwSettings["ir_send_pin"] = sendPin;
                }
            }

            if (hwSettings["ir_recv_pin"].is<int>()) {
                int recvPin = hwSettings["ir_recv_pin"];
                if (recvPin >= 0 && recvPin <= 48) {
                    responseHwSettings["ir_recv_pin"] = recvPin;
                }
            }

            if (hwSettings["ir_frequency"].is<int>()) {
                int frequency = hwSettings["ir_frequency"];
                if (frequency >= 30000 && frequency <= 56000) {
                    responseHwSettings["ir_frequency"] = frequency;
                }
            }

            if (hwSettings["ir_duty_cycle"].is<int>()) {
                int dutyCycle = hwSettings["ir_duty_cycle"];
                if (dutyCycle >= 10 && dutyCycle <= 50) {
                    responseHwSettings["ir_duty_cycle"] = dutyCycle;
                }
            }

            if (hwSettings["status_led_pin"].is<int>()) {
                int ledPin = hwSettings["status_led_pin"];
                if (ledPin >= 0 && ledPin <= 48) {
                    responseHwSettings["status_led_pin"] = ledPin;
                }
            }

            if (hwSettings["status_led_enabled"].is<bool>()) {
                responseHwSettings["status_led_enabled"] = hwSettings["status_led_enabled"];
            }

            // 保存硬件配置到系统配置文件
            SystemManager* systemManager = getSystemManager();
            if (systemManager) {
                systemManager->updateHardwareConfig(responseHwSettings);
            }
        }

        // 更新配置元数据
        updatedConfig["version"] = "1.0.0";
        updatedConfig["last_modified"] = getCurrentUnixMs();

        sendSuccessResponse(request, responseDoc, "用户配置更新成功", startTime);

        // 通知WebSocket客户端
        SystemManager* systemManager = getSystemManager();
        if (systemManager) {
            JsonDocument eventData;
            eventData["config_updated"] = true;
            eventData["timestamp"] = getCurrentUnixMs();
            systemManager->sendSystemEvent("user_config_updated", eventData);
        }

        // 记录日志
        Serial.printf("✅ 用户配置更新完成 (%d ms)\n", millis() - startTime);
    }
}

void APIHandlers::handleResetUserConfig(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    // 重置为默认配置（只创建数据部分，不包装success）
    JsonDocument responseData;
    responseData["reset_time"] = getCurrentUnixMs();
    responseData["version"] = "1.0.0";

    sendSuccessResponse(request, responseData, "用户配置已重置为默认值", startTime);

    // 通知WebSocket客户端
    SystemManager* systemManager = getSystemManager();
    if (systemManager) {
        JsonDocument eventData;
        eventData["config_reset"] = true;
        eventData["timestamp"] = getCurrentUnixMs();
        systemManager->sendSystemEvent("user_config_reset", eventData);
    }

    // 记录日志
    Serial.printf("✅ 用户配置重置完成 (%d ms)\n", millis() - startTime);
}

// ==================== 系统备份恢复API ====================

void APIHandlers::handleExportBackup(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    // 获取系统管理器
    SystemManager* systemManager = getSystemManager();
    if (!systemManager) {
        sendErrorResponse(request, "系统管理器未初始化", 500);
        return;
    }

    // 获取信号存储管理器
    SignalStorage* storage = getSignalStorage();
    if (!storage) {
        sendErrorResponse(request, "信号存储系统未初始化", 500);
        return;
    }

    // 创建备份数据
    JsonDocument backupDoc;

    // 备份元数据
    backupDoc["backup_info"]["version"] = "1.0.0";
    backupDoc["backup_info"]["created_time"] = getCurrentUnixMs();
    backupDoc["backup_info"]["device_id"] = WiFi.macAddress();
    backupDoc["backup_info"]["firmware_version"] = "1.0.0";
    backupDoc["backup_info"]["backup_type"] = "full";

    // 备份信号数据
    JsonArray signalsArray = backupDoc["signals"].to<JsonArray>();
    std::vector<SignalData> allSignals = storage->getAllSignals();

    for (const SignalData& signal : allSignals) {
        JsonObject signalObj = signalsArray.add<JsonObject>();
        signalObj["id"] = signal.id;
        signalObj["name"] = signal.name;
        signalObj["type"] = signal.type;
        signalObj["protocol"] = signal.protocol;
        signalObj["data"] = signal.data;
        signalObj["signalCode"] = signal.signalCode;
        signalObj["frequency"] = signal.frequency;
        signalObj["description"] = signal.description;
        signalObj["isLearned"] = signal.isLearned;
        signalObj["created"] = signal.created;
        signalObj["lastSent"] = signal.lastSent;
        signalObj["sentCount"] = signal.sentCount;
    }

    // 备份定时任务数据
    TimerManagerBackend* timerManager = systemManager->getTimerManager();
    if (timerManager) {
        JsonArray timerTasksArray = backupDoc["timer_tasks"].to<JsonArray>();
        // 这里需要实现获取所有定时任务的方法
        // 暂时创建空数组
    }

    // 备份用户配置（使用默认配置作为示例）
    JsonObject userConfig = backupDoc["user_config"].to<JsonObject>();

    // 界面设置
    JsonObject uiSettings = userConfig["ui_settings"].to<JsonObject>();
    uiSettings["theme"] = "light";
    uiSettings["language"] = "zh-CN";
    uiSettings["default_view"] = "grid";
    uiSettings["auto_refresh"] = true;
    uiSettings["refresh_interval"] = 30;

    // 信号设置
    JsonObject signalSettings = userConfig["signal_settings"].to<JsonObject>();
    signalSettings["default_protocol"] = "NEC";
    signalSettings["default_frequency"] = 38000;
    signalSettings["learning_timeout"] = 30;
    signalSettings["emit_retry_count"] = 3;

    // 定时器设置
    JsonObject timerSettings = userConfig["timer_settings"].to<JsonObject>();
    timerSettings["enabled"] = true;
    timerSettings["default_interval"] = 60;
    timerSettings["max_concurrent_tasks"] = 5;

    // 系统统计信息
    JsonObject systemStats = backupDoc["system_stats"].to<JsonObject>();
    systemStats["total_signals"] = allSignals.size();
    systemStats["total_timer_tasks"] = 0;  // 待实现
    systemStats["uptime"] = millis() / 1000;
    systemStats["free_heap"] = ESP.getFreeHeap();
    systemStats["chip_temperature"] = temperatureRead();

    // 生成备份文件名
    String timestamp = String(getCurrentUnixMs());
    String filename = "system_backup_" + timestamp + ".json";

    // 序列化JSON
    String jsonString;
    serializeJsonPretty(backupDoc, jsonString);

    // 设置响应头
    AsyncWebServerResponse *response = request->beginResponse(200, "application/json", jsonString);
    response->addHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
    response->addHeader("Access-Control-Allow-Origin", "*");

    request->send(response);

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);

    // 记录备份日志
    Serial.printf("✅ 系统备份导出完成: %d个信号, 文件: %s (%d ms)\n",
                  allSignals.size(), filename.c_str(), processingTime);
}

void APIHandlers::handleImportBackup(AsyncWebServerRequest *request, uint8_t *data,
                                    size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (index + len == total) {
        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, (char*)data);

        if (error) {
            sendErrorResponse(request, "备份文件解析失败: " + String(error.c_str()), 400);
            return;
        }

        // 验证备份文件格式
        if (!doc["backup_info"].is<JsonObject>()) {
            sendErrorResponse(request, "备份文件格式错误：缺少备份信息", 400);
            return;
        }

        JsonObject backupInfo = doc["backup_info"].as<JsonObject>();
        String backupVersion = backupInfo["version"].as<String>();

        if (backupVersion.isEmpty()) {
            sendErrorResponse(request, "备份文件格式错误：缺少版本信息", 400);
            return;
        }

        // 获取系统管理器
        SystemManager* systemManager = getSystemManager();
        if (!systemManager) {
            sendErrorResponse(request, "系统管理器未初始化", 500);
            return;
        }

        // 获取信号存储管理器
        SignalStorage* storage = getSignalStorage();
        if (!storage) {
            sendErrorResponse(request, "信号存储系统未初始化", 500);
            return;
        }

        // 恢复统计
        int signalsImported = 0;
        int signalsFailed = 0;
        int timerTasksImported = 0;
        int timerTasksFailed = 0;
        bool configRestored = false;

        // 恢复信号数据
        if (doc["signals"].is<JsonArray>()) {
            JsonArray signalsArray = doc["signals"].as<JsonArray>();

            for (JsonVariant signalVariant : signalsArray) {
                JsonObject signalObj = signalVariant.as<JsonObject>();

                // 验证信号数据
                if (!validateSignalData(signalObj)) {
                    signalsFailed++;
                    continue;
                }

                // 创建信号数据结构
                SignalData signalData;
                signalData.id = signalObj["id"].as<String>();
                signalData.name = signalObj["name"].as<String>();
                signalData.type = signalObj["type"].as<String>();
                signalData.protocol = signalObj["protocol"].as<String>();
                signalData.data = signalObj["data"].as<String>();
                signalData.signalCode = signalObj["signalCode"].as<String>();
                signalData.frequency = signalObj["frequency"].as<String>();
                signalData.description = signalObj["description"].as<String>();
                signalData.isLearned = signalObj["isLearned"].as<bool>() || true;
                signalData.created = signalObj["created"].as<unsigned long>() ?: getCurrentUnixMs();
                signalData.lastSent = signalObj["lastSent"].as<unsigned long>() ?: 0;
                signalData.sentCount = signalObj["sentCount"].as<int>() ?: 0;

                // 保存信号
                if (storage->saveSignal(signalData)) {
                    signalsImported++;
                } else {
                    signalsFailed++;
                }
            }
        }

        // 恢复定时任务数据
        if (doc["timer_tasks"].is<JsonArray>()) {
            JsonArray timerTasksArray = doc["timer_tasks"].as<JsonArray>();
            TimerManagerBackend* timerManager = systemManager->getTimerManager();

            if (timerManager) {
                for (JsonVariant taskVariant : timerTasksArray) {
                    JsonObject taskObj = taskVariant.as<JsonObject>();

                    // 创建定时任务数据
                    TimerTask task;
                    task.name = taskObj["name"].as<String>();
                    task.startTime = taskObj["startTime"].as<String>();
                    task.endTime = taskObj["endTime"].as<String>();
                    task.isEnabled = taskObj["isEnabled"].as<bool>() || true;
                    task.isDaily = taskObj["isDaily"].as<bool>() || true;
                    task.intervalMinutes = taskObj["intervalMinutes"].as<int>() ?: 60;

                    // 解析信号ID列表
                    if (taskObj["signal_ids"].is<JsonArray>()) {
                        JsonArray signalIds = taskObj["signal_ids"].as<JsonArray>();
                        for (JsonVariant signalId : signalIds) {
                            task.signalIds.push_back(signalId.as<String>());
                        }
                    }

                    // 创建定时任务
                    String taskId = timerManager->createTask(task);
                    if (!taskId.isEmpty()) {
                        timerTasksImported++;
                    } else {
                        timerTasksFailed++;
                    }
                }
            }
        }

        // 恢复用户配置
        if (doc["user_config"].is<JsonObject>()) {
            // 这里可以实现配置恢复逻辑
            // 暂时标记为已恢复
            configRestored = true;
        }

        // 构建响应
        JsonDocument responseDoc;
        responseDoc["success"] = true;
        responseDoc["data"]["backup_version"] = backupVersion;
        responseDoc["data"]["backup_time"] = backupInfo["created_time"];
        responseDoc["data"]["restore_time"] = getCurrentUnixMs();

        JsonObject restoreStats = responseDoc["data"]["restore_stats"].to<JsonObject>();
        restoreStats["signals_imported"] = signalsImported;
        restoreStats["signals_failed"] = signalsFailed;
        restoreStats["timer_tasks_imported"] = timerTasksImported;
        restoreStats["timer_tasks_failed"] = timerTasksFailed;
        restoreStats["config_restored"] = configRestored;

        String message = "系统恢复完成: " + String(signalsImported) + "个信号, " +
                        String(timerTasksImported) + "个定时任务";

        sendSuccessResponse(request, responseDoc, message, startTime);

        // 通知WebSocket客户端
        JsonDocument eventData;
        eventData["signals_imported"] = signalsImported;
        eventData["timer_tasks_imported"] = timerTasksImported;
        eventData["config_restored"] = configRestored;
        systemManager->sendSystemEvent("system_restored", eventData);

        // 记录恢复日志
        Serial.printf("✅ 系统恢复完成: %d个信号, %d个定时任务 (%d ms)\n",
                      signalsImported, timerTasksImported, millis() - startTime);
    }
}

// ==================== 日志管理API ====================

void APIHandlers::handleGetSystemLogs(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    // 获取查询参数
    int page = 1;
    int pageSize = 50;
    String level = "all";  // all, error, warn, info, debug
    String startDate = "";
    String endDate = "";

    if (request->hasParam("page")) {
        page = request->getParam("page")->value().toInt();
        if (page < 1) page = 1;
    }

    if (request->hasParam("page_size")) {
        pageSize = request->getParam("page_size")->value().toInt();
        if (pageSize < 1) pageSize = 50;
        if (pageSize > 1000) pageSize = 1000;
    }

    if (request->hasParam("level")) {
        level = request->getParam("level")->value();
    }

    if (request->hasParam("start_date")) {
        startDate = request->getParam("start_date")->value();
    }

    if (request->hasParam("end_date")) {
        endDate = request->getParam("end_date")->value();
    }

    // 创建模拟日志数据（实际项目中应该从日志存储中读取）
    JsonDocument responseDoc;
    responseDoc["success"] = true;

    JsonArray logsArray = responseDoc["data"]["logs"].to<JsonArray>();

    // 生成一些示例日志
    const char* logLevels[] = {"ERROR", "WARN", "INFO", "DEBUG"};
    const char* logMessages[] = {
        "系统启动完成",
        "WiFi连接成功",
        "信号学习开始",
        "信号发射完成",
        "定时任务执行",
        "WebSocket连接建立",
        "API请求处理",
        "内存使用率检查",
        "温度监控更新",
        "配置文件加载"
    };

    uint64_t currentTime = getCurrentUnixMs();
    int totalLogs = 100;  // 模拟总日志数
    int startIndex = (page - 1) * pageSize;
    int endIndex = min(startIndex + pageSize, totalLogs);

    for (int i = startIndex; i < endIndex; i++) {
        JsonObject logObj = logsArray.add<JsonObject>();
        logObj["id"] = String("log_") + String(i + 1);
        logObj["timestamp"] = currentTime - (totalLogs - i) * 60000;  // 每分钟一条日志
        logObj["level"] = logLevels[i % 4];
        logObj["message"] = logMessages[i % 10];
        logObj["module"] = (i % 3 == 0) ? "SignalManager" :
                          (i % 3 == 1) ? "TimerManager" : "SystemManager";
        logObj["thread"] = (i % 2 == 0) ? "Core0" : "Core1";
        logObj["memory_usage"] = 45.2 + (i % 20);
        logObj["free_heap"] = 200000 + (i * 1000);
    }

    // 分页信息
    JsonObject pagination = responseDoc["data"]["pagination"].to<JsonObject>();
    pagination["current_page"] = page;
    pagination["page_size"] = pageSize;
    pagination["total_logs"] = totalLogs;
    pagination["total_pages"] = (totalLogs + pageSize - 1) / pageSize;
    pagination["has_next"] = page < pagination["total_pages"].as<int>();
    pagination["has_prev"] = page > 1;

    // 过滤信息
    JsonObject filters = responseDoc["data"]["filters"].to<JsonObject>();
    filters["level"] = level;
    filters["start_date"] = startDate;
    filters["end_date"] = endDate;

    // 统计信息
    JsonObject stats = responseDoc["data"]["stats"].to<JsonObject>();
    stats["error_count"] = totalLogs / 4;
    stats["warn_count"] = totalLogs / 4;
    stats["info_count"] = totalLogs / 4;
    stats["debug_count"] = totalLogs / 4;
    stats["today_logs"] = 50;
    stats["last_hour_logs"] = 10;

    sendSuccessResponse(request, responseDoc, "日志获取成功", startTime);
}

void APIHandlers::handleClearSystemLogs(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    // 获取清理参数
    String clearType = "all";  // all, before_date, by_level
    String beforeDate = "";
    String level = "";

    if (request->hasParam("type")) {
        clearType = request->getParam("type")->value();
    }

    if (request->hasParam("before_date")) {
        beforeDate = request->getParam("before_date")->value();
    }

    if (request->hasParam("level")) {
        level = request->getParam("level")->value();
    }

    // 模拟清理操作
    int clearedCount = 0;

    if (clearType == "all") {
        clearedCount = 100;  // 模拟清理所有日志
    } else if (clearType == "before_date" && !beforeDate.isEmpty()) {
        clearedCount = 50;   // 模拟清理指定日期前的日志
    } else if (clearType == "by_level" && !level.isEmpty()) {
        clearedCount = 25;   // 模拟清理指定级别的日志
    }

    JsonDocument responseDoc;
    responseDoc["success"] = true;
    responseDoc["data"]["cleared_count"] = clearedCount;
    responseDoc["data"]["clear_type"] = clearType;
    responseDoc["data"]["clear_time"] = getCurrentUnixMs();

    if (!beforeDate.isEmpty()) {
        responseDoc["data"]["before_date"] = beforeDate;
    }

    if (!level.isEmpty()) {
        responseDoc["data"]["level"] = level;
    }

    String message = "日志清理完成，共清理 " + String(clearedCount) + " 条日志";
    sendSuccessResponse(request, responseDoc, message, startTime);

    // 通知WebSocket客户端
    SystemManager* systemManager = getSystemManager();
    if (systemManager) {
        JsonDocument eventData;
        eventData["cleared_count"] = clearedCount;
        eventData["clear_type"] = clearType;
        systemManager->sendSystemEvent("logs_cleared", eventData);
    }

    // 记录清理日志
    Serial.printf("✅ 日志清理完成: %d条日志 (%s) (%d ms)\n",
                  clearedCount, clearType.c_str(), millis() - startTime);
}

void APIHandlers::handleDownloadLogs(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    // 获取下载参数
    String format = "txt";  // txt, json, csv
    String level = "all";
    String startDate = "";
    String endDate = "";

    if (request->hasParam("format")) {
        format = request->getParam("format")->value();
    }

    if (request->hasParam("level")) {
        level = request->getParam("level")->value();
    }

    if (request->hasParam("start_date")) {
        startDate = request->getParam("start_date")->value();
    }

    if (request->hasParam("end_date")) {
        endDate = request->getParam("end_date")->value();
    }

    // 生成日志内容
    String logContent;
    String contentType;
    String fileExtension;

    if (format == "json") {
        // JSON格式
        JsonDocument logsDoc;
        JsonArray logsArray = logsDoc["logs"].to<JsonArray>();

        // 添加示例日志
        for (int i = 0; i < 10; i++) {
            JsonObject logObj = logsArray.add<JsonObject>();
            logObj["timestamp"] = getCurrentUnixMs() - i * 60000;
            logObj["level"] = (i % 2 == 0) ? "INFO" : "DEBUG";
            logObj["message"] = "示例日志消息 " + String(i + 1);
            logObj["module"] = "SystemManager";
        }

        serializeJsonPretty(logsDoc, logContent);
        contentType = "application/json";
        fileExtension = "json";

    } else if (format == "csv") {
        // CSV格式
        logContent = "Timestamp,Level,Module,Message\n";
        for (int i = 0; i < 10; i++) {
            logContent += String(getCurrentUnixMs() - i * 60000) + ",";
            logContent += ((i % 2 == 0) ? "INFO" : "DEBUG") + String(",");
            logContent += "SystemManager,";
            logContent += "示例日志消息 " + String(i + 1) + "\n";
        }
        contentType = "text/csv";
        fileExtension = "csv";

    } else {
        // TXT格式（默认）
        for (int i = 0; i < 10; i++) {
            uint64_t timestamp = getCurrentUnixMs() - i * 60000;
            String level = (i % 2 == 0) ? "INFO" : "DEBUG";
            logContent += "[" + String(timestamp) + "] " + level + " [SystemManager] 示例日志消息 " + String(i + 1) + "\n";
        }
        contentType = "text/plain";
        fileExtension = "txt";
    }

    // 生成文件名
    String timestamp = String(getCurrentUnixMs());
    String filename = "system_logs_" + timestamp + "." + fileExtension;

    // 设置响应头
    AsyncWebServerResponse *response = request->beginResponse(200, contentType, logContent);
    response->addHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
    response->addHeader("Access-Control-Allow-Origin", "*");

    request->send(response);

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);

    // 记录下载日志
    Serial.printf("✅ 日志下载完成: 格式=%s, 文件=%s (%d ms)\n",
                  format.c_str(), filename.c_str(), processingTime);
}

// ==================== 红外功能API ====================

void APIHandlers::handleLearningControl(AsyncWebServerRequest *request, uint8_t *data,
                                       size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    // 🎯 添加详细的调试信息
    Serial.printf("🎯 [Learning API Body Handler] 被调用: len=%d, index=%d, total=%d\n", len, index, total);

    // 处理空请求的情况 - 如果没有body数据，直接返回错误
    if (total == 0) {
        Serial.println("❌ [Learning API] 收到空请求体");
        sendErrorResponse(request, "请求体不能为空", 400);
        return;
    }

    if (data && len > 0) {
        String requestBody = String((char*)data, len);
        Serial.printf("📝 [Learning API] 请求体内容: %s\n", requestBody.c_str());
    } else {
        Serial.println("⚠️ [Learning API] Body处理器收到空数据");
    }

    if (index + len == total) {
        Serial.println("✅ [Learning API] 完整请求体已接收，开始处理");
        // 检查是否有请求体
        if (len == 0 || data == nullptr) {
            Serial.println("❌ [Learning API] 请求体为空");
            sendErrorResponse(request, "请求体不能为空", 400);
            return;
        }

        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, (char*)data);

        if (error) {
            Serial.printf("❌ [Learning API] JSON解析失败: %s\n", error.c_str());
            sendErrorResponse(request, "JSON解析失败", 400);
            return;
        }

        String command = doc["command"].as<String>();
        uint32_t timeout = doc["timeout"].as<uint32_t>() ?: 30000;  // 默认30秒超时

        // 获取IR管理器
        SystemManager* systemManager = getSystemManager();
        if (!systemManager) {
            sendErrorResponse(request, "系统管理器未初始化", 500);
            return;
        }

        IRSignalManager* irManager = systemManager->getIRManager();
        if (!irManager) {
            sendErrorResponse(request, "红外管理器未初始化", 500);
            return;
        }

        JsonDocument responseDoc;

        if (command == "start") {
            Serial.println("🔄 [Learning API] 执行开始学习命令");
            bool startResult = irManager->startLearning(timeout);
            Serial.printf("🔍 [Learning API] startLearning()返回: %s\n", startResult ? "true" : "false");

            if (startResult) {
                // 只传递data部分，不要包装整个响应
                JsonDocument dataOnly;
                dataOnly["timeout"] = timeout;
                dataOnly["status"] = "learning";

                // 发送WebSocket事件通知前端
                WebSocketManager* wsManager = systemManager->getWebSocketManager();
                if (wsManager) {
                    wsManager->sendLearningStartedEvent(timeout);
                }

                Serial.println("✅ [Learning API] 准备发送成功响应");
                sendSuccessResponse(request, dataOnly, "学习模式已启动", startTime);
                Serial.println("✅ [Learning API] 成功响应已发送");
            } else {
                Serial.println("❌ [Learning API] 准备发送错误响应");
                sendErrorResponse(request, "启动学习模式失败", 500);
                Serial.println("❌ [Learning API] 错误响应已发送");
            }
        } else if (command == "stop") {
            Serial.println("🔄 [Learning API] 执行停止学习命令");
            bool stopResult = irManager->stopLearning();
            Serial.printf("🔍 [Learning API] stopLearning()返回: %s\n", stopResult ? "true" : "false");

            if (stopResult) {
                // 只传递data部分，不要包装整个响应
                JsonDocument dataOnly;
                dataOnly["status"] = "stopped";
                Serial.println("✅ [Learning API] 准备发送成功响应");
                sendSuccessResponse(request, dataOnly, "学习模式已停止", startTime);
                Serial.println("✅ [Learning API] 成功响应已发送");
            } else {
                Serial.println("❌ [Learning API] 准备发送错误响应");
                sendErrorResponse(request, "停止学习模式失败", 500);
                Serial.println("❌ [Learning API] 错误响应已发送");
            }
        } else if (command == "pause") {
            // 暂停功能暂时通过停止学习实现
            if (irManager->stopLearning()) {
                responseDoc["success"] = true;
                responseDoc["data"]["status"] = "paused";
                sendSuccessResponse(request, responseDoc, "学习模式已暂停", startTime);
            } else {
                sendErrorResponse(request, "暂停学习模式失败", 500);
            }
        } else if (command == "resume") {
            // 恢复功能暂时通过重新启动学习实现
            if (irManager->startLearning(timeout)) {
                responseDoc["success"] = true;
                responseDoc["data"]["status"] = "learning";
                sendSuccessResponse(request, responseDoc, "学习模式已恢复", startTime);
            } else {
                sendErrorResponse(request, "恢复学习模式失败", 500);
            }
        } else {
            sendErrorResponse(request, "无效的学习命令", 400);
        }
    }
}

void APIHandlers::handleLearningDetect(AsyncWebServerRequest *request, uint8_t *data,
                                      size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (index + len == total) {
        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, (char*)data);

        if (error) {
            sendErrorResponse(request, "JSON解析失败", 400);
            return;
        }

        uint32_t timeout = doc["timeout"].as<uint32_t>() ?: 5000;  // 默认5秒检测超时

        // 获取IR管理器
        SystemManager* systemManager = getSystemManager();
        if (!systemManager) {
            sendErrorResponse(request, "系统管理器未初始化", 500);
            return;
        }

        IRSignalManager* irManager = systemManager->getIRManager();
        if (!irManager) {
            sendErrorResponse(request, "红外管理器未初始化", 500);
            return;
        }

        // 开始信号检测（通过启动学习模式实现）
        bool detected = false;
        JsonDocument responseDoc;
        responseDoc["success"] = true;

        if (irManager->startLearning(timeout)) {
            // 等待检测结果或超时
            uint32_t detectStartTime = millis();
            while (millis() - detectStartTime < timeout) {
                if (irManager->isLearning()) {
                    JsonDocument learningStatus = irManager->getLearningStatus();
                    if (learningStatus["state"] == "LEARNING_COMPLETE") {
                        detected = true;
                        responseDoc["data"]["detected"] = true;
                        responseDoc["data"]["signal"] = learningStatus["signal"];
                        irManager->stopLearning();
                        break;
                    }
                } else {
                    break;
                }
                delay(100);  // 短暂延迟
            }

            if (!detected) {
                responseDoc["data"]["detected"] = false;
                irManager->stopLearning();
            }
        } else {
            responseDoc["data"]["detected"] = false;
        }

        String message = detected ? "检测到红外信号" : "未检测到红外信号";
        sendSuccessResponse(request, responseDoc, message, startTime);
    }
}

// ==================== 定时器管理API ====================

void APIHandlers::handleCreateTimerTask(AsyncWebServerRequest *request, uint8_t *data,
                                       size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (index + len == total) {
        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, (char*)data);

        if (error) {
            sendErrorResponse(request, "JSON解析失败", 400);
            return;
        }

        // 验证必需字段
        if (!doc["name"].is<const char*>() || !doc["startTime"].is<const char*>() || !doc["signal_ids"].is<JsonArray>()) {
            sendErrorResponse(request, "缺少必需字段", 400);
            return;
        }

        // 获取定时器管理器
        SystemManager* systemManager = getSystemManager();
        if (!systemManager) {
            sendErrorResponse(request, "系统管理器未初始化", 500);
            return;
        }

        TimerManagerBackend* timerManager = systemManager->getTimerManager();
        if (!timerManager) {
            sendErrorResponse(request, "定时器管理器未初始化", 500);
            return;
        }

        // 创建定时任务数据
        TimerTask task;
        task.name = doc["name"].as<String>();
        task.startTime = doc["startTime"].as<String>();
        task.endTime = doc["endTime"].as<String>();
        task.isEnabled = doc["enabled"].as<bool>() || true;
        task.isDaily = doc["isDaily"].as<bool>() || true;
        task.intervalMinutes = doc["intervalMinutes"].as<int>() ?: 60;

        // 解析信号ID列表
        JsonArray signalIds = doc["signal_ids"].as<JsonArray>();
        for (JsonVariant signalId : signalIds) {
            task.signalIds.push_back(signalId.as<String>());
        }

        // 创建定时任务
        String taskId = timerManager->createTask(task);
        if (!taskId.isEmpty()) {
            JsonDocument responseData;
            responseData["taskId"] = taskId;
            responseData["name"] = task.name;
            responseData["enabled"] = task.isEnabled;

            sendSuccessResponse(request, responseData, "定时任务创建成功", startTime);

            // 通知WebSocket客户端
            JsonDocument eventData;
            eventData["taskId"] = taskId;
            eventData["name"] = task.name;
            systemManager->sendSystemEvent("timer_task_created", eventData);
        } else {
            sendErrorResponse(request, "定时任务创建失败", 500);
        }
    }
}

void APIHandlers::handleUpdateTimerTask(AsyncWebServerRequest *request, uint8_t *data,
                                       size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (index + len == total) {
        // 获取任务ID
        String taskId = request->pathArg(0);
        if (taskId.isEmpty()) {
            sendErrorResponse(request, "任务ID不能为空", 400);
            return;
        }

        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, (char*)data);

        if (error) {
            sendErrorResponse(request, "JSON解析失败", 400);
            return;
        }

        // 获取定时器管理器
        SystemManager* systemManager = getSystemManager();
        if (!systemManager) {
            sendErrorResponse(request, "系统管理器未初始化", 500);
            return;
        }

        TimerManagerBackend* timerManager = systemManager->getTimerManager();
        if (!timerManager) {
            sendErrorResponse(request, "定时器管理器未初始化", 500);
            return;
        }

        // 获取现有任务
        TimerTask* existingTaskPtr = timerManager->getTask(taskId);
        if (!existingTaskPtr) {
            sendErrorResponse(request, "定时任务不存在", 404);
            return;
        }

        TimerTask existingTask = *existingTaskPtr;

        // 更新任务数据
        if (doc["name"].is<const char*>()) {
            existingTask.name = doc["name"].as<String>();
        }
        if (doc["startTime"].is<const char*>()) {
            existingTask.startTime = doc["startTime"].as<String>();
        }
        if (doc["endTime"].is<const char*>()) {
            existingTask.endTime = doc["endTime"].as<String>();
        }
        if (doc["enabled"].is<bool>()) {
            existingTask.isEnabled = doc["enabled"];
        }
        if (doc["isDaily"].is<bool>()) {
            existingTask.isDaily = doc["isDaily"];
        }
        if (doc["intervalMinutes"].is<int>()) {
            existingTask.intervalMinutes = doc["intervalMinutes"];
        }
        if (doc["signal_ids"].is<JsonArray>()) {
            existingTask.signalIds.clear();
            JsonArray signalIds = doc["signal_ids"].as<JsonArray>();
            for (JsonVariant signalId : signalIds) {
                existingTask.signalIds.push_back(signalId.as<String>());
            }
        }

        // 更新定时任务
        if (timerManager->updateTask(taskId, existingTask)) {
            JsonDocument responseDoc;
            responseDoc["success"] = true;
            responseDoc["data"]["taskId"] = existingTask.id;
            responseDoc["data"]["name"] = existingTask.name;
            responseDoc["data"]["enabled"] = existingTask.isEnabled;

            sendSuccessResponse(request, responseDoc, "定时任务更新成功", startTime);

            // 通知WebSocket客户端
            JsonDocument eventData;
            eventData["taskId"] = taskId;
            eventData["name"] = existingTask.name;
            systemManager->sendSystemEvent("timer_task_updated", eventData);
        } else {
            sendErrorResponse(request, "定时任务更新失败", 500);
        }
    }
}

void APIHandlers::handleDeleteTimerTask(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    // 获取任务ID
    String taskId = request->pathArg(0);
    if (taskId.isEmpty()) {
        sendErrorResponse(request, "任务ID不能为空", 400);
        return;
    }

    // 获取定时器管理器
    SystemManager* systemManager = getSystemManager();
    if (!systemManager) {
        sendErrorResponse(request, "系统管理器未初始化", 500);
        return;
    }

    TimerManagerBackend* timerManager = systemManager->getTimerManager();
    if (!timerManager) {
        sendErrorResponse(request, "定时器管理器未初始化", 500);
        return;
    }

    // 检查任务是否存在
    TimerTask* existingTaskPtr = timerManager->getTask(taskId);
    if (!existingTaskPtr) {
        sendErrorResponse(request, "定时任务不存在", 404);
        return;
    }

    TimerTask existingTask = *existingTaskPtr;

    // 删除定时任务
    if (timerManager->deleteTask(taskId)) {
        JsonDocument responseData;
        responseData["taskId"] = taskId;
        responseData["name"] = existingTask.name;

        sendSuccessResponse(request, responseData, "定时任务删除成功", startTime);

        // 通知WebSocket客户端
        JsonDocument eventData;
        eventData["taskId"] = taskId;
        eventData["name"] = existingTask.name;
        systemManager->sendSystemEvent("timer_task_deleted", eventData);
    } else {
        sendErrorResponse(request, "定时任务删除失败", 500);
    }
}

void APIHandlers::handleTimerEnable(AsyncWebServerRequest *request, uint8_t *data,
                                   size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (index + len == total) {
        JsonDocument doc;
        DeserializationError error = deserializeJson(doc, (char*)data);

        if (error) {
            sendErrorResponse(request, "JSON解析失败", 400);
            return;
        }

        if (!doc["enabled"].is<bool>()) {
            sendErrorResponse(request, "缺少enabled字段", 400);
            return;
        }

        bool enabled = doc["enabled"];

        // 获取定时器管理器
        SystemManager* systemManager = getSystemManager();
        if (!systemManager) {
            sendErrorResponse(request, "系统管理器未初始化", 500);
            return;
        }

        TimerManagerBackend* timerManager = systemManager->getTimerManager();
        if (!timerManager) {
            sendErrorResponse(request, "定时器管理器未初始化", 500);
            return;
        }

        // 启用或禁用定时器系统
        bool success = timerManager->setMasterEnabled(enabled);

        if (success) {
            JsonDocument responseDoc;
            responseDoc["success"] = true;
            responseDoc["data"]["enabled"] = enabled;

            String message = enabled ? "定时器系统已启用" : "定时器系统已禁用";
            sendSuccessResponse(request, responseDoc, message, startTime);

            // 通知WebSocket客户端
            JsonDocument eventData;
            eventData["enabled"] = enabled;
            systemManager->sendSystemEvent("timer_system_toggled", eventData);
        } else {
            String errorMsg = enabled ? "启用定时器系统失败" : "禁用定时器系统失败";
            sendErrorResponse(request, errorMsg, 500);
        }
    }
}

void APIHandlers::handleEmitSignal(AsyncWebServerRequest *request, uint8_t *data,
                                   size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    Serial.printf("📤 [API] 处理信号发射请求: len=%d, index=%d, total=%d\n", len, index, total);

    try {
        if (!requestMiddleware(request)) {
            return;
        }

        // 解析JSON请求体
        JsonDocument doc;
        if (!parseJsonBody(data, len, doc)) {
            sendErrorResponse(request, "JSON解析失败", 400);
            return;
        }
    
    IRSignalManager* irManager = getIRManager();
    if (!irManager) {
        sendErrorResponse(request, "红外管理器未初始化", 500);
        return;
    }
    
    String signalId = doc["signalId"].as<String>();
    if (signalId.isEmpty()) {
        sendErrorResponse(request, "信号ID缺失", 400);
        return;
    }
    
    // 检查信号是否存在
    SignalStorage* storage = getSignalStorage();
    if (!storage) {
        sendErrorResponse(request, "信号存储未初始化", 500);
        return;
    }
    
    SignalData signal = storage->getSignal(signalId);
    if (signal.id.isEmpty()) {
        sendErrorResponse(request, "信号不存在", 404);
        return;
    }
    
    // 发射信号
    if (irManager->emitSignal(signalId)) {
        // 更新信号统计
        signal.lastSent = millis();
        signal.sentCount++;
        storage->updateSignal(signal);
        
        JsonDocument responseData;
        responseData["signal_id"] = signalId;
        responseData["signal_name"] = signal.name;
        responseData["sent_count"] = signal.sentCount;
        responseData["last_sent"] = signal.lastSent;
        
        sendSuccessResponse(request, responseData, "信号发射成功", startTime);
        
        // 发送WebSocket事件
        SystemManager* systemManager = getSystemManager();
        if (systemManager) {
            JsonDocument eventData;
            eventData["signal_id"] = signalId;
            eventData["signal_name"] = signal.name;
            eventData["timestamp"] = signal.lastSent;
            systemManager->sendSystemEvent("signal_sent", eventData);
        }
    } else {
        sendErrorResponse(request, "信号发射失败", 500);
    }
    
        uint32_t processingTime = millis() - startTime;
        logRequest(request, 200, processingTime);

    } catch (const std::exception& e) {
        Serial.printf("❌ [API] 信号发射异常: %s\n", e.what());
        sendErrorResponse(request, "信号发射处理异常", 500);
    } catch (...) {
        Serial.println("❌ [API] 信号发射未知异常");
        sendErrorResponse(request, "信号发射处理失败", 500);
    }
}

// ==================== 批量操作API ====================

void APIHandlers::handleBatchOperation(AsyncWebServerRequest *request, uint8_t *data,
                                       size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    Serial.printf("📦 [API] 处理批量操作请求: len=%d, index=%d, total=%d\n", len, index, total);

    try {
        if (!requestMiddleware(request)) {
            return;
        }

        // 解析JSON请求体
        JsonDocument doc;
        if (!parseJsonBody(data, len, doc)) {
            sendErrorResponse(request, "JSON解析失败", 400);
            return;
        }
    
    JsonArray operations = doc["operations"];
    if (operations.size() == 0) {
        sendErrorResponse(request, "操作列表为空", 400);
        return;
    }
    
    uint8_t priority = doc["priority"].as<uint8_t>();
    if (priority == 0) priority = 2;  // 默认优先级
    
    bool isLoopMode = doc["isLoopMode"].as<bool>();
    uint32_t emitRate = doc["emitRate"].as<uint32_t>();
    uint32_t intervalRate = doc["intervalRate"].as<uint32_t>();
    
    if (emitRate == 0) emitRate = 1000;      // 默认1秒
    if (intervalRate == 0) intervalRate = 500; // 默认0.5秒
    
    TaskScheduler* scheduler = getTaskScheduler();
    if (!scheduler) {
        sendErrorResponse(request, "任务调度器未初始化", 500);
        return;
    }
    
    // 创建批量任务
    String taskId = scheduler->addBatchTask(operations, priority);
    
    if (!taskId.isEmpty()) {
        JsonDocument responseData;
        responseData["task_id"] = taskId;
        responseData["operation_count"] = operations.size();
        responseData["priority"] = priority;
        responseData["is_loop_mode"] = isLoopMode;
        responseData["emit_rate"] = emitRate;
        responseData["interval_rate"] = intervalRate;
        
        sendSuccessResponse(request, responseData, "批量任务创建成功", startTime);
        
        // 发送WebSocket事件
        SystemManager* systemManager = getSystemManager();
        if (systemManager) {
            JsonDocument eventData;
            eventData["task_id"] = taskId;
            eventData["operation_count"] = operations.size();
            systemManager->sendSystemEvent("batch_task_created", eventData);
        }
        } else {
            sendErrorResponse(request, "批量任务创建失败", 500);
        }

        uint32_t processingTime = millis() - startTime;
        logRequest(request, 200, processingTime);

    } catch (const std::exception& e) {
        Serial.printf("❌ [API] 批量操作异常: %s\n", e.what());
        sendErrorResponse(request, "批量操作处理异常", 500);
    } catch (...) {
        Serial.println("❌ [API] 批量操作未知异常");
        sendErrorResponse(request, "批量操作处理失败", 500);
    }
}

// ==================== 工具方法实现 ====================

void APIHandlers::sendSuccessResponse(AsyncWebServerRequest *request,
                                     const JsonDocument& data,
                                     const String& message,
                                     uint32_t requestStartTime) {
    // 计算真实的响应时间
    uint32_t processingTime = millis() - requestStartTime;

    Serial.printf("📤 [API] 发送成功响应: %s (处理时间: %d ms)\n", message.c_str(), processingTime);

    APIResponse response = APIResponse::createSuccess(data, message, processingTime);
    String jsonResponse = response.toJsonString();

    Serial.printf("📤 [API] 响应JSON: %s\n", jsonResponse.c_str());
    Serial.println("🚀 [API] 正在发送200状态码响应...");
    request->send(200, "application/json", jsonResponse);
    Serial.println("📤 [API] 200响应已发送到客户端");
}

void APIHandlers::sendErrorResponse(AsyncWebServerRequest *request,
                                   const String& error,
                                   int code,
                                   const String& message) {
    // 简单的处理时间计算 - 使用固定值，避免复杂的时间追踪
    uint32_t processingTime = 1; // 默认1ms，表示处理完成

    Serial.printf("📤 [API] 发送错误响应: %s (状态码: %d)\n", error.c_str(), code);

    APIResponse response = APIResponse::createError(error, message, processingTime);
    String jsonResponse = response.toJsonString();

    Serial.printf("📤 [API] 错误响应JSON: %s\n", jsonResponse.c_str());
    request->send(code, "application/json", jsonResponse);
    Serial.println("📤 [API] 错误响应已发送到客户端");
}

bool APIHandlers::parseJsonBody(uint8_t *data, size_t len, JsonDocument& doc) {
    if (len == 0 || data == nullptr) {
        return false;
    }
    
    DeserializationError error = deserializeJson(doc, data, len);
    if (error) {
        Serial.printf("❌ JSON解析错误: %s\n", error.c_str());
        return false;
    }
    
    return true;
}

bool APIHandlers::validateRequest(AsyncWebServerRequest *request, 
                                 const std::vector<String>& requiredParams) {
    for (const String& param : requiredParams) {
        if (!request->hasParam(param)) {
            Serial.printf("⚠️ 缺少必需参数: %s\n", param.c_str());
            return false;
        }
    }
    return true;
}

void APIHandlers::logRequest(AsyncWebServerRequest *request, int responseCode, uint32_t processingTime) {
    IPAddress clientIP = getClientIP(request);
    String method = (request->method() == HTTP_GET) ? "GET" :
                   (request->method() == HTTP_POST) ? "POST" :
                   (request->method() == HTTP_PUT) ? "PUT" :
                   (request->method() == HTTP_DELETE) ? "DELETE" : "OTHER";
    
    if (DEBUG_LEVEL >= 3) {
        Serial.printf("📝 %s %s - %d (%d ms) [%s]\n",
                      method.c_str(),
                      request->url().c_str(),
                      responseCode,
                      processingTime,
                      clientIP.toString().c_str());
    }
}

IPAddress APIHandlers::getClientIP(AsyncWebServerRequest *request) {
    // 检查X-Forwarded-For头（代理情况）
    if (request->hasHeader("X-Forwarded-For")) {
        String forwardedFor = request->getHeader("X-Forwarded-For")->value();
        // 取第一个IP地址
        int commaIndex = forwardedFor.indexOf(',');
        if (commaIndex > 0) {
            forwardedFor = forwardedFor.substring(0, commaIndex);
        }
        IPAddress ip;
        if (ip.fromString(forwardedFor)) {
            return ip;
        }
    }
    
    // 检查X-Real-IP头
    if (request->hasHeader("X-Real-IP")) {
        String realIP = request->getHeader("X-Real-IP")->value();
        IPAddress ip;
        if (ip.fromString(realIP)) {
            return ip;
        }
    }
    
    // 返回直接连接的客户端IP
    return request->client()->remoteIP();
}

void APIHandlers::handleOptions(AsyncWebServerRequest *request) {
    AsyncWebServerResponse *response = request->beginResponse(200);
    response->addHeader("Access-Control-Allow-Origin", "*");
    response->addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    response->addHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
    response->addHeader("Access-Control-Max-Age", "86400");
    request->send(response);
}

void APIHandlers::handleNotFound(AsyncWebServerRequest *request) {
    JsonDocument errorDoc;
    errorDoc["error"] = "API端点不存在";
    errorDoc["path"] = request->url();
    errorDoc["method"] = (request->method() == HTTP_GET) ? "GET" :
                        (request->method() == HTTP_POST) ? "POST" :
                        (request->method() == HTTP_PUT) ? "PUT" :
                        (request->method() == HTTP_DELETE) ? "DELETE" : "OTHER";
    
    APIResponse response = APIResponse::createError("API端点不存在", "请检查请求路径和方法");
    String jsonResponse = response.toJsonString();
    
    request->send(404, "application/json", jsonResponse);
    
    logRequest(request, 404, 0);
}

bool APIHandlers::requestMiddleware(AsyncWebServerRequest *request) {
    // 检查可用内存
    size_t freeHeap = ESP.getFreeHeap();
    if (freeHeap < 15000) {  // 需要至少15KB可用内存
        Serial.printf("❌ [API] 内存不足: %d bytes\n", freeHeap);
        sendErrorResponse(request, "系统内存不足，请稍后重试", 503);
        return false;
    }

    // 检查内容类型（对于POST/PUT请求）
    if (request->method() == HTTP_POST || request->method() == HTTP_PUT) {
        if (!request->hasHeader("Content-Type")) {
            sendErrorResponse(request, "缺少Content-Type头", 400);
            return false;
        }

        String contentType = request->getHeader("Content-Type")->value();
        if (contentType.indexOf("application/json") == -1) {
            sendErrorResponse(request, "不支持的Content-Type，需要application/json", 400);
            return false;
        }
    }
    
    // 记录请求开始
    if (DEBUG_LEVEL >= 4) {
        IPAddress clientIP = getClientIP(request);
        Serial.printf("🔍 请求开始: %s %s [%s]\n",
                      (request->method() == HTTP_GET) ? "GET" :
                      (request->method() == HTTP_POST) ? "POST" :
                      (request->method() == HTTP_PUT) ? "PUT" :
                      (request->method() == HTTP_DELETE) ? "DELETE" : "OTHER",
                      request->url().c_str(),
                      clientIP.toString().c_str());
    }
    
    return true;
}

SystemManager* APIHandlers::getSystemManager() {
    return &SystemManager::getInstance();
}

IRSignalManager* APIHandlers::getIRManager() {
    SystemManager* systemManager = getSystemManager();
    return systemManager ? systemManager->getIRManager() : nullptr;
}

SignalStorage* APIHandlers::getSignalStorage() {
    SystemManager* systemManager = getSystemManager();
    return systemManager ? systemManager->getSignalStorage() : nullptr;
}

TaskScheduler* APIHandlers::getTaskScheduler() {
    return &TaskScheduler::getInstance();
}

// ==================== 辅助函数实现 ====================

bool APIHandlers::validateSignalData(const JsonObject& signal) {
    // 检查必需字段
    if (!signal["id"].is<const char*>() || signal["id"].as<String>().isEmpty()) {
        return false;
    }

    if (!signal["name"].is<const char*>() || signal["name"].as<String>().isEmpty()) {
        return false;
    }

    if (!signal["type"].is<const char*>() || signal["type"].as<String>().isEmpty()) {
        return false;
    }

    if (!signal["protocol"].is<const char*>() || signal["protocol"].as<String>().isEmpty()) {
        return false;
    }

    if (!signal["signalCode"].is<const char*>() || signal["signalCode"].as<String>().isEmpty()) {
        return false;
    }

    // 检查数据格式
    String signalId = signal["id"].as<String>();
    if (signalId.length() > 32) {  // ID长度限制
        return false;
    }

    String signalName = signal["name"].as<String>();
    if (signalName.length() > 64) {  // 名称长度限制
        return false;
    }

    // 检查频率格式（字符串）
    String frequency = signal["frequency"].as<String>();
    if (frequency.isEmpty()) {
        return false;
    }

    return true;
}

void APIHandlers::processBatchRequest(const JsonObject& request, JsonObject& response) {
    String endpoint = request["endpoint"].as<String>();
    String method = request["method"].as<String>();
    String requestId = request["id"].as<String>();

    response["id"] = requestId;
    response["endpoint"] = endpoint;
    response["method"] = method;
    response["timestamp"] = getCurrentUnixMs();

    // 根据端点和方法处理请求
    if (endpoint == "/api/signals" && method == "GET") {
        // 获取信号列表
        SignalStorage* storage = getSignalStorage();
        if (storage) {
            response["success"] = true;
            JsonArray signalsArray = response["data"]["signals"].to<JsonArray>();

            std::vector<SignalData> allSignals = storage->getAllSignals();
            for (const SignalData& signal : allSignals) {
                JsonObject signalObj = signalsArray.add<JsonObject>();
                signalObj["id"] = signal.id;
                signalObj["name"] = signal.name;
                signalObj["type"] = signal.type;
                signalObj["protocol"] = signal.protocol;
                signalObj["frequency"] = signal.frequency;
                signalObj["created"] = signal.created;
                signalObj["lastSent"] = signal.lastSent;
                signalObj["sentCount"] = signal.sentCount;
            }

            response["data"]["total"] = allSignals.size();
            response["message"] = "信号列表获取成功";
        } else {
            response["success"] = false;
            response["error"] = "信号存储系统未初始化";
        }

    } else if (endpoint == "/api/status" && method == "GET") {
        // 获取系统状态
        SystemManager* systemManager = getSystemManager();
        if (systemManager) {
            response["success"] = true;
            JsonObject data = response["data"].to<JsonObject>();
            data["uptime"] = millis() / 1000;
            data["free_heap"] = ESP.getFreeHeap();
            data["chip_temperature"] = temperatureRead();
            data["wifi_strength"] = WiFi.RSSI();
            data["memory_usage"] = ((ESP.getHeapSize() - ESP.getFreeHeap()) * 100.0) / ESP.getHeapSize();
            response["message"] = "系统状态获取成功";
        } else {
            response["success"] = false;
            response["error"] = "系统管理器未初始化";
        }

    } else if (endpoint == "/api/timer/status" && method == "GET") {
        // 获取定时器状态
        SystemManager* systemManager = getSystemManager();
        if (systemManager) {
            TimerManagerBackend* timerManager = systemManager->getTimerManager();
            if (timerManager) {
                response["success"] = true;
                JsonObject data = response["data"].to<JsonObject>();
                data["enabled"] = timerManager->isMasterEnabled();
                data["active_tasks"] = timerManager->getActiveTaskCount();
                response["message"] = "定时器状态获取成功";
            } else {
                response["success"] = false;
                response["error"] = "定时器管理器未初始化";
            }
        } else {
            response["success"] = false;
            response["error"] = "系统管理器未初始化";
        }

    } else if (endpoint == "/api/timer/tasks" && method == "GET") {
        // 获取定时任务列表
        SystemManager* systemManager = getSystemManager();
        if (systemManager) {
            TimerManagerBackend* timerManager = systemManager->getTimerManager();
            if (timerManager) {
                response["success"] = true;
                JsonArray tasksArray = response["data"]["tasks"].to<JsonArray>();
                // 这里需要实现获取所有任务的方法
                response["data"]["total"] = 0;
                response["message"] = "定时任务列表获取成功";
            } else {
                response["success"] = false;
                response["error"] = "定时器管理器未初始化";
            }
        } else {
            response["success"] = false;
            response["error"] = "系统管理器未初始化";
        }

    } else if (endpoint == "/api/groups" && method == "GET") {
        // 获取信号分组列表
        SignalStorage* storage = getSignalStorage();
        if (storage) {
            response["success"] = true;
            JsonArray groupsArray = response["data"]["groups"].to<JsonArray>();

            // 获取所有信号
            std::vector<SignalData> allSignals = storage->getAllSignals();

            // 统计各类型信号数量
            std::map<String, int> typeCounts;
            for (const SignalData& signal : allSignals) {
                typeCounts[signal.type]++;
            }

            // 创建类型分组
            const char* typeNames[] = {"tv", "ac", "fan", "light", "other"};
            const char* typeDisplayNames[] = {"电视", "空调", "风扇", "灯光", "其他"};

            for (int i = 0; i < 5; i++) {
                JsonObject groupObj = groupsArray.add<JsonObject>();
                groupObj["id"] = String("type_") + typeNames[i];
                groupObj["name"] = typeDisplayNames[i];
                groupObj["type"] = "system";
                groupObj["signal_count"] = typeCounts[typeNames[i]];
            }

            response["data"]["total"] = 5;
            response["message"] = "信号分组列表获取成功";
        } else {
            response["success"] = false;
            response["error"] = "信号存储系统未初始化";
        }

    } else if (endpoint == "/api/system/stats" && method == "GET") {
        // 获取系统统计信息
        SystemManager* systemManager = getSystemManager();
        SignalStorage* storage = getSignalStorage();

        if (systemManager && storage) {
            response["success"] = true;
            JsonObject data = response["data"].to<JsonObject>();

            // 系统统计
            data["uptime"] = millis() / 1000;
            data["free_heap"] = ESP.getFreeHeap();
            data["total_heap"] = ESP.getHeapSize();
            data["memory_usage"] = ((ESP.getHeapSize() - ESP.getFreeHeap()) * 100.0) / ESP.getHeapSize();
            data["chip_temperature"] = temperatureRead();
            data["wifi_strength"] = WiFi.RSSI();

            // 信号统计
            std::vector<SignalData> allSignals = storage->getAllSignals();
            data["total_signals"] = allSignals.size();

            // 按类型统计信号
            JsonObject signalsByType = data["signals_by_type"].to<JsonObject>();
            std::map<String, int> typeCounts;
            for (const SignalData& signal : allSignals) {
                typeCounts[signal.type]++;
            }
            signalsByType["tv"] = typeCounts["tv"];
            signalsByType["ac"] = typeCounts["ac"];
            signalsByType["fan"] = typeCounts["fan"];
            signalsByType["light"] = typeCounts["light"];
            signalsByType["other"] = typeCounts["other"];

            // 定时器统计
            TimerManagerBackend* timerManager = systemManager->getTimerManager();
            if (timerManager) {
                data["timer_enabled"] = timerManager->isMasterEnabled();
                data["active_timer_tasks"] = timerManager->getActiveTaskCount();
            }

            response["message"] = "系统统计信息获取成功";
        } else {
            response["success"] = false;
            response["error"] = "系统组件未初始化";
        }

    } else if (endpoint.startsWith("/api/signals/") && method == "GET") {
        // 获取单个信号详情
        String signalId = endpoint.substring(13);  // 去掉 "/api/signals/" 前缀

        SignalStorage* storage = getSignalStorage();
        if (storage) {
            SignalData signal = storage->getSignal(signalId);
            if (!signal.id.isEmpty()) {
                response["success"] = true;
                JsonObject signalObj = response["data"]["signal"].to<JsonObject>();
                signalObj["id"] = signal.id;
                signalObj["name"] = signal.name;
                signalObj["type"] = signal.type;
                signalObj["protocol"] = signal.protocol;
                signalObj["data"] = signal.data;
                signalObj["signalCode"] = signal.signalCode;
                signalObj["frequency"] = signal.frequency;
                signalObj["description"] = signal.description;
                signalObj["isLearned"] = signal.isLearned;
                signalObj["created"] = signal.created;
                signalObj["lastSent"] = signal.lastSent;
                signalObj["sentCount"] = signal.sentCount;
                response["message"] = "信号详情获取成功";
            } else {
                response["success"] = false;
                response["error"] = "信号不存在";
            }
        } else {
            response["success"] = false;
            response["error"] = "信号存储系统未初始化";
        }

    } else {
        response["success"] = false;
        response["error"] = "不支持的批量请求: " + method + " " + endpoint;
        response["supported_endpoints"] = JsonArray();
        JsonArray supportedEndpoints = response["supported_endpoints"].as<JsonArray>();
        supportedEndpoints.add("GET /api/signals");
        supportedEndpoints.add("GET /api/status");
        supportedEndpoints.add("GET /api/timer/status");
        supportedEndpoints.add("GET /api/timer/tasks");
        supportedEndpoints.add("GET /api/groups");
        supportedEndpoints.add("GET /api/system/stats");
        supportedEndpoints.add("GET /api/signals/{id}");
    }

    // 添加处理时间
    response["processing_time"] = millis() - response["timestamp"].as<uint64_t>();
}
