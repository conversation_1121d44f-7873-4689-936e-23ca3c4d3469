#!/usr/bin/env python3
"""
前端文件gzip压缩脚本
为ESP32 LittleFS生成压缩文件，优化传输性能
"""

import os
import gzip
import shutil
from pathlib import Path

def compress_file(source_path, target_path):
    """压缩单个文件"""
    try:
        with open(source_path, 'rb') as f_in:
            with gzip.open(target_path, 'wb', compresslevel=9) as f_out:
                shutil.copyfileobj(f_in, f_out)
        
        # 获取压缩比
        original_size = os.path.getsize(source_path)
        compressed_size = os.path.getsize(target_path)
        ratio = (1 - compressed_size / original_size) * 100
        
        print(f"✅ {source_path.name}: {original_size:,} → {compressed_size:,} bytes ({ratio:.1f}% 减少)")
        return True
    except Exception as e:
        print(f"❌ 压缩失败 {source_path}: {e}")
        return False

def should_compress(file_path):
    """判断文件是否需要压缩"""
    # 需要压缩的文件类型
    compress_extensions = {'.html', '.css', '.js', '.json', '.svg', '.txt', '.md'}
    
    # 排除的文件
    exclude_files = {'api-test.html', 'button-test.html'}
    
    if file_path.name in exclude_files:
        return False
    
    if file_path.suffix.lower() in compress_extensions:
        # 只压缩大于1KB的文件
        return file_path.stat().st_size > 1024
    
    return False

def compress_frontend():
    """压缩前端文件"""
    data_dir = Path('data')
    
    if not data_dir.exists():
        print("❌ data目录不存在")
        return False
    
    print("🗜️  开始压缩前端文件...")
    
    compressed_count = 0
    total_original = 0
    total_compressed = 0
    
    # 遍历所有文件
    for file_path in data_dir.rglob('*'):
        if file_path.is_file() and should_compress(file_path):
            gz_path = file_path.with_suffix(file_path.suffix + '.gz')
            
            # 如果.gz文件已存在且比原文件新，跳过
            if gz_path.exists() and gz_path.stat().st_mtime > file_path.stat().st_mtime:
                continue
            
            if compress_file(file_path, gz_path):
                compressed_count += 1
                total_original += file_path.stat().st_size
                total_compressed += gz_path.stat().st_size
    
    if compressed_count > 0:
        total_ratio = (1 - total_compressed / total_original) * 100
        print(f"\n🎉 压缩完成!")
        print(f"📊 压缩了 {compressed_count} 个文件")
        print(f"📈 总大小: {total_original:,} → {total_compressed:,} bytes")
        print(f"💾 节省空间: {total_ratio:.1f}%")
    else:
        print("ℹ️  没有需要压缩的文件")
    
    return True

if __name__ == '__main__':
    compress_frontend()
