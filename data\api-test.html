<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            font-weight: 500;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 ESP32 API连接测试</h1>
        
        <div class="test-result info">
            <strong>当前地址:</strong> <span id="current-url"></span><br>
            <strong>API基础URL:</strong> <span id="api-base-url"></span><br>
            <strong>WebSocket URL:</strong> <span id="ws-url"></span>
        </div>

        <button class="btn" onclick="testAPI()">测试API连接</button>
        <button class="btn" onclick="testWebSocket()">测试WebSocket</button>
        <button class="btn" onclick="clearResults()">清空结果</button>

        <div id="results"></div>
    </div>

    <script>
        // 显示连接信息
        const hostname = window.location.hostname;
        const apiBaseURL = `http://${hostname}:8000`;
        const wsURL = `ws://${hostname}:8001/ws`;
        
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('api-base-url').textContent = apiBaseURL;
        document.getElementById('ws-url').textContent = wsURL;

        function addResult(message, type, data = null) {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            
            if (data) {
                const pre = document.createElement('pre');
                pre.textContent = JSON.stringify(data, null, 2);
                div.appendChild(pre);
            }
            
            document.getElementById('results').appendChild(div);
        }

        async function testAPI() {
            addResult('🔍 开始测试API连接...', 'info');
            
            // 测试基本连接
            try {
                const response = await fetch(`${apiBaseURL}/api/status`);
                
                addResult(`📡 HTTP状态: ${response.status} ${response.statusText}`, 
                         response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ API响应成功', 'success', data);
                } else {
                    const text = await response.text();
                    addResult('❌ API响应失败', 'error', { responseText: text });
                }
                
            } catch (error) {
                addResult(`❌ API连接失败: ${error.message}`, 'error');
                
                // 详细错误信息
                addResult('🔧 错误详情:', 'info', {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                });
            }
            
            // 测试其他API端点
            const endpoints = [
                '/api/system/stats',
                '/api/signals',
                '/api/timer/status'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${apiBaseURL}${endpoint}`);
                    addResult(`📍 ${endpoint}: ${response.status}`, 
                             response.ok ? 'success' : 'error');
                } catch (error) {
                    addResult(`📍 ${endpoint}: 连接失败`, 'error');
                }
            }
        }

        function testWebSocket() {
            addResult('🔌 开始测试WebSocket连接...', 'info');
            
            try {
                const ws = new WebSocket(wsURL);
                
                ws.onopen = () => {
                    addResult('✅ WebSocket连接成功', 'success');
                    ws.close();
                };
                
                ws.onmessage = (event) => {
                    addResult('📨 收到WebSocket消息', 'info', JSON.parse(event.data));
                };
                
                ws.onerror = (error) => {
                    addResult('❌ WebSocket连接错误', 'error');
                };
                
                ws.onclose = (event) => {
                    addResult(`🔌 WebSocket连接关闭: ${event.code} ${event.reason}`, 'info');
                };
                
                // 超时处理
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        ws.close();
                        addResult('⏰ WebSocket连接超时', 'error');
                    }
                }, 5000);
                
            } catch (error) {
                addResult(`❌ WebSocket测试失败: ${error.message}`, 'error');
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 页面加载时显示环境信息
        window.onload = function() {
            addResult('🌐 环境信息', 'info', {
                userAgent: navigator.userAgent,
                hostname: hostname,
                protocol: location.protocol,
                port: location.port || '默认端口'
            });
        };
    </script>
</body>
</html>
