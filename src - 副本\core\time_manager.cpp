#include "time_manager.h"

// 静态成员初始化
TimeManager* TimeManager::instance = nullptr;
TimeManager* timeManager = nullptr;

TimeManager::TimeManager() 
    : isInitialized(false)
    , isNTPSynced(false)
    , lastSyncAttempt(0)
    , syncInterval(3600000)  // 1小时同步一次
    , timezone("CST-8")      // 中国标准时间
    , ntpServer1("pool.ntp.org")
    , ntpServer2("time.nist.gov")
    , ntpServer3("time.cloudflare.com")
    , lastSyncTime(0)
    , bootTimeOffset(0)
{
}

TimeManager::~TimeManager() {
}

TimeManager* TimeManager::getInstance() {
    if (instance == nullptr) {
        instance = new TimeManager();
    }
    return instance;
}

bool TimeManager::begin() {
    if (isInitialized) {
        return true;
    }
    
    Serial.println("🕐 初始化时间管理器...");
    
    // 设置时区
    setenv("TZ", timezone.c_str(), 1);
    tzset();
    
    // 配置NTP服务器
    configTime(0, 0, ntpServer1.c_str(), ntpServer2.c_str(), ntpServer3.c_str());
    
    Serial.printf("🌐 NTP服务器: %s, %s, %s\n", 
                  ntpServer1.c_str(), ntpServer2.c_str(), ntpServer3.c_str());
    Serial.printf("🌍 时区: %s\n", timezone.c_str());
    
    isInitialized = true;

    Serial.println("✅ 时间管理器初始化完成，等待WiFi连接后同步NTP");

    return true;
}

void TimeManager::setTimezone(const String& tz) {
    timezone = tz;
    if (isInitialized) {
        setenv("TZ", timezone.c_str(), 1);
        tzset();
        Serial.printf("🌍 时区已更新: %s\n", timezone.c_str());
    }
}

void TimeManager::setNTPServers(const String& server1, const String& server2, const String& server3) {
    ntpServer1 = server1;
    if (!server2.isEmpty()) ntpServer2 = server2;
    if (!server3.isEmpty()) ntpServer3 = server3;
    
    if (isInitialized) {
        configTime(0, 0, ntpServer1.c_str(), ntpServer2.c_str(), ntpServer3.c_str());
        Serial.printf("🌐 NTP服务器已更新: %s, %s, %s\n", 
                      ntpServer1.c_str(), ntpServer2.c_str(), ntpServer3.c_str());
    }
}

bool TimeManager::syncWithNTP() {
    if (!WiFi.isConnected()) {
        Serial.println("⚠️ WiFi未连接，无法同步NTP时间");
        return false;
    }
    
    uint32_t currentTime = millis();
    
    // 避免频繁同步
    if (currentTime - lastSyncAttempt < 30000) {  // 30秒内不重复同步
        return isNTPSynced;
    }
    
    lastSyncAttempt = currentTime;
    
    Serial.println("🕐 开始NTP时间同步...");
    
    // 等待时间同步
    if (waitForTimeSync(10000)) {
        time(&lastSyncTime);
        updateBootTimeOffset();
        isNTPSynced = true;
        
        Serial.printf("✅ NTP时间同步成功: %s\n", getFormattedTime().c_str());
        return true;
    } else {
        Serial.println("❌ NTP时间同步失败");
        return false;
    }
}

void TimeManager::loop() {
    if (!isInitialized || !WiFi.isConnected()) {
        return;
    }
    
    uint32_t currentTime = millis();
    
    // 定期同步时间
    if (currentTime - lastSyncAttempt >= syncInterval) {
        syncWithNTP();
    }
}

uint64_t TimeManager::getUnixTimestamp() {
    if (!isNTPSynced) {
        return 0;
    }
    
    time_t now;
    time(&now);
    return (uint64_t)now;
}

uint64_t TimeManager::getUnixTimestampMs() {
    if (!isNTPSynced) {
        // 如果没有同步，返回基于启动时间的估算值
        return bootTimeOffset + millis();
    }
    
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)tv.tv_sec * 1000 + tv.tv_usec / 1000;
}

String TimeManager::getFormattedTime(const String& format) {
    if (!isNTPSynced) {
        return "未同步";
    }
    
    time_t now;
    time(&now);
    
    char buffer[64];
    strftime(buffer, sizeof(buffer), format.c_str(), localtime(&now));
    return String(buffer);
}

String TimeManager::getISO8601Time() {
    return getFormattedTime("%Y-%m-%dT%H:%M:%S");
}

uint32_t TimeManager::getTimeSinceSync() const {
    if (!isNTPSynced) {
        return 0;
    }
    
    time_t now;
    time(&now);
    return (uint32_t)(now - lastSyncTime);
}

void TimeManager::updateBootTimeOffset() {
    if (isNTPSynced) {
        uint64_t currentUnixMs = getUnixTimestampMs();
        uint32_t currentMillis = millis();
        bootTimeOffset = currentUnixMs - currentMillis;
    }
}

bool TimeManager::waitForTimeSync(uint32_t timeout_ms) {
    uint32_t startTime = millis();
    
    while (millis() - startTime < timeout_ms) {
        if (getLocalTime(&timeinfo)) {
            return true;
        }
        delay(100);
    }
    
    return false;
}

uint64_t TimeManager::millisToUnixMs(uint32_t millis_time) {
    TimeManager* tm = getInstance();
    if (tm && tm->isNTPSynced) {
        return tm->bootTimeOffset + millis_time;
    }
    return millis_time;  // 回退到相对时间
}

String TimeManager::formatUnixTime(uint64_t unix_ms, const String& format) {
    time_t timestamp = unix_ms / 1000;
    char buffer[64];
    strftime(buffer, sizeof(buffer), format.c_str(), localtime(&timestamp));
    return String(buffer);
}

// 全局便捷函数
uint64_t getCurrentUnixMs() {
    if (timeManager && timeManager->isTimeSynced()) {
        return timeManager->getUnixTimestampMs();
    }

    // 修复：即使未同步NTP，也返回合理的Unix时间戳
    // 使用2025年1月1日作为基准时间 + millis()
    const uint64_t EPOCH_2025_MS = 1735689600000ULL; // 2025-01-01 00:00:00 UTC
    return EPOCH_2025_MS + millis();
}

String getCurrentTimeString() {
    if (timeManager && timeManager->isTimeSynced()) {
        return timeManager->getFormattedTime();
    }
    return "未同步";
}
