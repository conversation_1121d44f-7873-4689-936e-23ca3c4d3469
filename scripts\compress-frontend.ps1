# 前端文件gzip压缩脚本 (PowerShell版本)
# 为ESP32 LittleFS生成压缩文件，优化传输性能

function Compress-File {
    param(
        [string]$SourcePath,
        [string]$TargetPath
    )
    
    try {
        # 读取原文件
        $sourceBytes = [System.IO.File]::ReadAllBytes($SourcePath)
        
        # 创建gzip压缩流
        $targetStream = [System.IO.File]::Create($TargetPath)
        $gzipStream = New-Object System.IO.Compression.GZipStream($targetStream, [System.IO.Compression.CompressionMode]::Compress)
        
        # 写入压缩数据
        $gzipStream.Write($sourceBytes, 0, $sourceBytes.Length)
        $gzipStream.Close()
        $targetStream.Close()
        
        # 计算压缩比
        $originalSize = (Get-Item $SourcePath).Length
        $compressedSize = (Get-Item $TargetPath).Length
        $ratio = [math]::Round((1 - $compressedSize / $originalSize) * 100, 1)
        
        Write-Host "✅ $(Split-Path $SourcePath -Leaf): $($originalSize.ToString('N0')) → $($compressedSize.ToString('N0')) bytes ($ratio% 减少)" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ 压缩失败 $SourcePath : $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Should-Compress {
    param([System.IO.FileInfo]$File)
    
    # 需要压缩的文件类型
    $compressExtensions = @('.html', '.css', '.js', '.json', '.svg', '.txt', '.md')
    
    # 排除的文件
    $excludeFiles = @('api-test.html', 'button-test.html')
    
    if ($excludeFiles -contains $File.Name) {
        return $false
    }
    
    if ($compressExtensions -contains $File.Extension.ToLower()) {
        # 只压缩大于1KB的文件
        return $File.Length -gt 1024
    }
    
    return $false
}

function Compress-Frontend {
    $dataDir = "data"
    
    if (-not (Test-Path $dataDir)) {
        Write-Host "❌ data目录不存在" -ForegroundColor Red
        return $false
    }
    
    Write-Host "🗜️  开始压缩前端文件..." -ForegroundColor Cyan
    
    $compressedCount = 0
    $totalOriginal = 0
    $totalCompressed = 0
    
    # 遍历所有文件
    Get-ChildItem -Path $dataDir -Recurse -File | ForEach-Object {
        if (Should-Compress $_) {
            $gzPath = $_.FullName + ".gz"
            
            # 如果.gz文件已存在且比原文件新，跳过
            if ((Test-Path $gzPath) -and ((Get-Item $gzPath).LastWriteTime -gt $_.LastWriteTime)) {
                return
            }
            
            if (Compress-File $_.FullName $gzPath) {
                $compressedCount++
                $totalOriginal += $_.Length
                $totalCompressed += (Get-Item $gzPath).Length
            }
        }
    }
    
    if ($compressedCount -gt 0) {
        $totalRatio = [math]::Round((1 - $totalCompressed / $totalOriginal) * 100, 1)
        Write-Host "`n🎉 压缩完成!" -ForegroundColor Green
        Write-Host "📊 压缩了 $compressedCount 个文件" -ForegroundColor Yellow
        Write-Host "📈 总大小: $($totalOriginal.ToString('N0')) → $($totalCompressed.ToString('N0')) bytes" -ForegroundColor Yellow
        Write-Host "💾 节省空间: $totalRatio%" -ForegroundColor Yellow
    }
    else {
        Write-Host "ℹ️  没有需要压缩的文件" -ForegroundColor Blue
    }
    
    return $true
}

# 运行压缩
Compress-Frontend
