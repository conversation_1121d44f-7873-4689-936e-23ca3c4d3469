#include "ir_manager.h"
#include "core/system_manager.h"
#include "storage/signal_storage_temp.h"
#include "network/websocket_manager.h"

IRSignalManager::IRSignalManager() 
    : irSend(nullptr)
    , irRecv(nullptr)
    , learningState(IDLE)
    , emitState(EMIT_IDLE)
    , learningStartTime(0)
    , learningTimeout(LEARNING_TIMEOUT)
    , lastEmitTime(0)
    , isLearningActive(false)
    , defaultFrequency(IR_FREQUENCY)
    , defaultDutyCycle(IR_DUTY_CYCLE)
    , emitRepeatCount(1)
    , irMutex(nullptr)
    , learningTaskHandle(nullptr)
    , totalSignalsLearned(0)
    , totalSignalsEmitted(0)
    , learningAttempts(0)
    , emitAttempts(0) {
}

IRSignalManager::~IRSignalManager() {
    stop();
}

bool IRSignalManager::begin() {
    Serial.println("📡 初始化红外管理器...");
    
    // 创建互斥锁
    irMutex = xSemaphoreCreateMutex();
    if (!irMutex) {
        Serial.println("❌ 红外互斥锁创建失败");
        return false;
    }
    
    // 初始化红外发射器
    irSend = new IRsend(IR_SEND_PIN);
    if (!irSend) {
        Serial.println("❌ 红外发射器创建失败");
        return false;
    }
    irSend->begin();
    
    // 初始化红外接收器
    irRecv = new IRrecv(IR_RECV_PIN, IR_BUFFER_SIZE, IR_TIMEOUT, true);
    if (!irRecv) {
        Serial.println("❌ 红外接收器创建失败");
        return false;
    }
    irRecv->enableIRIn();
    
    // 设置默认参数
    irSend->enableIROut(defaultFrequency, defaultDutyCycle);
    
    Serial.printf("✅ 红外管理器初始化完成\n");
    Serial.printf("📡 发射引脚: GPIO%d, 接收引脚: GPIO%d\n", IR_SEND_PIN, IR_RECV_PIN);
    Serial.printf("📶 默认频率: %d Hz, 占空比: %d%%\n", defaultFrequency, defaultDutyCycle);
    
    return true;
}

void IRSignalManager::loop() {
    if (xSemaphoreTake(irMutex, pdMS_TO_TICKS(10)) == pdTRUE) {
        // 处理学习状态
        if (isLearningActive) {
            processLearning();
        }
        
        xSemaphoreGive(irMutex);
    }
}

void IRSignalManager::stop() {
    Serial.println("🛑 停止红外管理器");
    
    // 停止学习
    stopLearning();
    
    // 清理资源
    if (irSend) {
        delete irSend;
        irSend = nullptr;
    }
    
    if (irRecv) {
        delete irRecv;
        irRecv = nullptr;
    }
    
    if (irMutex) {
        vSemaphoreDelete(irMutex);
        irMutex = nullptr;
    }
}

bool IRSignalManager::startLearning(uint32_t timeout, const String& signalId) {
    if (isLearningActive) {
        logIR("WARN", "学习已在进行中");
        return false;
    }
    
    if (xSemaphoreTake(irMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        learningState = WAITING_FOR_SIGNAL;
        isLearningActive = true;
        learningStartTime = millis();
        learningTimeout = timeout;
        currentLearningId = signalId.isEmpty() ? generateSignalId() : signalId;
        learningAttempts++;
        
        // 清空接收缓冲区
        while (irRecv->decode(&results)) {
            irRecv->resume();
        }
        
        xSemaphoreGive(irMutex);
        
        logIR("INFO", "开始信号学习 (超时: " + String(timeout) + "ms)");
        
        // 发送学习开始事件
        SystemManager& systemManager = SystemManager::getInstance();
        JsonDocument eventData;
        eventData["learning_started"] = true;
        eventData["signal_id"] = currentLearningId;
        eventData["timeout"] = timeout;
        eventData["timestamp"] = millis();
        systemManager.sendSystemEvent("learning_started", eventData);
        
        return true;
    }
    
    return false;
}

bool IRSignalManager::stopLearning() {
    if (!isLearningActive) {
        return true;
    }
    
    if (xSemaphoreTake(irMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        isLearningActive = false;
        learningState = IDLE;
        
        xSemaphoreGive(irMutex);
        
        logIR("INFO", "停止信号学习");
        
        // 发送学习停止事件
        SystemManager& systemManager = SystemManager::getInstance();
        JsonDocument eventData;
        eventData["learning_stopped"] = true;
        eventData["signal_id"] = currentLearningId;
        eventData["timestamp"] = millis();
        systemManager.sendSystemEvent("learning_stopped", eventData);
        
        return true;
    }
    
    return false;
}

JsonDocument IRSignalManager::getLearningStatus() const {
    JsonDocument doc;
    
    doc["is_learning"] = isLearningActive;
    doc["signal_id"] = currentLearningId;
    doc["timeout"] = learningTimeout;
    
    if (isLearningActive) {
        doc["elapsed_time"] = millis() - learningStartTime;
        doc["remaining_time"] = (learningTimeout > (millis() - learningStartTime)) ? 
                               (learningTimeout - (millis() - learningStartTime)) : 0;
    }
    
    // 状态字符串
    switch (learningState) {
        case IDLE:
            doc["state"] = "idle";
            break;
        case WAITING_FOR_SIGNAL:
            doc["state"] = "waiting";
            break;
        case PROCESSING_SIGNAL:
            doc["state"] = "processing";
            break;
        case LEARNING_COMPLETE:
            doc["state"] = "completed";
            break;
        case LEARNING_ERROR:
            doc["state"] = "error";
            break;
        case LEARNING_TIMEOUT:
            doc["state"] = "timeout";
            break;
        default:
            doc["state"] = "unknown";
            break;
    }
    
    return doc;
}

bool IRSignalManager::emitSignal(const String& signalId) {
    if (signalId.isEmpty()) {
        logIR("ERROR", "信号ID为空");
        return false;
    }
    
    emitAttempts++;
    
    // 从存储中获取信号数据
    SystemManager& systemManager = SystemManager::getInstance();
    SignalStorage* storage = systemManager.getSignalStorage();
    
    if (!storage) {
        logIR("ERROR", "信号存储未初始化");
        return false;
    }
    
    SignalData signal = storage->getSignal(signalId);
    if (signal.id.isEmpty()) {
        logIR("ERROR", "信号不存在: " + signalId);
        return false;
    }
    
    return emitRawSignal(signal);
}

bool IRSignalManager::emitRawSignal(const SignalData& signalData) {
    if (!validateSignalData(signalData)) {
        logIR("ERROR", "信号数据无效");
        return false;
    }
    
    if (xSemaphoreTake(irMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        emitState = EMIT_PREPARING;
        
        // 解析信号数据
        String protocol = signalData.protocol;
        uint64_t data = stringToUint64(signalData.signalCode);
        uint32_t frequency = signalData.frequency.toInt();
        
        if (frequency == 0) frequency = defaultFrequency;
        
        logIR("INFO", "发射信号: " + signalData.name + " (" + protocol + ")");
        
        emitState = EMIT_SENDING;
        bool success = false;
        
        // 根据协议发射信号
        decode_type_t protocolType = stringToProtocol(protocol);
        
        try {
            switch (protocolType) {
                case NEC:
                    irSend->sendNEC(data, 32);
                    success = true;
                    break;
                    
                case SONY:
                    irSend->sendSony(data, 12);
                    success = true;
                    break;
                    
                case RC5:
                    irSend->sendRC5(data, 13);
                    success = true;
                    break;
                    
                case RC6:
                    irSend->sendRC6(data, 20);
                    success = true;
                    break;
                    
                case SAMSUNG:
                    irSend->sendSAMSUNG(data, 32);
                    success = true;
                    break;
                    
                case LG:
                    irSend->sendLG(data, 28);
                    success = true;
                    break;
                    
                default:
                    // 尝试作为原始数据发送
                    logIR("WARN", "未知协议，尝试原始发送: " + protocol);
                    // 这里可以添加原始数据发送逻辑
                    success = false;
                    break;
            }
            
            if (success) {
                emitState = EMIT_COMPLETE;
                totalSignalsEmitted++;
                lastEmitTime = millis();
                
                logIR("INFO", "信号发射成功: " + signalData.name);
                sendEmitCompleteEvent(signalData.id, true);
            } else {
                emitState = EMIT_ERROR;
                logIR("ERROR", "信号发射失败: " + signalData.name);
                sendEmitCompleteEvent(signalData.id, false);
            }
            
        } catch (...) {
            emitState = EMIT_ERROR;
            success = false;
            logIR("ERROR", "信号发射异常: " + signalData.name);
            sendEmitCompleteEvent(signalData.id, false);
        }
        
        xSemaphoreGive(irMutex);
        return success;
    }

    return false;
}

bool IRSignalManager::emitProtocolSignal(const String& protocol, uint64_t data, uint16_t bits, uint32_t frequency) {
    if (xSemaphoreTake(irMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        decode_type_t protocolType = stringToProtocol(protocol);
        bool success = false;

        try {
            switch (protocolType) {
                case NEC:
                    irSend->sendNEC(data, bits);
                    success = true;
                    break;
                case SONY:
                    irSend->sendSony(data, bits);
                    success = true;
                    break;
                case RC5:
                    irSend->sendRC5(data, bits);
                    success = true;
                    break;
                default:
                    logIR("ERROR", "不支持的协议: " + protocol);
                    break;
            }
        } catch (...) {
            logIR("ERROR", "协议信号发射异常");
        }

        xSemaphoreGive(irMutex);
        return success;
    }

    return false;
}

bool IRSignalManager::repeatEmitSignal(const String& signalId, uint16_t repeatCount, uint32_t interval) {
    bool allSuccess = true;

    for (uint16_t i = 0; i < repeatCount; i++) {
        if (!emitSignal(signalId)) {
            allSuccess = false;
        }

        if (i < repeatCount - 1 && interval > 0) {
            delay(interval);
        }
    }

    return allSuccess;
}

String IRSignalManager::identifyProtocol(const String& rawData) {
    // 简化的协议识别逻辑
    if (rawData.length() == 8) {
        return "NEC";
    } else if (rawData.length() == 6) {
        return "SONY";
    } else if (rawData.length() == 4) {
        return "RC5";
    }

    return "RAW";
}

bool IRSignalManager::validateSignalData(const SignalData& signalData) {
    if (signalData.id.isEmpty() || signalData.signalCode.isEmpty() ||
        signalData.protocol.isEmpty() || signalData.frequency.isEmpty()) {
        return false;
    }

    // 验证频率范围
    uint32_t freq = signalData.frequency.toInt();
    if (freq < 30000 || freq > 60000) {
        return false;
    }

    // 验证协议
    std::vector<String> supportedProtocols = getSupportedProtocols();
    bool protocolSupported = false;
    for (const String& protocol : supportedProtocols) {
        if (signalData.protocol.equalsIgnoreCase(protocol)) {
            protocolSupported = true;
            break;
        }
    }

    return protocolSupported;
}

std::vector<String> IRSignalManager::getSupportedProtocols() const {
    return {
        "NEC", "SONY", "RC5", "RC6", "SAMSUNG", "LG",
        "PANASONIC", "MITSUBISHI", "DISH", "SHARP", "RAW"
    };
}

JsonDocument IRSignalManager::getIRStatus() const {
    JsonDocument doc;

    doc["is_initialized"] = (irSend != nullptr && irRecv != nullptr);
    doc["is_learning"] = isLearningActive;
    doc["learning_state"] = static_cast<int>(learningState);
    doc["emit_state"] = static_cast<int>(emitState);
    doc["default_frequency"] = defaultFrequency;
    doc["default_duty_cycle"] = defaultDutyCycle;
    doc["emit_repeat_count"] = emitRepeatCount;
    doc["last_emit_time"] = lastEmitTime;

    return doc;
}

JsonDocument IRSignalManager::getStatistics() const {
    JsonDocument doc;

    doc["total_signals_learned"] = totalSignalsLearned;
    doc["total_signals_emitted"] = totalSignalsEmitted;
    doc["learning_attempts"] = learningAttempts;
    doc["emit_attempts"] = emitAttempts;
    doc["learning_success_rate"] = (learningAttempts > 0) ?
                                  ((float)totalSignalsLearned / learningAttempts * 100.0) : 0.0;
    doc["emit_success_rate"] = (emitAttempts > 0) ?
                              ((float)totalSignalsEmitted / emitAttempts * 100.0) : 0.0;

    return doc;
}

bool IRSignalManager::checkHardwareStatus() const {
    // 简单的硬件状态检查
    return (irSend != nullptr && irRecv != nullptr);
}

// ==================== 私有方法实现 ====================

void IRSignalManager::processLearning() {
    if (learningState == WAITING_FOR_SIGNAL) {
        // 检查超时
        if (millis() - learningStartTime > learningTimeout) {
            learningState = LEARNING_TIMEOUT;
            isLearningActive = false;

            logIR("WARN", "学习超时");

            // 发送超时事件
            SystemManager& systemManager = SystemManager::getInstance();
            WebSocketManager* wsManager = systemManager.getWebSocketManager();
            if (wsManager) {
                wsManager->sendLearningTimeoutEvent();
            }

            JsonDocument eventData;
            eventData["error"] = "学习超时";
            eventData["signal_id"] = currentLearningId;
            eventData["timeout"] = learningTimeout;
            systemManager.sendSystemEvent("learning_timeout", eventData);

            return;
        }

        // 检查是否接收到信号
        if (irRecv->decode(&results)) {
            learningState = PROCESSING_SIGNAL;
            processReceivedSignal();
            irRecv->resume();
        }
    }
}

void IRSignalManager::processReceivedSignal() {
    // 验证信号有效性
    if (results.bits == 0 || results.value == 0) {
        learningState = LEARNING_ERROR;
        logIR("ERROR", "接收到无效信号");

        // 发送错误事件
        SystemManager& systemManager = SystemManager::getInstance();
        JsonDocument eventData;
        eventData["error"] = "无效信号";
        eventData["signal_id"] = currentLearningId;
        systemManager.sendSystemEvent("learning_error", eventData);

        return;
    }

    // 创建信号数据 - 兼容前端数据格式
    JsonDocument signalDoc;
    signalDoc["id"] = currentLearningId;
    signalDoc["name"] = "新学习信号";
    signalDoc["signalCode"] = uint64ToString(results.value, 16);
    signalDoc["protocol"] = protocolToString(results.decode_type);
    signalDoc["frequency"] = String(defaultFrequency);
    signalDoc["type"] = "other";
    signalDoc["description"] = "学习的红外信号";
    signalDoc["isLearned"] = true;
    signalDoc["sentCount"] = 0;
    signalDoc["lastSent"] = 0;
    signalDoc["created"] = millis();
    signalDoc["data"] = uint64ToString(results.value, 16);

    // 保存信号到存储
    SystemManager& systemManager = SystemManager::getInstance();
    SignalStorage* storage = systemManager.getSignalStorage();

    if (storage) {
        SignalData signal = SignalData::fromJson(signalDoc);
        if (storage->saveSignal(signal)) {
            learningState = LEARNING_COMPLETE;
            isLearningActive = false;
            totalSignalsLearned++;

            logIR("INFO", "信号学习完成: " + signal.name);
            sendLearningCompleteEvent(signalDoc);
        } else {
            learningState = LEARNING_ERROR;
            logIR("ERROR", "信号保存失败");
        }
    } else {
        learningState = LEARNING_ERROR;
        logIR("ERROR", "信号存储未初始化");
    }
}

String IRSignalManager::generateSignalId() {
    static uint32_t signalCounter = 0;
    signalCounter++;
    return "signal_" + String(millis()) + "_" + String(signalCounter);
}

String IRSignalManager::protocolToString(decode_type_t protocol) const {
    switch (protocol) {
        case NEC: return "NEC";
        case SONY: return "SONY";
        case RC5: return "RC5";
        case RC6: return "RC6";
        case SAMSUNG: return "SAMSUNG";
        case LG: return "LG";
        case PANASONIC: return "PANASONIC";
        case MITSUBISHI: return "MITSUBISHI";
        case DISH: return "DISH";
        case SHARP: return "SHARP";
        default: return "RAW";
    }
}

decode_type_t IRSignalManager::stringToProtocol(const String& protocolStr) const {
    if (protocolStr.equalsIgnoreCase("NEC")) return NEC;
    if (protocolStr.equalsIgnoreCase("SONY")) return SONY;
    if (protocolStr.equalsIgnoreCase("RC5")) return RC5;
    if (protocolStr.equalsIgnoreCase("RC6")) return RC6;
    if (protocolStr.equalsIgnoreCase("SAMSUNG")) return SAMSUNG;
    if (protocolStr.equalsIgnoreCase("LG")) return LG;
    if (protocolStr.equalsIgnoreCase("PANASONIC")) return PANASONIC;
    if (protocolStr.equalsIgnoreCase("MITSUBISHI")) return MITSUBISHI;
    if (protocolStr.equalsIgnoreCase("DISH")) return DISH;
    if (protocolStr.equalsIgnoreCase("SHARP")) return SHARP;
    return UNKNOWN;
}

String IRSignalManager::uint64ToString(uint64_t value, uint8_t base) const {
    String result = "";
    if (base == 16) {
        result = "0x" + String((uint32_t)(value >> 32), HEX) + String((uint32_t)(value & 0xFFFFFFFF), HEX);
    } else {
        result = String((unsigned long)value, base);
    }
    return result;
}

uint64_t IRSignalManager::stringToUint64(const String& str) const {
    if (str.startsWith("0x") || str.startsWith("0X")) {
        return strtoull(str.c_str() + 2, nullptr, 16);
    } else {
        return strtoull(str.c_str(), nullptr, 10);
    }
}

void IRSignalManager::sendLearningCompleteEvent(const JsonDocument& signalData) {
    SystemManager& systemManager = SystemManager::getInstance();
    systemManager.sendSystemEvent("signal_learned", signalData);
}

void IRSignalManager::sendEmitCompleteEvent(const String& signalId, bool success) {
    SystemManager& systemManager = SystemManager::getInstance();
    JsonDocument eventData;
    eventData["signal_id"] = signalId;
    eventData["success"] = success;
    eventData["timestamp"] = millis();
    systemManager.sendSystemEvent("signal_sent", eventData);
}

void IRSignalManager::logIR(const String& level, const String& message) {
    String logMessage = "[IR] " + message;

    if (level == "ERROR") {
        Serial.println("❌ " + logMessage);
    } else if (level == "WARN") {
        Serial.println("⚠️ " + logMessage);
    } else if (level == "INFO") {
        Serial.println("ℹ️ " + logMessage);
    } else if (level == "DEBUG" && DEBUG_LEVEL >= 4) {
        Serial.println("🔍 " + logMessage);
    }
}
