================================================================================
R1智能红外控制系统 - 调试报告
================================================================================
生成时间: 2025-07-19T18:12:46.160Z
会话时长: 327秒
日志总数: 3098
用户代理: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0
页面URL: http://***********:8000/

---------------------------------------- 系统状态 ----------------------------------------
{
  "currentModule": "signalManager",
  "isInitialized": true,
  "modules": [
    "signalManager",
    "controlModule",
    "timerSettings",
    "statusDisplay",
    "systemMonitor"
  ],
  "performance": {
    "startTime": 143.90000000596046,
    "initTime": 1286.5,
    "moduleLoadTime": 809.7000000029802,
    "memoryUsage": 0
  },
  "esp32Connected": true
}

---------------------------------------- 内存信息 ----------------------------------------
已用内存: 3MB
总内存: 4MB
内存限制: 4096MB

---------------------------------------- 错误统计 ----------------------------------------
LOG: 3064条
ERROR: 22条
WARN: 12条

---------------------------------------- 详细日志 ----------------------------------------
[1] [+0ms] [LOG] 2025-07-19T18:07:19.298Z
✅ 错误收集器已启动，点击右下角按钮下载报告

[2] [+0ms] [LOG] 2025-07-19T18:07:19.298Z
✅ 错误收集器已启动，点击右下角按钮下载报告

[3] [+0ms] [LOG] 2025-07-19T18:07:19.298Z
🌙 深色模式已启用

[4] [+0ms] [LOG] 2025-07-19T18:07:19.298Z
🌙 深色模式已启用

[5] [+0ms] [LOG] 2025-07-19T18:07:19.298Z
✅ 核心组件初始化完成 (会话ID: session_48439298)

[6] [+0ms] [LOG] 2025-07-19T18:07:19.298Z
✅ 核心组件初始化完成 (会话ID: session_48439298)

[7] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
➕ UnifiedTimerManager: 添加定时器 [system_time_update], 间隔: 1000ms, 重复: true, 下次执行: 2025/7/20 02:07:20

[8] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
➕ UnifiedTimerManager: 添加定时器 [system_time_update], 间隔: 1000ms, 重复: true, 下次执行: 2025/7/20 02:07:20

[9] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
🚀 UnifiedTimerManager: 主定时器已启动，间隔: 100ms, 定时器数量: 1

[10] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
🚀 UnifiedTimerManager: 主定时器已启动，间隔: 100ms, 定时器数量: 1

[11] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
⏰ 系统时间显示已启动

[12] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
⏰ 系统时间显示已启动

[13] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
📡 初始化ESP32管理器...

[14] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
📡 初始化ESP32管理器...

[15] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
🚀 初始化ESP32管理器...

[16] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
🚀 初始化ESP32管理器...

[17] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
📡 HTTP API: http://***********:8000

[18] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
📡 HTTP API: http://***********:8000

[19] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
🔌 WebSocket: ws://***********:8001/ws

[20] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
🔌 WebSocket: ws://***********:8001/ws

[21] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
🔍 测试后端连接...

[22] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
🔍 测试后端连接...

[23] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
📡 请求URL: http://***********:8000/api/status

[24] [+1ms] [LOG] 2025-07-19T18:07:19.299Z
📡 请求URL: http://***********:8000/api/status

[25] [+330ms] [LOG] 2025-07-19T18:07:19.628Z
📊 HTTP状态: 200 OK

[26] [+330ms] [LOG] 2025-07-19T18:07:19.628Z
📊 HTTP状态: 200 OK

[27] [+330ms] [LOG] 2025-07-19T18:07:19.628Z
📋 响应头: {
  "content-type": "application/json",
  "content-length": "238"
}
  参数2: {
  "content-type": "application/json",
  "content-length": "238"
}

[28] [+331ms] [LOG] 2025-07-19T18:07:19.629Z
📋 响应头: {
  "content-type": "application/json",
  "content-length": "238"
}
  参数2: {
  "content-type": "application/json",
  "content-length": "238"
}

[29] [+331ms] [LOG] 2025-07-19T18:07:19.629Z
📦 响应数据: {
  "success": true,
  "data": {
    "uptime": 121,
    "memory_usage": 36.2417,
    "signal_count": 1,
    "wifi_strength": 0,
    "free_heap": 220744,
    "chip_temperature": 55.1,
    "timestamp": 122706
  },
  "message": "系统状态获取成功",
  "timestamp": 1735689722717,
  "responseTime": 0
}
  参数2: {
  "success": true,
  "data": {
    "uptime": 121,
    "memory_usage": 36.2417,
    "signal_count": 1,
    "wifi_strength": 0,
    "free_heap": 220744,
    "chip_temperature": 55.1,
    "timestamp": 122706
  },
  "message": "系统状态获取成功",
  "timestamp": 1735689722717,
  "responseTime": 0
}

[30] [+331ms] [LOG] 2025-07-19T18:07:19.629Z
📦 响应数据: {
  "success": true,
  "data": {
    "uptime": 121,
    "memory_usage": 36.2417,
    "signal_count": 1,
    "wifi_strength": 0,
    "free_heap": 220744,
    "chip_temperature": 55.1,
    "timestamp": 122706
  },
  "message": "系统状态获取成功",
  "timestamp": 1735689722717,
  "responseTime": 0
}
  参数2: {
  "success": true,
  "data": {
    "uptime": 121,
    "memory_usage": 36.2417,
    "signal_count": 1,
    "wifi_strength": 0,
    "free_heap": 220744,
    "chip_temperature": 55.1,
    "timestamp": 122706
  },
  "message": "系统状态获取成功",
  "timestamp": 1735689722717,
  "responseTime": 0
}

[31] [+331ms] [LOG] 2025-07-19T18:07:19.629Z
✅ 后端连接正常

[32] [+331ms] [LOG] 2025-07-19T18:07:19.629Z
✅ 后端连接正常

[33] [+343ms] [LOG] 2025-07-19T18:07:19.641Z
✅ WebSocket连接成功

[34] [+343ms] [LOG] 2025-07-19T18:07:19.641Z
✅ WebSocket连接成功

[35] [+343ms] [LOG] 2025-07-19T18:07:19.641Z
✅ ESP32管理器初始化完成 - 在线模式

[36] [+343ms] [LOG] 2025-07-19T18:07:19.641Z
✅ ESP32管理器初始化完成 - 在线模式

[37] [+343ms] [LOG] 2025-07-19T18:07:19.641Z
✅ ESP32连接成功

[38] [+343ms] [LOG] 2025-07-19T18:07:19.641Z
✅ ESP32连接成功

[39] [+343ms] [LOG] 2025-07-19T18:07:19.641Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/config/user",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/config/user",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[40] [+343ms] [LOG] 2025-07-19T18:07:19.641Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/config/user",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/config/user",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[41] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
✅ 已加载当前硬件配置: {
  "ir_recv_pin": 14,
  "ir_send_pin": 21,
  "ir_frequency": 38000,
  "ir_duty_cycle": 33,
  "status_led_pin": 2,
  "status_led_enabled": true
}
  参数2: {
  "ir_recv_pin": 14,
  "ir_send_pin": 21,
  "ir_frequency": 38000,
  "ir_duty_cycle": 33,
  "status_led_pin": 2,
  "status_led_enabled": true
}

[42] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
✅ 已加载当前硬件配置: {
  "ir_recv_pin": 14,
  "ir_send_pin": 21,
  "ir_frequency": 38000,
  "ir_duty_cycle": 33,
  "status_led_pin": 2,
  "status_led_enabled": true
}
  参数2: {
  "ir_recv_pin": 14,
  "ir_send_pin": 21,
  "ir_frequency": 38000,
  "ir_duty_cycle": 33,
  "status_led_pin": 2,
  "status_led_enabled": true
}

[43] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
📋 当前硬件配置:

[44] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
📋 当前硬件配置:

[45] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
  红外接收引脚: GPIO14

[46] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
  红外接收引脚: GPIO14

[47] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
  红外发射引脚: GPIO21

[48] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
  红外发射引脚: GPIO21

[49] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
  红外频率: 38000 Hz

[50] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
  红外频率: 38000 Hz

[51] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
  占空比: 33%

[52] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
  占空比: 33%

[53] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
  状态LED引脚: GPIO2

[54] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
  状态LED引脚: GPIO2

[55] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
  状态LED启用: 是

[56] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
  状态LED启用: 是

[57] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
📦 开始初始化所有模块...

[58] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
📦 开始初始化所有模块...

[59] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
🔧 初始化 signalManager 模块...

[60] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
🔧 初始化 signalManager 模块...

[61] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
🔍 signalManager 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[62] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
🔍 signalManager 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[63] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
🔧 开始初始化 SignalManager 模块...

[64] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
🔧 开始初始化 SignalManager 模块...

[65] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
🔧 初始化 controlModule 模块...

[66] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
🔧 初始化 controlModule 模块...

[67] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
🔍 controlModule 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[68] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
🔍 controlModule 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[69] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
🔧 开始初始化 ControlModule 模块...

[70] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
🔧 开始初始化 ControlModule 模块...

[71] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
🔧 初始化 timerSettings 模块...

[72] [+475ms] [LOG] 2025-07-19T18:07:19.773Z
🔧 初始化 timerSettings 模块...

[73] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔍 timerSettings 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[74] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔍 timerSettings 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[75] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔧 开始初始化 TimerSettings 模块...

[76] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔧 开始初始化 TimerSettings 模块...

[77] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
TimerSettings: 事件监听器设置完成

[78] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
TimerSettings: 事件监听器设置完成

[79] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔧 初始化 statusDisplay 模块...

[80] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔧 初始化 statusDisplay 模块...

[81] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔍 statusDisplay 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[82] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔍 statusDisplay 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[83] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔧 开始初始化 StatusDisplay 模块...

[84] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔧 开始初始化 StatusDisplay 模块...

[85] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔧 初始化 systemMonitor 模块...

[86] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔧 初始化 systemMonitor 模块...

[87] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔍 systemMonitor 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[88] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔍 systemMonitor 初始化调试: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}
  参数2: {
  "eventBusExists": true,
  "esp32Exists": true,
  "esp32Connected": true,
  "esp32Type": "object"
}

[89] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔧 开始初始化 SystemMonitor 模块...

[90] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔧 开始初始化 SystemMonitor 模块...

[91] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
📡 SignalManager 事件监听器初始化完成

[92] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
📡 SignalManager 事件监听器初始化完成

[93] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔍 渲染信号 - 总数: 0, 过滤后: 0

[94] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔍 渲染信号 - 总数: 0, 过滤后: 0

[95] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[96] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[97] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
📡 ControlModule 事件监听器初始化完成

[98] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
📡 ControlModule 事件监听器初始化完成

[99] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
ControlModule: 事件绑定完成

[100] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
ControlModule: 事件绑定完成

[101] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
📡 TimerSettings 事件监听器初始化完成

[102] [+476ms] [LOG] 2025-07-19T18:07:19.774Z
📡 TimerSettings 事件监听器初始化完成

[103] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
TimerSettings: 默认时间值设置完成（9-18点）

[104] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
TimerSettings: 默认时间值设置完成（9-18点）

[105] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
TimerSettings: 时间选择器事件绑定完成

[106] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
TimerSettings: 时间选择器事件绑定完成

[107] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
TimerSettings: 主开关状态: false

[108] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
TimerSettings: 主开关状态: false

[109] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
TimerSettings: 间隔设置状态已更新 {
  "hasMultipleTasks": false,
  "intervalDisabled": false,
  "reason": "单任务模式允许间隔"
}
  参数2: {
  "hasMultipleTasks": false,
  "intervalDisabled": false,
  "reason": "单任务模式允许间隔"
}

[110] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
TimerSettings: 间隔设置状态已更新 {
  "hasMultipleTasks": false,
  "intervalDisabled": false,
  "reason": "单任务模式允许间隔"
}
  参数2: {
  "hasMultipleTasks": false,
  "intervalDisabled": false,
  "reason": "单任务模式允许间隔"
}

[111] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
TimerSettings: 事件绑定完成

[112] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
TimerSettings: 事件绑定完成

[113] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
📡 StatusDisplay 事件监听器初始化完成

[114] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
📡 StatusDisplay 事件监听器初始化完成

[115] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
📡 SystemMonitor 事件监听器初始化完成

[116] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
📡 SystemMonitor 事件监听器初始化完成

[117] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
🔍 SystemMonitor: 开始设置UI...

[118] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
🔍 SystemMonitor: 开始设置UI...

[119] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
🔍 SystemMonitor: 绑定过滤器事件...

[120] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
🔍 SystemMonitor: 绑定过滤器事件...

[121] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
🔍 SystemMonitor: 查找systemMonitorArea结果: {}
  参数2: {}

[122] [+477ms] [LOG] 2025-07-19T18:07:19.775Z
🔍 SystemMonitor: 查找systemMonitorArea结果: {}
  参数2: {}

[123] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
🎨 SignalManager UI初始化完成

[124] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
🎨 SignalManager UI初始化完成

[125] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
📥 加载 0 个标准格式信号

[126] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
📥 加载 0 个标准格式信号

[127] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
✅ 从本地存储加载了 0 个信号

[128] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
✅ 从本地存储加载了 0 个信号

[129] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
📡 SignalManager: 从ESP32加载信号列表...

[130] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
📡 SignalManager: 从ESP32加载信号列表...

[131] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[132] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[133] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
🎨 ControlModule UI初始化完成

[134] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
🎨 ControlModule UI初始化完成

[135] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
🎨 TimerSettings UI初始化完成

[136] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
🎨 TimerSettings UI初始化完成

[137] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
TimerSettings: 数据加载待重新实现

[138] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
TimerSettings: 数据加载待重新实现

[139] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
🎨 StatusDisplay UI初始化完成

[140] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
🎨 StatusDisplay UI初始化完成

[141] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
StatusDisplay 数据加载完成，等待其他模块初始化

[142] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
StatusDisplay 数据加载完成，等待其他模块初始化

[143] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
🎨 SystemMonitor UI初始化完成

[144] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
🎨 SystemMonitor UI初始化完成

[145] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[146] [+478ms] [LOG] 2025-07-19T18:07:19.776Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[147] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[148] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[149] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[150] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[151] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[152] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[153] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
📊 ControlModule 数据加载完成

[154] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
📊 ControlModule 数据加载完成

[155] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
✅ ControlModule 模块初始化完成，耗时: 3.70ms

[156] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
✅ ControlModule 模块初始化完成，耗时: 3.70ms

[157] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
📊 TimerSettings 数据加载完成

[158] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
📊 TimerSettings 数据加载完成

[159] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
✅ TimerSettings 模块初始化完成，耗时: 3.80ms

[160] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
✅ TimerSettings 模块初始化完成，耗时: 3.80ms

[161] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
📊 StatusDisplay 数据加载完成

[162] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
📊 StatusDisplay 数据加载完成

[163] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
✅ StatusDisplay 模块初始化完成，耗时: 3.70ms

[164] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
✅ StatusDisplay 模块初始化完成，耗时: 3.70ms

[165] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
📊 SystemMonitor 数据加载完成

[166] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
📊 SystemMonitor 数据加载完成

[167] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
✅ SystemMonitor 模块初始化完成，耗时: 3.70ms

[168] [+479ms] [LOG] 2025-07-19T18:07:19.777Z
✅ SystemMonitor 模块初始化完成，耗时: 3.70ms

[169] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
📨 WebSocket消息: {
  "type": "connected",
  "payload": {
    "message": "WebSocket连接成功",
    "clientId": "client_2",
    "serverTime": 122906,
    "serverVersion": "1.0.0"
  },
  "timestamp": 122906
}
  参数2: {
  "type": "connected",
  "payload": {
    "message": "WebSocket连接成功",
    "clientId": "client_2",
    "serverTime": 122906,
    "serverVersion": "1.0.0"
  },
  "timestamp": 122906
}

[170] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
📨 WebSocket消息: {
  "type": "connected",
  "payload": {
    "message": "WebSocket连接成功",
    "clientId": "client_2",
    "serverTime": 122906,
    "serverVersion": "1.0.0"
  },
  "timestamp": 122906
}
  参数2: {
  "type": "connected",
  "payload": {
    "message": "WebSocket连接成功",
    "clientId": "client_2",
    "serverTime": 122906,
    "serverVersion": "1.0.0"
  },
  "timestamp": 122906
}

[171] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
✅ 模块就绪: ControlModule

[172] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
✅ 模块就绪: ControlModule

[173] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
✅ 模块就绪: TimerSettings

[174] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
✅ 模块就绪: TimerSettings

[175] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
✅ 模块就绪: StatusDisplay

[176] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
✅ 模块就绪: StatusDisplay

[177] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
✅ 模块就绪: SystemMonitor

[178] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
✅ 模块就绪: SystemMonitor

[179] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
✅ SignalManager: ESP32连接成功

[180] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
✅ SignalManager: ESP32连接成功

[181] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
📡 SignalManager: 从ESP32加载信号列表...

[182] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
📡 SignalManager: 从ESP32加载信号列表...

[183] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[184] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/signals",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[185] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
✅ ControlModule: ESP32连接成功

[186] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
✅ ControlModule: ESP32连接成功

[187] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
✅ TimerSettings: ESP32连接成功

[188] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
✅ TimerSettings: ESP32连接成功

[189] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
📡 从ESP32加载定时任务列表...

[190] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
📡 从ESP32加载定时任务列表...

[191] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/timer/tasks",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/timer/tasks",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[192] [+493ms] [LOG] 2025-07-19T18:07:19.791Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/timer/tasks",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/timer/tasks",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[193] [+494ms] [LOG] 2025-07-19T18:07:19.792Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[194] [+494ms] [LOG] 2025-07-19T18:07:19.792Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[195] [+494ms] [LOG] 2025-07-19T18:07:19.792Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[196] [+494ms] [LOG] 2025-07-19T18:07:19.792Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[197] [+494ms] [LOG] 2025-07-19T18:07:19.792Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[198] [+494ms] [LOG] 2025-07-19T18:07:19.792Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[199] [+494ms] [LOG] 2025-07-19T18:07:19.792Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[200] [+494ms] [LOG] 2025-07-19T18:07:19.792Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[201] [+1003ms] [LOG] 2025-07-19T18:07:20.301Z
⏰ UnifiedTimerManager: 1 个定时器到期

[202] [+1003ms] [LOG] 2025-07-19T18:07:20.301Z
⏰ UnifiedTimerManager: 1 个定时器到期

[203] [+1004ms] [LOG] 2025-07-19T18:07:20.302Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[204] [+1004ms] [LOG] 2025-07-19T18:07:20.302Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[205] [+1004ms] [LOG] 2025-07-19T18:07:20.302Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[206] [+1004ms] [LOG] 2025-07-19T18:07:20.302Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[207] [+1004ms] [LOG] 2025-07-19T18:07:20.302Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:21

[208] [+1004ms] [LOG] 2025-07-19T18:07:20.302Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:21

[209] [+1283ms] [LOG] 2025-07-19T18:07:20.581Z
💾 保存 1 个标准格式信号

[210] [+1283ms] [LOG] 2025-07-19T18:07:20.581Z
💾 保存 1 个标准格式信号

[211] [+1283ms] [LOG] 2025-07-19T18:07:20.581Z
💾 保存前信号总数: 1

[212] [+1283ms] [LOG] 2025-07-19T18:07:20.581Z
💾 保存前信号总数: 1

[213] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
✅ 信号 "新学习信号" 将被保存

[214] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
✅ 信号 "新学习信号" 将被保存

[215] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
💾 准备保存 1 个有效信号

[216] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
💾 准备保存 1 个有效信号

[217] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
💾 内存中保留 1 个信号

[218] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
💾 内存中保留 1 个信号

[219] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
✅ 标准格式信号数据保存完成 - 保存了 1 个信号

[220] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
✅ 标准格式信号数据保存完成 - 保存了 1 个信号

[221] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
✅ SignalManager: 从ESP32加载了 1 个信号

[222] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
✅ SignalManager: 从ESP32加载了 1 个信号

[223] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
🔍 渲染信号 - 总数: 1, 过滤后: 1

[224] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
🔍 渲染信号 - 总数: 1, 过滤后: 1

[225] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[226] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[227] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
🔍 开始渲染 1 个信号到网格

[228] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
🔍 开始渲染 1 个信号到网格

[229] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
✅ 信号 1: 新学习信号 HTML生成成功

[230] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
✅ 信号 1: 新学习信号 HTML生成成功

[231] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
📊 HTML生成完成，共 1 个信号，DOM子元素: 1

[232] [+1284ms] [LOG] 2025-07-19T18:07:20.582Z
📊 HTML生成完成，共 1 个信号，DOM子元素: 1

[233] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ 网格渲染完成，容器子元素数量: 1

[234] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ 网格渲染完成，容器子元素数量: 1

[235] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
🔍 渲染信号 - 总数: 1, 过滤后: 1

[236] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
🔍 渲染信号 - 总数: 1, 过滤后: 1

[237] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[238] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[239] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
🔍 使用缓存，跳过渲染

[240] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
🔍 使用缓存，跳过渲染

[241] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
📊 SignalManager 数据加载完成

[242] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
📊 SignalManager 数据加载完成

[243] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ SignalManager 模块初始化完成，耗时: 809.30ms

[244] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ SignalManager 模块初始化完成，耗时: 809.30ms

[245] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ signalManager 模块初始化成功

[246] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ signalManager 模块初始化成功

[247] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ controlModule 模块初始化成功

[248] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ controlModule 模块初始化成功

[249] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ timerSettings 模块初始化成功

[250] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ timerSettings 模块初始化成功

[251] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ statusDisplay 模块初始化成功

[252] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ statusDisplay 模块初始化成功

[253] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ systemMonitor 模块初始化成功

[254] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ systemMonitor 模块初始化成功

[255] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
📦 模块初始化完成: 成功 5 个，失败 0 个，耗时: 809.70ms

[256] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
📦 模块初始化完成: 成功 5 个，失败 0 个，耗时: 809.70ms

[257] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ R1系统启动完成，耗时: 1286.50ms

[258] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
✅ R1系统启动完成，耗时: 1286.50ms

[259] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
➕ UnifiedTimerManager: 添加定时器 [notification_notification_48440583], 间隔: 5000ms, 重复: false, 下次执行: 2025/7/20 02:07:25

[260] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
➕ UnifiedTimerManager: 添加定时器 [notification_notification_48440583], 间隔: 5000ms, 重复: false, 下次执行: 2025/7/20 02:07:25

[261] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
⚠️ UnifiedTimerManager: 主定时器已在运行

[262] [+1285ms] [LOG] 2025-07-19T18:07:20.583Z
⚠️ UnifiedTimerManager: 主定时器已在运行

[263] [+1300ms] [LOG] 2025-07-19T18:07:20.598Z
✅ 模块就绪: SignalManager

[264] [+1300ms] [LOG] 2025-07-19T18:07:20.598Z
✅ 模块就绪: SignalManager

[265] [+1300ms] [LOG] 2025-07-19T18:07:20.598Z
🎉 所有模块初始化完成: 成功 5 个

[266] [+1300ms] [LOG] 2025-07-19T18:07:20.598Z
🎉 所有模块初始化完成: 成功 5 个

[267] [+1300ms] [LOG] 2025-07-19T18:07:20.598Z
StatusDisplay: 收到系统模块初始化完成事件，开始自动更新

[268] [+1300ms] [LOG] 2025-07-19T18:07:20.598Z
StatusDisplay: 收到系统模块初始化完成事件，开始自动更新

[269] [+1300ms] [LOG] 2025-07-19T18:07:20.598Z
StatusDisplay: updateCurrentTaskDisplay 被调用，现在主要通过事件驱动更新

[270] [+1300ms] [LOG] 2025-07-19T18:07:20.598Z
StatusDisplay: updateCurrentTaskDisplay 被调用，现在主要通过事件驱动更新

[271] [+1601ms] [LOG] 2025-07-19T18:07:20.899Z
📨 WebSocket消息: {
  "type": "websocket_client_connected",
  "payload": {
    "client_id": 2,
    "client_ip": "***********",
    "total_clients": 1
  },
  "timestamp": 1735689722908
}
  参数2: {
  "type": "websocket_client_connected",
  "payload": {
    "client_id": 2,
    "client_ip": "***********",
    "total_clients": 1
  },
  "timestamp": 1735689722908
}

[272] [+1601ms] [LOG] 2025-07-19T18:07:20.899Z
📨 WebSocket消息: {
  "type": "websocket_client_connected",
  "payload": {
    "client_id": 2,
    "client_ip": "***********",
    "total_clients": 1
  },
  "timestamp": 1735689722908
}
  参数2: {
  "type": "websocket_client_connected",
  "payload": {
    "client_id": 2,
    "client_ip": "***********",
    "total_clients": 1
  },
  "timestamp": 1735689722908
}

[273] [+1608ms] [ERROR] 2025-07-19T18:07:20.906Z
❌ 从ESP32加载定时任务失败: {}
  参数2: {}

[274] [+1608ms] [ERROR] 2025-07-19T18:07:20.906Z
❌ 从ESP32加载定时任务失败: {}
  参数2: {}

[275] [+2103ms] [LOG] 2025-07-19T18:07:21.401Z
⏰ UnifiedTimerManager: 1 个定时器到期

[276] [+2103ms] [LOG] 2025-07-19T18:07:21.401Z
⏰ UnifiedTimerManager: 1 个定时器到期

[277] [+2103ms] [LOG] 2025-07-19T18:07:21.401Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[278] [+2103ms] [LOG] 2025-07-19T18:07:21.401Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[279] [+2103ms] [LOG] 2025-07-19T18:07:21.401Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[280] [+2103ms] [LOG] 2025-07-19T18:07:21.401Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[281] [+2103ms] [LOG] 2025-07-19T18:07:21.401Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:22

[282] [+2103ms] [LOG] 2025-07-19T18:07:21.401Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:22

[283] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
💾 保存 1 个标准格式信号

[284] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
💾 保存 1 个标准格式信号

[285] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
💾 保存前信号总数: 1

[286] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
💾 保存前信号总数: 1

[287] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
✅ 信号 "新学习信号" 将被保存

[288] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
✅ 信号 "新学习信号" 将被保存

[289] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
💾 准备保存 1 个有效信号

[290] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
💾 准备保存 1 个有效信号

[291] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
💾 内存中保留 1 个信号

[292] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
💾 内存中保留 1 个信号

[293] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
✅ 标准格式信号数据保存完成 - 保存了 1 个信号

[294] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
✅ 标准格式信号数据保存完成 - 保存了 1 个信号

[295] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
✅ SignalManager: 从ESP32加载了 1 个信号

[296] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
✅ SignalManager: 从ESP32加载了 1 个信号

[297] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
🔍 渲染信号 - 总数: 1, 过滤后: 1

[298] [+2474ms] [LOG] 2025-07-19T18:07:21.772Z
🔍 渲染信号 - 总数: 1, 过滤后: 1

[299] [+2475ms] [LOG] 2025-07-19T18:07:21.773Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[300] [+2475ms] [LOG] 2025-07-19T18:07:21.773Z
🔍 搜索关键词: "", 类型过滤: "", 排序: "name"

[301] [+2475ms] [LOG] 2025-07-19T18:07:21.773Z
🔍 使用缓存，跳过渲染

[302] [+2475ms] [LOG] 2025-07-19T18:07:21.773Z
🔍 使用缓存，跳过渲染

[303] [+2790ms] [LOG] 2025-07-19T18:07:22.088Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 123459
}
  参数2: {
  "type": "ping",
  "timestamp": 123459
}

[304] [+2790ms] [LOG] 2025-07-19T18:07:22.088Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 123459
}
  参数2: {
  "type": "ping",
  "timestamp": 123459
}

[305] [+2790ms] [LOG] 2025-07-19T18:07:22.088Z
🏓 回复pong消息

[306] [+2790ms] [LOG] 2025-07-19T18:07:22.088Z
🏓 回复pong消息

[307] [+3102ms] [LOG] 2025-07-19T18:07:22.400Z
⏰ UnifiedTimerManager: 1 个定时器到期

[308] [+3102ms] [LOG] 2025-07-19T18:07:22.400Z
⏰ UnifiedTimerManager: 1 个定时器到期

[309] [+3102ms] [LOG] 2025-07-19T18:07:22.400Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[310] [+3102ms] [LOG] 2025-07-19T18:07:22.400Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[311] [+3102ms] [LOG] 2025-07-19T18:07:22.400Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[312] [+3102ms] [LOG] 2025-07-19T18:07:22.400Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[313] [+3102ms] [LOG] 2025-07-19T18:07:22.400Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:23

[314] [+3102ms] [LOG] 2025-07-19T18:07:22.400Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:23

[315] [+4102ms] [LOG] 2025-07-19T18:07:23.400Z
⏰ UnifiedTimerManager: 1 个定时器到期

[316] [+4102ms] [LOG] 2025-07-19T18:07:23.400Z
⏰ UnifiedTimerManager: 1 个定时器到期

[317] [+4103ms] [LOG] 2025-07-19T18:07:23.401Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[318] [+4103ms] [LOG] 2025-07-19T18:07:23.401Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[319] [+4103ms] [LOG] 2025-07-19T18:07:23.401Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[320] [+4103ms] [LOG] 2025-07-19T18:07:23.401Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[321] [+4103ms] [LOG] 2025-07-19T18:07:23.401Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:24

[322] [+4103ms] [LOG] 2025-07-19T18:07:23.401Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:24

[323] [+5201ms] [LOG] 2025-07-19T18:07:24.499Z
⏰ UnifiedTimerManager: 1 个定时器到期

[324] [+5201ms] [LOG] 2025-07-19T18:07:24.499Z
⏰ UnifiedTimerManager: 1 个定时器到期

[325] [+5202ms] [LOG] 2025-07-19T18:07:24.500Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[326] [+5202ms] [LOG] 2025-07-19T18:07:24.500Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[327] [+5202ms] [LOG] 2025-07-19T18:07:24.500Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[328] [+5202ms] [LOG] 2025-07-19T18:07:24.500Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[329] [+5202ms] [LOG] 2025-07-19T18:07:24.500Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:25

[330] [+5202ms] [LOG] 2025-07-19T18:07:24.500Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:25

[331] [+6163ms] [LOG] 2025-07-19T18:07:25.461Z
🎮 [DEBUG] ControlModule.handleSendSignalRequest() - 来自信号管理模块: {
  "signalId": "signal_77579_1",
  "source": "SignalManager"
}
  参数2: {
  "signalId": "signal_77579_1",
  "source": "SignalManager"
}

[332] [+6163ms] [LOG] 2025-07-19T18:07:25.461Z
🎮 [DEBUG] ControlModule.handleSendSignalRequest() - 来自信号管理模块: {
  "signalId": "signal_77579_1",
  "source": "SignalManager"
}
  参数2: {
  "signalId": "signal_77579_1",
  "source": "SignalManager"
}

[333] [+6163ms] [LOG] 2025-07-19T18:07:25.461Z
📤 [DEBUG] ControlModule: 发送 signal.request.by-ids 事件获取单个信号详情

[334] [+6163ms] [LOG] 2025-07-19T18:07:25.461Z
📤 [DEBUG] ControlModule: 发送 signal.request.by-ids 事件获取单个信号详情

[335] [+6168ms] [LOG] 2025-07-19T18:07:25.466Z
📨 [DEBUG] SignalManager.handleSignalRequestByIds() - 来源: ControlModule, IDs: signal_77579_1

[336] [+6168ms] [LOG] 2025-07-19T18:07:25.466Z
📨 [DEBUG] SignalManager.handleSignalRequestByIds() - 来源: ControlModule, IDs: signal_77579_1

[337] [+6169ms] [LOG] 2025-07-19T18:07:25.467Z
🔍 [DEBUG] 查找信号 ID: signal_77579_1, 找到: 新学习信号

[338] [+6169ms] [LOG] 2025-07-19T18:07:25.467Z
🔍 [DEBUG] 查找信号 ID: signal_77579_1, 找到: 新学习信号

[339] [+6169ms] [LOG] 2025-07-19T18:07:25.467Z
📤 [DEBUG] SignalManager: 返回 1 个指定信号，信号名称: 新学习信号

[340] [+6169ms] [LOG] 2025-07-19T18:07:25.467Z
📤 [DEBUG] SignalManager: 返回 1 个指定信号，信号名称: 新学习信号

[341] [+6169ms] [LOG] 2025-07-19T18:07:25.467Z
🔄 [DEBUG] SignalManager: 调用回调函数，传递 1 个信号

[342] [+6169ms] [LOG] 2025-07-19T18:07:25.467Z
🔄 [DEBUG] SignalManager: 调用回调函数，传递 1 个信号

[343] [+6169ms] [LOG] 2025-07-19T18:07:25.467Z
🔄 [DEBUG] ControlModule: 收到信号回调，信号数量: 1

[344] [+6169ms] [LOG] 2025-07-19T18:07:25.467Z
🔄 [DEBUG] ControlModule: 收到信号回调，信号数量: 1

[345] [+6169ms] [LOG] 2025-07-19T18:07:25.467Z
✅ [DEBUG] ControlModule: 获得信号详情: 新学习信号, 创建单个发射任务

[346] [+6169ms] [LOG] 2025-07-19T18:07:25.467Z
✅ [DEBUG] ControlModule: 获得信号详情: 新学习信号, 创建单个发射任务

[347] [+6169ms] [LOG] 2025-07-19T18:07:25.467Z
🚀 [DEBUG] ControlModule: 启动单个信号发射任务: 单个信号发射

[348] [+6169ms] [LOG] 2025-07-19T18:07:25.467Z
🚀 [DEBUG] ControlModule: 启动单个信号发射任务: 单个信号发射

[349] [+6170ms] [LOG] 2025-07-19T18:07:25.468Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 29,
  "bodyPreview": "{\"signalId\":\"signal_77579_1\"}"
}
  参数2: {
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 29,
  "bodyPreview": "{\"signalId\":\"signal_77579_1\"}"
}

[350] [+6170ms] [LOG] 2025-07-19T18:07:25.468Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 29,
  "bodyPreview": "{\"signalId\":\"signal_77579_1\"}"
}
  参数2: {
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 29,
  "bodyPreview": "{\"signalId\":\"signal_77579_1\"}"
}

[351] [+6170ms] [LOG] 2025-07-19T18:07:25.468Z
📊 StatusDisplay: 解析后的任务信息: {
  "name": "单个信号发射",
  "type": "single",
  "status": "executing",
  "totalSignals": 1,
  "currentIndex": 0,
  "currentSignal": "新学习信号"
}
  参数2: {
  "name": "单个信号发射",
  "type": "single",
  "status": "executing",
  "totalSignals": 1,
  "currentIndex": 0,
  "currentSignal": "新学习信号"
}

[352] [+6170ms] [LOG] 2025-07-19T18:07:25.468Z
📊 StatusDisplay: 解析后的任务信息: {
  "name": "单个信号发射",
  "type": "single",
  "status": "executing",
  "totalSignals": 1,
  "currentIndex": 0,
  "currentSignal": "新学习信号"
}
  参数2: {
  "name": "单个信号发射",
  "type": "single",
  "status": "executing",
  "totalSignals": 1,
  "currentIndex": 0,
  "currentSignal": "新学习信号"
}

[353] [+6170ms] [LOG] 2025-07-19T18:07:25.468Z
📊 StatusDisplay: updateSystemStatus 被调用 - status: executing, text: 执行中

[354] [+6170ms] [LOG] 2025-07-19T18:07:25.468Z
📊 StatusDisplay: updateSystemStatus 被调用 - status: executing, text: 执行中

[355] [+6171ms] [LOG] 2025-07-19T18:07:25.469Z
📊 StatusDisplay: 状态更新完成 - 显示文本: 执行中, 颜色: var(--primary-color)

[356] [+6171ms] [LOG] 2025-07-19T18:07:25.469Z
📊 StatusDisplay: 状态更新完成 - 显示文本: 执行中, 颜色: var(--primary-color)

[357] [+6171ms] [LOG] 2025-07-19T18:07:25.469Z
🔍 SystemMonitor: 收到 control.emit.started 事件，原始数据: {
  "task": {
    "id": "emit-task-1752948445467",
    "name": "单个信号发射",
    "type": "single",
    "signals": [
      {
        "id": "signal_77579_1",
        "name": "新学习信号",
        "type": "other",
        "description": "学习的红外信号",
        "signalCode": "0x552aa000000",
        "protocol": "RAW",
        "frequency": "38000",
        "data": "0x552aa000000",
        "isLearned": true,
        "created": 80433,
        "lastSent": 0,
        "sentCount": 0
      }
    ],
    "priority": 2,
    "isManual": true,
    "startTime": 1752948445467,
    "isLoopMode": true
  },
  "source": "ControlModule"
}
  参数2: {
  "task": {
    "id": "emit-task-1752948445467",
    "name": "单个信号发射",
    "type": "single",
    "signals": [
      {
        "id": "signal_77579_1",
        "name": "新学习信号",
        "type": "other",
        "description": "学习的红外信号",
        "signalCode": "0x552aa000000",
        "protocol": "RAW",
        "frequency": "38000",
        "data": "0x552aa000000",
        "isLearned": true,
        "created": 80433,
        "lastSent": 0,
        "sentCount": 0
      }
    ],
    "priority": 2,
    "isManual": true,
    "startTime": 1752948445467,
    "isLoopMode": true
  },
  "source": "ControlModule"
}

[358] [+6171ms] [LOG] 2025-07-19T18:07:25.469Z
🔍 SystemMonitor: 收到 control.emit.started 事件，原始数据: {
  "task": {
    "id": "emit-task-1752948445467",
    "name": "单个信号发射",
    "type": "single",
    "signals": [
      {
        "id": "signal_77579_1",
        "name": "新学习信号",
        "type": "other",
        "description": "学习的红外信号",
        "signalCode": "0x552aa000000",
        "protocol": "RAW",
        "frequency": "38000",
        "data": "0x552aa000000",
        "isLearned": true,
        "created": 80433,
        "lastSent": 0,
        "sentCount": 0
      }
    ],
    "priority": 2,
    "isManual": true,
    "startTime": 1752948445467,
    "isLoopMode": true
  },
  "source": "ControlModule"
}
  参数2: {
  "task": {
    "id": "emit-task-1752948445467",
    "name": "单个信号发射",
    "type": "single",
    "signals": [
      {
        "id": "signal_77579_1",
        "name": "新学习信号",
        "type": "other",
        "description": "学习的红外信号",
        "signalCode": "0x552aa000000",
        "protocol": "RAW",
        "frequency": "38000",
        "data": "0x552aa000000",
        "isLearned": true,
        "created": 80433,
        "lastSent": 0,
        "sentCount": 0
      }
    ],
    "priority": 2,
    "isManual": true,
    "startTime": 1752948445467,
    "isLoopMode": true
  },
  "source": "ControlModule"
}

[359] [+6171ms] [LOG] 2025-07-19T18:07:25.469Z
🔍 SystemMonitor: 解析后的任务信息: {
  "taskType": "single",
  "taskName": "单个信号发射",
  "taskId": "emit-task-1752948445467",
  "signalsCount": 1,
  "isLoopMode": true,
  "modeText": "循环",
  "rawEventData": {
    "task": {
      "id": "emit-task-1752948445467",
      "name": "单个信号发射",
      "type": "single",
      "signals": [
        {
          "id": "signal_77579_1",
          "name": "新学习信号",
          "type": "other",
          "description": "学习的红外信号",
          "signalCode": "0x552aa000000",
          "protocol": "RAW",
          "frequency": "38000",
          "data": "0x552aa000000",
          "isLearned": true,
          "created": 80433,
          "lastSent": 0,
          "sentCount": 0
        }
      ],
      "priority": 2,
      "isManual": true,
      "startTime": 1752948445467,
      "isLoopMode": true
    },
    "source": "ControlModule"
  }
}
  参数2: {
  "taskType": "single",
  "taskName": "单个信号发射",
  "taskId": "emit-task-1752948445467",
  "signalsCount": 1,
  "isLoopMode": true,
  "modeText": "循环",
  "rawEventData": {
    "task": {
      "id": "emit-task-1752948445467",
      "name": "单个信号发射",
      "type": "single",
      "signals": [
        {
          "id": "signal_77579_1",
          "name": "新学习信号",
          "type": "other",
          "description": "学习的红外信号",
          "signalCode": "0x552aa000000",
          "protocol": "RAW",
          "frequency": "38000",
          "data": "0x552aa000000",
          "isLearned": true,
          "created": 80433,
          "lastSent": 0,
          "sentCount": 0
        }
      ],
      "priority": 2,
      "isManual": true,
      "startTime": 1752948445467,
      "isLoopMode": true
    },
    "source": "ControlModule"
  }
}

[360] [+6171ms] [LOG] 2025-07-19T18:07:25.469Z
🔍 SystemMonitor: 解析后的任务信息: {
  "taskType": "single",
  "taskName": "单个信号发射",
  "taskId": "emit-task-1752948445467",
  "signalsCount": 1,
  "isLoopMode": true,
  "modeText": "循环",
  "rawEventData": {
    "task": {
      "id": "emit-task-1752948445467",
      "name": "单个信号发射",
      "type": "single",
      "signals": [
        {
          "id": "signal_77579_1",
          "name": "新学习信号",
          "type": "other",
          "description": "学习的红外信号",
          "signalCode": "0x552aa000000",
          "protocol": "RAW",
          "frequency": "38000",
          "data": "0x552aa000000",
          "isLearned": true,
          "created": 80433,
          "lastSent": 0,
          "sentCount": 0
        }
      ],
      "priority": 2,
      "isManual": true,
      "startTime": 1752948445467,
      "isLoopMode": true
    },
    "source": "ControlModule"
  }
}
  参数2: {
  "taskType": "single",
  "taskName": "单个信号发射",
  "taskId": "emit-task-1752948445467",
  "signalsCount": 1,
  "isLoopMode": true,
  "modeText": "循环",
  "rawEventData": {
    "task": {
      "id": "emit-task-1752948445467",
      "name": "单个信号发射",
      "type": "single",
      "signals": [
        {
          "id": "signal_77579_1",
          "name": "新学习信号",
          "type": "other",
          "description": "学习的红外信号",
          "signalCode": "0x552aa000000",
          "protocol": "RAW",
          "frequency": "38000",
          "data": "0x552aa000000",
          "isLearned": true,
          "created": 80433,
          "lastSent": 0,
          "sentCount": 0
        }
      ],
      "priority": 2,
      "isManual": true,
      "startTime": 1752948445467,
      "isLoopMode": true
    },
    "source": "ControlModule"
  }
}

[361] [+6171ms] [LOG] 2025-07-19T18:07:25.469Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[362] [+6171ms] [LOG] 2025-07-19T18:07:25.469Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[363] [+6172ms] [LOG] 2025-07-19T18:07:25.470Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[364] [+6172ms] [LOG] 2025-07-19T18:07:25.470Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[365] [+6172ms] [LOG] 2025-07-19T18:07:25.470Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[366] [+6172ms] [LOG] 2025-07-19T18:07:25.470Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[367] [+6172ms] [LOG] 2025-07-19T18:07:25.470Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[368] [+6172ms] [LOG] 2025-07-19T18:07:25.470Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[369] [+6202ms] [LOG] 2025-07-19T18:07:25.500Z
⏰ UnifiedTimerManager: 1 个定时器到期

[370] [+6202ms] [LOG] 2025-07-19T18:07:25.500Z
⏰ UnifiedTimerManager: 1 个定时器到期

[371] [+6202ms] [LOG] 2025-07-19T18:07:25.500Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[372] [+6202ms] [LOG] 2025-07-19T18:07:25.500Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[373] [+6202ms] [LOG] 2025-07-19T18:07:25.500Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[374] [+6202ms] [LOG] 2025-07-19T18:07:25.500Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[375] [+6202ms] [LOG] 2025-07-19T18:07:25.500Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:26

[376] [+6202ms] [LOG] 2025-07-19T18:07:25.500Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:26

[377] [+6301ms] [LOG] 2025-07-19T18:07:25.599Z
⏰ UnifiedTimerManager: 1 个定时器到期

[378] [+6302ms] [LOG] 2025-07-19T18:07:25.600Z
⏰ UnifiedTimerManager: 1 个定时器到期

[379] [+6302ms] [LOG] 2025-07-19T18:07:25.600Z
🔥 UnifiedTimerManager: 执行定时器 [notification_notification_48440583], 延迟: 16ms

[380] [+6302ms] [LOG] 2025-07-19T18:07:25.600Z
🔥 UnifiedTimerManager: 执行定时器 [notification_notification_48440583], 延迟: 16ms

[381] [+6302ms] [LOG] 2025-07-19T18:07:25.600Z
➕ UnifiedTimerManager: 添加定时器 [notification_remove_notification_48440583], 间隔: 300ms, 重复: false, 下次执行: 2025/7/20 02:07:25

[382] [+6302ms] [LOG] 2025-07-19T18:07:25.600Z
➕ UnifiedTimerManager: 添加定时器 [notification_remove_notification_48440583], 间隔: 300ms, 重复: false, 下次执行: 2025/7/20 02:07:25

[383] [+6302ms] [LOG] 2025-07-19T18:07:25.600Z
⚠️ UnifiedTimerManager: 主定时器已在运行

[384] [+6302ms] [LOG] 2025-07-19T18:07:25.600Z
⚠️ UnifiedTimerManager: 主定时器已在运行

[385] [+6302ms] [LOG] 2025-07-19T18:07:25.600Z
✅ UnifiedTimerManager: 定时器 [notification_notification_48440583] 执行成功

[386] [+6302ms] [LOG] 2025-07-19T18:07:25.600Z
✅ UnifiedTimerManager: 定时器 [notification_notification_48440583] 执行成功

[387] [+6302ms] [LOG] 2025-07-19T18:07:25.600Z
🗑️ UnifiedTimerManager: 一次性定时器 [notification_notification_48440583] 执行完成，已删除

[388] [+6302ms] [LOG] 2025-07-19T18:07:25.600Z
🗑️ UnifiedTimerManager: 一次性定时器 [notification_notification_48440583] 执行完成，已删除

[389] [+6325ms] [LOG] 2025-07-19T18:07:25.623Z
📨 WebSocket消息: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "success": true,
    "timestamp": 128747
  },
  "timestamp": 1735689728747
}
  参数2: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "success": true,
    "timestamp": 128747
  },
  "timestamp": 1735689728747
}

[390] [+6326ms] [LOG] 2025-07-19T18:07:25.624Z
📨 WebSocket消息: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "success": true,
    "timestamp": 128747
  },
  "timestamp": 1735689728747
}
  参数2: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "success": true,
    "timestamp": 128747
  },
  "timestamp": 1735689728747
}

[391] [+6335ms] [LOG] 2025-07-19T18:07:25.633Z
📡 SignalManager: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 128747
}
  参数2: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 128747
}

[392] [+6335ms] [LOG] 2025-07-19T18:07:25.633Z
📡 SignalManager: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 128747
}
  参数2: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 128747
}

[393] [+6335ms] [LOG] 2025-07-19T18:07:25.633Z
✅ SignalManager: 信号发射成功: 未知信号

[394] [+6335ms] [LOG] 2025-07-19T18:07:25.633Z
✅ SignalManager: 信号发射成功: 未知信号

[395] [+6336ms] [LOG] 2025-07-19T18:07:25.634Z
📡 ControlModule: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 128747
}
  参数2: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 128747
}

[396] [+6336ms] [LOG] 2025-07-19T18:07:25.634Z
📡 ControlModule: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 128747
}
  参数2: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 128747
}

[397] [+6336ms] [LOG] 2025-07-19T18:07:25.634Z
✅ ControlModule: 信号发射成功: 未知信号

[398] [+6336ms] [LOG] 2025-07-19T18:07:25.634Z
✅ ControlModule: 信号发射成功: 未知信号

[399] [+6336ms] [ERROR] 2025-07-19T18:07:25.634Z
Event handler error for esp32.signal_sent: {}
  参数2: {}

[400] [+6336ms] [ERROR] 2025-07-19T18:07:25.634Z
Event handler error for esp32.signal_sent: {}
  参数2: {}

[401] [+6336ms] [LOG] 2025-07-19T18:07:25.634Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[402] [+6336ms] [LOG] 2025-07-19T18:07:25.634Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[403] [+6336ms] [LOG] 2025-07-19T18:07:25.634Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[404] [+6336ms] [LOG] 2025-07-19T18:07:25.634Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[405] [+6336ms] [LOG] 2025-07-19T18:07:25.634Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[406] [+6336ms] [LOG] 2025-07-19T18:07:25.634Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[407] [+6337ms] [LOG] 2025-07-19T18:07:25.635Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[408] [+6337ms] [LOG] 2025-07-19T18:07:25.635Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[409] [+6603ms] [LOG] 2025-07-19T18:07:25.901Z
⏰ UnifiedTimerManager: 1 个定时器到期

[410] [+6603ms] [LOG] 2025-07-19T18:07:25.901Z
⏰ UnifiedTimerManager: 1 个定时器到期

[411] [+6603ms] [LOG] 2025-07-19T18:07:25.901Z
🔥 UnifiedTimerManager: 执行定时器 [notification_remove_notification_48440583], 延迟: 1ms

[412] [+6603ms] [LOG] 2025-07-19T18:07:25.901Z
🔥 UnifiedTimerManager: 执行定时器 [notification_remove_notification_48440583], 延迟: 1ms

[413] [+6603ms] [LOG] 2025-07-19T18:07:25.901Z
✅ UnifiedTimerManager: 定时器 [notification_remove_notification_48440583] 执行成功

[414] [+6603ms] [LOG] 2025-07-19T18:07:25.901Z
✅ UnifiedTimerManager: 定时器 [notification_remove_notification_48440583] 执行成功

[415] [+6603ms] [LOG] 2025-07-19T18:07:25.901Z
🗑️ UnifiedTimerManager: 一次性定时器 [notification_remove_notification_48440583] 执行完成，已删除

[416] [+6603ms] [LOG] 2025-07-19T18:07:25.901Z
🗑️ UnifiedTimerManager: 一次性定时器 [notification_remove_notification_48440583] 执行完成，已删除

[417] [+7212ms] [LOG] 2025-07-19T18:07:26.510Z
⏰ UnifiedTimerManager: 1 个定时器到期

[418] [+7213ms] [LOG] 2025-07-19T18:07:26.511Z
⏰ UnifiedTimerManager: 1 个定时器到期

[419] [+7213ms] [LOG] 2025-07-19T18:07:26.511Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 10ms

[420] [+7213ms] [LOG] 2025-07-19T18:07:26.511Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 10ms

[421] [+7213ms] [LOG] 2025-07-19T18:07:26.511Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[422] [+7213ms] [LOG] 2025-07-19T18:07:26.511Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[423] [+7213ms] [LOG] 2025-07-19T18:07:26.511Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:27

[424] [+7213ms] [LOG] 2025-07-19T18:07:26.511Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:27

[425] [+7264ms] [ERROR] 2025-07-19T18:07:26.562Z
🔍 HTTP错误详情: {
  "status": 400,
  "statusText": "Bad Request",
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "responseHeaders": {
    "accept-ranges": "none",
    "access-control-allow-headers": "Content-Type, Authorization",
    "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS",
    "access-control-allow-origin": "*",
    "access-control-max-age": "86400",
    "connection": "close",
    "content-length": "24",
    "content-type": "application/json"
  },
  "errorBody": "{\"error\":\"Missing body\"}"
}
  参数2: {
  "status": 400,
  "statusText": "Bad Request",
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "responseHeaders": {
    "accept-ranges": "none",
    "access-control-allow-headers": "Content-Type, Authorization",
    "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS",
    "access-control-allow-origin": "*",
    "access-control-max-age": "86400",
    "connection": "close",
    "content-length": "24",
    "content-type": "application/json"
  },
  "errorBody": "{\"error\":\"Missing body\"}"
}

[426] [+7264ms] [ERROR] 2025-07-19T18:07:26.562Z
🔍 HTTP错误详情: {
  "status": 400,
  "statusText": "Bad Request",
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "responseHeaders": {
    "accept-ranges": "none",
    "access-control-allow-headers": "Content-Type, Authorization",
    "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS",
    "access-control-allow-origin": "*",
    "access-control-max-age": "86400",
    "connection": "close",
    "content-length": "24",
    "content-type": "application/json"
  },
  "errorBody": "{\"error\":\"Missing body\"}"
}
  参数2: {
  "status": 400,
  "statusText": "Bad Request",
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "responseHeaders": {
    "accept-ranges": "none",
    "access-control-allow-headers": "Content-Type, Authorization",
    "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS",
    "access-control-allow-origin": "*",
    "access-control-max-age": "86400",
    "connection": "close",
    "content-length": "24",
    "content-type": "application/json"
  },
  "errorBody": "{\"error\":\"Missing body\"}"
}

[427] [+7264ms] [ERROR] 2025-07-19T18:07:26.562Z
ControlModule: 信号发射失败: 新学习信号 {}
  参数2: {}

[428] [+7264ms] [ERROR] 2025-07-19T18:07:26.562Z
ControlModule: 信号发射失败: 新学习信号 {}
  参数2: {}

[429] [+7264ms] [ERROR] 2025-07-19T18:07:26.562Z
ControlModule: 速率控制发射失败: {}
  参数2: {}

[430] [+7264ms] [ERROR] 2025-07-19T18:07:26.562Z
ControlModule: 速率控制发射失败: {}
  参数2: {}

[431] [+7270ms] [LOG] 2025-07-19T18:07:26.568Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[432] [+7270ms] [LOG] 2025-07-19T18:07:26.568Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[433] [+7270ms] [LOG] 2025-07-19T18:07:26.568Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[434] [+7270ms] [LOG] 2025-07-19T18:07:26.568Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[435] [+7271ms] [LOG] 2025-07-19T18:07:26.569Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[436] [+7271ms] [LOG] 2025-07-19T18:07:26.569Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[437] [+7271ms] [LOG] 2025-07-19T18:07:26.569Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[438] [+7271ms] [LOG] 2025-07-19T18:07:26.569Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[439] [+7272ms] [LOG] 2025-07-19T18:07:26.570Z
🔍 SystemMonitor: 收到 control.signal.emit.failed 事件: {
  "signal": {
    "id": "signal_77579_1",
    "name": "新学习信号",
    "type": "other",
    "description": "学习的红外信号",
    "signalCode": "0x552aa000000",
    "protocol": "RAW",
    "frequency": "38000",
    "data": "0x552aa000000",
    "isLearned": true,
    "created": 80433,
    "lastSent": 0,
    "sentCount": 0
  },
  "error": "ESP32发射失败: HTTP 400: Bad Request - {\"error\":\"Missing body\"}",
  "timestamp": 1752948446562,
  "source": "ControlModule"
}
  参数2: {
  "signal": {
    "id": "signal_77579_1",
    "name": "新学习信号",
    "type": "other",
    "description": "学习的红外信号",
    "signalCode": "0x552aa000000",
    "protocol": "RAW",
    "frequency": "38000",
    "data": "0x552aa000000",
    "isLearned": true,
    "created": 80433,
    "lastSent": 0,
    "sentCount": 0
  },
  "error": "ESP32发射失败: HTTP 400: Bad Request - {\"error\":\"Missing body\"}",
  "timestamp": 1752948446562,
  "source": "ControlModule"
}

[440] [+7272ms] [LOG] 2025-07-19T18:07:26.570Z
🔍 SystemMonitor: 收到 control.signal.emit.failed 事件: {
  "signal": {
    "id": "signal_77579_1",
    "name": "新学习信号",
    "type": "other",
    "description": "学习的红外信号",
    "signalCode": "0x552aa000000",
    "protocol": "RAW",
    "frequency": "38000",
    "data": "0x552aa000000",
    "isLearned": true,
    "created": 80433,
    "lastSent": 0,
    "sentCount": 0
  },
  "error": "ESP32发射失败: HTTP 400: Bad Request - {\"error\":\"Missing body\"}",
  "timestamp": 1752948446562,
  "source": "ControlModule"
}
  参数2: {
  "signal": {
    "id": "signal_77579_1",
    "name": "新学习信号",
    "type": "other",
    "description": "学习的红外信号",
    "signalCode": "0x552aa000000",
    "protocol": "RAW",
    "frequency": "38000",
    "data": "0x552aa000000",
    "isLearned": true,
    "created": 80433,
    "lastSent": 0,
    "sentCount": 0
  },
  "error": "ESP32发射失败: HTTP 400: Bad Request - {\"error\":\"Missing body\"}",
  "timestamp": 1752948446562,
  "source": "ControlModule"
}

[441] [+7272ms] [LOG] 2025-07-19T18:07:26.570Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[442] [+7272ms] [LOG] 2025-07-19T18:07:26.570Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[443] [+7272ms] [LOG] 2025-07-19T18:07:26.570Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[444] [+7272ms] [LOG] 2025-07-19T18:07:26.570Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[445] [+7272ms] [LOG] 2025-07-19T18:07:26.570Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[446] [+7273ms] [LOG] 2025-07-19T18:07:26.571Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[447] [+7273ms] [LOG] 2025-07-19T18:07:26.571Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[448] [+7273ms] [LOG] 2025-07-19T18:07:26.571Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[449] [+7273ms] [WARN] 2025-07-19T18:07:26.571Z
⚠️ SignalManager: 信号发射失败: 新学习信号, 错误: ESP32发射失败: HTTP 400: Bad Request - {"error":"Missing body"}

[450] [+7273ms] [WARN] 2025-07-19T18:07:26.571Z
⚠️ SignalManager: 信号发射失败: 新学习信号, 错误: ESP32发射失败: HTTP 400: Bad Request - {"error":"Missing body"}

[451] [+7607ms] [LOG] 2025-07-19T18:07:26.905Z
📨 WebSocket消息: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "signal_name": "新学习信号",
    "timestamp": 128758
  },
  "timestamp": 1735689729669
}
  参数2: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "signal_name": "新学习信号",
    "timestamp": 128758
  },
  "timestamp": 1735689729669
}

[452] [+7607ms] [LOG] 2025-07-19T18:07:26.905Z
📨 WebSocket消息: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "signal_name": "新学习信号",
    "timestamp": 128758
  },
  "timestamp": 1735689729669
}
  参数2: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "signal_name": "新学习信号",
    "timestamp": 128758
  },
  "timestamp": 1735689729669
}

[453] [+7617ms] [LOG] 2025-07-19T18:07:26.915Z
📡 SignalManager: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 128758
}
  参数2: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 128758
}

[454] [+7617ms] [LOG] 2025-07-19T18:07:26.915Z
📡 SignalManager: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 128758
}
  参数2: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 128758
}

[455] [+7617ms] [WARN] 2025-07-19T18:07:26.915Z
❌ SignalManager: 信号发射失败: 未知错误

[456] [+7617ms] [WARN] 2025-07-19T18:07:26.915Z
❌ SignalManager: 信号发射失败: 未知错误

[457] [+7617ms] [LOG] 2025-07-19T18:07:26.915Z
📡 ControlModule: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 128758
}
  参数2: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 128758
}

[458] [+7617ms] [LOG] 2025-07-19T18:07:26.915Z
📡 ControlModule: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 128758
}
  参数2: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 128758
}

[459] [+7617ms] [WARN] 2025-07-19T18:07:26.915Z
❌ ControlModule: 信号发射失败: 未知错误

[460] [+7617ms] [WARN] 2025-07-19T18:07:26.915Z
❌ ControlModule: 信号发射失败: 未知错误

[461] [+7618ms] [ERROR] 2025-07-19T18:07:26.916Z
Event handler error for esp32.signal_sent: {}
  参数2: {}

[462] [+7618ms] [ERROR] 2025-07-19T18:07:26.916Z
Event handler error for esp32.signal_sent: {}
  参数2: {}

[463] [+7618ms] [LOG] 2025-07-19T18:07:26.916Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[464] [+7618ms] [LOG] 2025-07-19T18:07:26.916Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[465] [+7618ms] [LOG] 2025-07-19T18:07:26.916Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[466] [+7618ms] [LOG] 2025-07-19T18:07:26.916Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[467] [+7618ms] [LOG] 2025-07-19T18:07:26.916Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[468] [+7618ms] [LOG] 2025-07-19T18:07:26.916Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[469] [+7619ms] [LOG] 2025-07-19T18:07:26.917Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[470] [+7619ms] [LOG] 2025-07-19T18:07:26.917Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[471] [+8215ms] [LOG] 2025-07-19T18:07:27.513Z
⏰ UnifiedTimerManager: 1 个定时器到期

[472] [+8215ms] [LOG] 2025-07-19T18:07:27.513Z
⏰ UnifiedTimerManager: 1 个定时器到期

[473] [+8215ms] [LOG] 2025-07-19T18:07:27.513Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[474] [+8215ms] [LOG] 2025-07-19T18:07:27.513Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[475] [+8215ms] [LOG] 2025-07-19T18:07:27.513Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[476] [+8215ms] [LOG] 2025-07-19T18:07:27.513Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[477] [+8215ms] [LOG] 2025-07-19T18:07:27.513Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:28

[478] [+8215ms] [LOG] 2025-07-19T18:07:27.513Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:28

[479] [+8266ms] [LOG] 2025-07-19T18:07:27.564Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 29,
  "bodyPreview": "{\"signalId\":\"signal_77579_1\"}"
}
  参数2: {
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 29,
  "bodyPreview": "{\"signalId\":\"signal_77579_1\"}"
}

[480] [+8266ms] [LOG] 2025-07-19T18:07:27.564Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 29,
  "bodyPreview": "{\"signalId\":\"signal_77579_1\"}"
}
  参数2: {
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 29,
  "bodyPreview": "{\"signalId\":\"signal_77579_1\"}"
}

[481] [+8740ms] [LOG] 2025-07-19T18:07:28.038Z
📨 WebSocket消息: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "success": true,
    "timestamp": 131150
  },
  "timestamp": 1735689731151
}
  参数2: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "success": true,
    "timestamp": 131150
  },
  "timestamp": 1735689731151
}

[482] [+8740ms] [LOG] 2025-07-19T18:07:28.038Z
📨 WebSocket消息: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "success": true,
    "timestamp": 131150
  },
  "timestamp": 1735689731151
}
  参数2: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "success": true,
    "timestamp": 131150
  },
  "timestamp": 1735689731151
}

[483] [+8751ms] [LOG] 2025-07-19T18:07:28.049Z
📡 SignalManager: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 131150
}
  参数2: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 131150
}

[484] [+8751ms] [LOG] 2025-07-19T18:07:28.049Z
📡 SignalManager: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 131150
}
  参数2: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 131150
}

[485] [+8751ms] [LOG] 2025-07-19T18:07:28.049Z
✅ SignalManager: 信号发射成功: 未知信号

[486] [+8751ms] [LOG] 2025-07-19T18:07:28.049Z
✅ SignalManager: 信号发射成功: 未知信号

[487] [+8751ms] [LOG] 2025-07-19T18:07:28.049Z
📡 ControlModule: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 131150
}
  参数2: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 131150
}

[488] [+8751ms] [LOG] 2025-07-19T18:07:28.049Z
📡 ControlModule: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 131150
}
  参数2: {
  "signal_id": "signal_77579_1",
  "success": true,
  "timestamp": 131150
}

[489] [+8751ms] [LOG] 2025-07-19T18:07:28.049Z
✅ ControlModule: 信号发射成功: 未知信号

[490] [+8751ms] [LOG] 2025-07-19T18:07:28.049Z
✅ ControlModule: 信号发射成功: 未知信号

[491] [+8751ms] [ERROR] 2025-07-19T18:07:28.049Z
Event handler error for esp32.signal_sent: {}
  参数2: {}

[492] [+8751ms] [ERROR] 2025-07-19T18:07:28.049Z
Event handler error for esp32.signal_sent: {}
  参数2: {}

[493] [+8752ms] [LOG] 2025-07-19T18:07:28.050Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[494] [+8752ms] [LOG] 2025-07-19T18:07:28.050Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[495] [+8752ms] [LOG] 2025-07-19T18:07:28.050Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[496] [+8752ms] [LOG] 2025-07-19T18:07:28.050Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[497] [+8752ms] [LOG] 2025-07-19T18:07:28.050Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[498] [+8752ms] [LOG] 2025-07-19T18:07:28.050Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[499] [+8752ms] [LOG] 2025-07-19T18:07:28.050Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[500] [+8752ms] [LOG] 2025-07-19T18:07:28.050Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[501] [+9301ms] [LOG] 2025-07-19T18:07:28.599Z
⏰ UnifiedTimerManager: 1 个定时器到期

[502] [+9301ms] [LOG] 2025-07-19T18:07:28.599Z
⏰ UnifiedTimerManager: 1 个定时器到期

[503] [+9301ms] [LOG] 2025-07-19T18:07:28.599Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 86ms

[504] [+9301ms] [LOG] 2025-07-19T18:07:28.599Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 86ms

[505] [+9301ms] [LOG] 2025-07-19T18:07:28.599Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[506] [+9301ms] [LOG] 2025-07-19T18:07:28.599Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[507] [+9301ms] [LOG] 2025-07-19T18:07:28.599Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:29

[508] [+9301ms] [LOG] 2025-07-19T18:07:28.599Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:29

[509] [+9584ms] [LOG] 2025-07-19T18:07:28.882Z
🔄 [direct] switchModule开始: signal-manager -> controlModule (9729.600ms)

[510] [+9584ms] [LOG] 2025-07-19T18:07:28.882Z
🔄 [direct] switchModule开始: signal-manager -> controlModule (9729.600ms)

[511] [+9584ms] [LOG] 2025-07-19T18:07:28.882Z
🔄 [direct] 步骤1-状态更新: 0.000ms

[512] [+9584ms] [LOG] 2025-07-19T18:07:28.882Z
🔄 [direct] 步骤1-状态更新: 0.000ms

[513] [+9584ms] [LOG] 2025-07-19T18:07:28.882Z
🔧 [direct] 模块ID映射: signal-manager -> signal-manager, controlModule -> control-module

[514] [+9584ms] [LOG] 2025-07-19T18:07:28.882Z
🔧 [direct] 模块ID映射: signal-manager -> signal-manager, controlModule -> control-module

[515] [+9584ms] [LOG] 2025-07-19T18:07:28.882Z
🔄 [direct] 步骤2-DOM查询: 0.000ms {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}
  参数2: {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}

[516] [+9584ms] [LOG] 2025-07-19T18:07:28.882Z
🔄 [direct] 步骤2-DOM查询: 0.000ms {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}
  参数2: {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}

[517] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🔄 [direct] 隐藏面板: signal-manager (ID: signal-manager)

[518] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🔄 [direct] 隐藏面板: signal-manager (ID: signal-manager)

[519] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🎯 [direct] 隐藏前CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[520] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🎯 [direct] 隐藏前CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[521] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🎯 [direct] 隐藏后CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[522] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🎯 [direct] 隐藏后CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[523] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🔄 [direct] 步骤3-隐藏面板: 0.700ms

[524] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🔄 [direct] 步骤3-隐藏面板: 0.700ms

[525] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🔄 [direct] 显示面板: controlModule (ID: control-module)

[526] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🔄 [direct] 显示面板: controlModule (ID: control-module)

[527] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🎯 [direct] 显示前CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[528] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🎯 [direct] 显示前CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[529] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🎯 [direct] 显示后CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[530] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🎯 [direct] 显示后CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[531] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🔄 [direct] 步骤4-显示面板: 0.200ms

[532] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🔄 [direct] 步骤4-显示面板: 0.200ms

[533] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🔄 [direct] 更新3个标签状态

[534] [+9585ms] [LOG] 2025-07-19T18:07:28.883Z
🔄 [direct] 更新3个标签状态

[535] [+9586ms] [LOG] 2025-07-19T18:07:28.884Z
🔄 [direct] 步骤5-清除标签: 0.100ms

[536] [+9586ms] [LOG] 2025-07-19T18:07:28.884Z
🔄 [direct] 步骤5-清除标签: 0.100ms

[537] [+9586ms] [LOG] 2025-07-19T18:07:28.884Z
🔄 [direct] 激活标签: controlModule (data-module: control-module)

[538] [+9586ms] [LOG] 2025-07-19T18:07:28.884Z
🔄 [direct] 激活标签: controlModule (data-module: control-module)

[539] [+9586ms] [LOG] 2025-07-19T18:07:28.884Z
🔄 [direct] 步骤6-激活标签: 0.100ms

[540] [+9586ms] [LOG] 2025-07-19T18:07:28.884Z
🔄 [direct] 步骤6-激活标签: 0.100ms

[541] [+9586ms] [LOG] 2025-07-19T18:07:28.884Z
🏁 [direct] switchModule完成: 1.600ms

[542] [+9586ms] [LOG] 2025-07-19T18:07:28.884Z
🏁 [direct] switchModule完成: 1.600ms

[543] [+9586ms] [LOG] 2025-07-19T18:07:28.884Z
📊 [direct] 性能分解: {
  "状态更新": "0.000ms",
  "DOM查询": "0.000ms",
  "隐藏面板": "0.700ms",
  "显示面板": "0.200ms",
  "清除标签": "0.100ms",
  "激活标签": "0.100ms",
  "总计": "1.600ms"
}
  参数2: {
  "状态更新": "0.000ms",
  "DOM查询": "0.000ms",
  "隐藏面板": "0.700ms",
  "显示面板": "0.200ms",
  "清除标签": "0.100ms",
  "激活标签": "0.100ms",
  "总计": "1.600ms"
}

[544] [+9586ms] [LOG] 2025-07-19T18:07:28.884Z
📊 [direct] 性能分解: {
  "状态更新": "0.000ms",
  "DOM查询": "0.000ms",
  "隐藏面板": "0.700ms",
  "显示面板": "0.200ms",
  "清除标签": "0.100ms",
  "激活标签": "0.100ms",
  "总计": "1.600ms"
}
  参数2: {
  "状态更新": "0.000ms",
  "DOM查询": "0.000ms",
  "隐藏面板": "0.700ms",
  "显示面板": "0.200ms",
  "清除标签": "0.100ms",
  "激活标签": "0.100ms",
  "总计": "1.600ms"
}

[545] [+9650ms] [ERROR] 2025-07-19T18:07:28.948Z
🔍 HTTP错误详情: {
  "status": 400,
  "statusText": "Bad Request",
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "responseHeaders": {
    "accept-ranges": "none",
    "access-control-allow-headers": "Content-Type, Authorization",
    "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS",
    "access-control-allow-origin": "*",
    "access-control-max-age": "86400",
    "connection": "close",
    "content-length": "24",
    "content-type": "application/json"
  },
  "errorBody": "{\"error\":\"Missing body\"}"
}
  参数2: {
  "status": 400,
  "statusText": "Bad Request",
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "responseHeaders": {
    "accept-ranges": "none",
    "access-control-allow-headers": "Content-Type, Authorization",
    "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS",
    "access-control-allow-origin": "*",
    "access-control-max-age": "86400",
    "connection": "close",
    "content-length": "24",
    "content-type": "application/json"
  },
  "errorBody": "{\"error\":\"Missing body\"}"
}

[546] [+9651ms] [ERROR] 2025-07-19T18:07:28.949Z
🔍 HTTP错误详情: {
  "status": 400,
  "statusText": "Bad Request",
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "responseHeaders": {
    "accept-ranges": "none",
    "access-control-allow-headers": "Content-Type, Authorization",
    "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS",
    "access-control-allow-origin": "*",
    "access-control-max-age": "86400",
    "connection": "close",
    "content-length": "24",
    "content-type": "application/json"
  },
  "errorBody": "{\"error\":\"Missing body\"}"
}
  参数2: {
  "status": 400,
  "statusText": "Bad Request",
  "url": "http://***********:8000/api/emit/signal",
  "method": "POST",
  "responseHeaders": {
    "accept-ranges": "none",
    "access-control-allow-headers": "Content-Type, Authorization",
    "access-control-allow-methods": "GET, POST, PUT, DELETE, OPTIONS",
    "access-control-allow-origin": "*",
    "access-control-max-age": "86400",
    "connection": "close",
    "content-length": "24",
    "content-type": "application/json"
  },
  "errorBody": "{\"error\":\"Missing body\"}"
}

[547] [+9651ms] [ERROR] 2025-07-19T18:07:28.949Z
ControlModule: 信号发射失败: 新学习信号 {}
  参数2: {}

[548] [+9651ms] [ERROR] 2025-07-19T18:07:28.949Z
ControlModule: 信号发射失败: 新学习信号 {}
  参数2: {}

[549] [+9651ms] [ERROR] 2025-07-19T18:07:28.949Z
ControlModule: 速率控制发射失败: {}
  参数2: {}

[550] [+9651ms] [ERROR] 2025-07-19T18:07:28.949Z
ControlModule: 速率控制发射失败: {}
  参数2: {}

[551] [+9668ms] [LOG] 2025-07-19T18:07:28.966Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[552] [+9668ms] [LOG] 2025-07-19T18:07:28.966Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[553] [+9668ms] [LOG] 2025-07-19T18:07:28.966Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[554] [+9668ms] [LOG] 2025-07-19T18:07:28.966Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[555] [+9668ms] [LOG] 2025-07-19T18:07:28.966Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[556] [+9669ms] [LOG] 2025-07-19T18:07:28.967Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[557] [+9669ms] [LOG] 2025-07-19T18:07:28.967Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[558] [+9669ms] [LOG] 2025-07-19T18:07:28.967Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[559] [+9669ms] [LOG] 2025-07-19T18:07:28.967Z
🔍 SystemMonitor: 收到 control.signal.emit.failed 事件: {
  "signal": {
    "id": "signal_77579_1",
    "name": "新学习信号",
    "type": "other",
    "description": "学习的红外信号",
    "signalCode": "0x552aa000000",
    "protocol": "RAW",
    "frequency": "38000",
    "data": "0x552aa000000",
    "isLearned": true,
    "created": 80433,
    "lastSent": 0,
    "sentCount": 0
  },
  "error": "ESP32发射失败: HTTP 400: Bad Request - {\"error\":\"Missing body\"}",
  "timestamp": 1752948448949,
  "source": "ControlModule"
}
  参数2: {
  "signal": {
    "id": "signal_77579_1",
    "name": "新学习信号",
    "type": "other",
    "description": "学习的红外信号",
    "signalCode": "0x552aa000000",
    "protocol": "RAW",
    "frequency": "38000",
    "data": "0x552aa000000",
    "isLearned": true,
    "created": 80433,
    "lastSent": 0,
    "sentCount": 0
  },
  "error": "ESP32发射失败: HTTP 400: Bad Request - {\"error\":\"Missing body\"}",
  "timestamp": 1752948448949,
  "source": "ControlModule"
}

[560] [+9669ms] [LOG] 2025-07-19T18:07:28.967Z
🔍 SystemMonitor: 收到 control.signal.emit.failed 事件: {
  "signal": {
    "id": "signal_77579_1",
    "name": "新学习信号",
    "type": "other",
    "description": "学习的红外信号",
    "signalCode": "0x552aa000000",
    "protocol": "RAW",
    "frequency": "38000",
    "data": "0x552aa000000",
    "isLearned": true,
    "created": 80433,
    "lastSent": 0,
    "sentCount": 0
  },
  "error": "ESP32发射失败: HTTP 400: Bad Request - {\"error\":\"Missing body\"}",
  "timestamp": 1752948448949,
  "source": "ControlModule"
}
  参数2: {
  "signal": {
    "id": "signal_77579_1",
    "name": "新学习信号",
    "type": "other",
    "description": "学习的红外信号",
    "signalCode": "0x552aa000000",
    "protocol": "RAW",
    "frequency": "38000",
    "data": "0x552aa000000",
    "isLearned": true,
    "created": 80433,
    "lastSent": 0,
    "sentCount": 0
  },
  "error": "ESP32发射失败: HTTP 400: Bad Request - {\"error\":\"Missing body\"}",
  "timestamp": 1752948448949,
  "source": "ControlModule"
}

[561] [+9670ms] [LOG] 2025-07-19T18:07:28.968Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[562] [+9670ms] [LOG] 2025-07-19T18:07:28.968Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[563] [+9670ms] [LOG] 2025-07-19T18:07:28.968Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[564] [+9670ms] [LOG] 2025-07-19T18:07:28.968Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[565] [+9670ms] [LOG] 2025-07-19T18:07:28.968Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[566] [+9670ms] [LOG] 2025-07-19T18:07:28.968Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[567] [+9670ms] [LOG] 2025-07-19T18:07:28.968Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[568] [+9670ms] [LOG] 2025-07-19T18:07:28.968Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[569] [+9671ms] [WARN] 2025-07-19T18:07:28.969Z
⚠️ SignalManager: 信号发射失败: 新学习信号, 错误: ESP32发射失败: HTTP 400: Bad Request - {"error":"Missing body"}

[570] [+9671ms] [WARN] 2025-07-19T18:07:28.969Z
⚠️ SignalManager: 信号发射失败: 新学习信号, 错误: ESP32发射失败: HTTP 400: Bad Request - {"error":"Missing body"}

[571] [+9984ms] [LOG] 2025-07-19T18:07:29.282Z
📨 WebSocket消息: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "signal_name": "新学习信号",
    "timestamp": 131162
  },
  "timestamp": 1735689732069
}
  参数2: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "signal_name": "新学习信号",
    "timestamp": 131162
  },
  "timestamp": 1735689732069
}

[572] [+9984ms] [LOG] 2025-07-19T18:07:29.282Z
📨 WebSocket消息: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "signal_name": "新学习信号",
    "timestamp": 131162
  },
  "timestamp": 1735689732069
}
  参数2: {
  "type": "signal_sent",
  "payload": {
    "signal_id": "signal_77579_1",
    "signal_name": "新学习信号",
    "timestamp": 131162
  },
  "timestamp": 1735689732069
}

[573] [+9984ms] [LOG] 2025-07-19T18:07:29.282Z
📡 SignalManager: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 131162
}
  参数2: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 131162
}

[574] [+9984ms] [LOG] 2025-07-19T18:07:29.282Z
📡 SignalManager: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 131162
}
  参数2: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 131162
}

[575] [+9984ms] [WARN] 2025-07-19T18:07:29.282Z
❌ SignalManager: 信号发射失败: 未知错误

[576] [+9984ms] [WARN] 2025-07-19T18:07:29.282Z
❌ SignalManager: 信号发射失败: 未知错误

[577] [+9984ms] [LOG] 2025-07-19T18:07:29.282Z
📡 ControlModule: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 131162
}
  参数2: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 131162
}

[578] [+9984ms] [LOG] 2025-07-19T18:07:29.282Z
📡 ControlModule: 收到信号发射完成事件: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 131162
}
  参数2: {
  "signal_id": "signal_77579_1",
  "signal_name": "新学习信号",
  "timestamp": 131162
}

[579] [+9984ms] [WARN] 2025-07-19T18:07:29.282Z
❌ ControlModule: 信号发射失败: 未知错误

[580] [+9984ms] [WARN] 2025-07-19T18:07:29.282Z
❌ ControlModule: 信号发射失败: 未知错误

[581] [+9984ms] [ERROR] 2025-07-19T18:07:29.282Z
Event handler error for esp32.signal_sent: {}
  参数2: {}

[582] [+9984ms] [ERROR] 2025-07-19T18:07:29.282Z
Event handler error for esp32.signal_sent: {}
  参数2: {}

[583] [+9984ms] [LOG] 2025-07-19T18:07:29.282Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[584] [+9984ms] [LOG] 2025-07-19T18:07:29.282Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[585] [+9984ms] [LOG] 2025-07-19T18:07:29.282Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[586] [+9984ms] [LOG] 2025-07-19T18:07:29.282Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[587] [+9984ms] [LOG] 2025-07-19T18:07:29.282Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[588] [+9984ms] [LOG] 2025-07-19T18:07:29.282Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[589] [+9985ms] [LOG] 2025-07-19T18:07:29.283Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[590] [+9985ms] [LOG] 2025-07-19T18:07:29.283Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[591] [+10302ms] [LOG] 2025-07-19T18:07:29.600Z
⏰ UnifiedTimerManager: 1 个定时器到期

[592] [+10302ms] [LOG] 2025-07-19T18:07:29.600Z
⏰ UnifiedTimerManager: 1 个定时器到期

[593] [+10302ms] [LOG] 2025-07-19T18:07:29.600Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[594] [+10302ms] [LOG] 2025-07-19T18:07:29.600Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[595] [+10303ms] [LOG] 2025-07-19T18:07:29.601Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[596] [+10303ms] [LOG] 2025-07-19T18:07:29.601Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[597] [+10303ms] [LOG] 2025-07-19T18:07:29.601Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:30

[598] [+10303ms] [LOG] 2025-07-19T18:07:29.601Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:30

[599] [+10437ms] [LOG] 2025-07-19T18:07:29.735Z
ControlModule: 发射已停止

[600] [+10437ms] [LOG] 2025-07-19T18:07:29.735Z
ControlModule: 发射已停止

[601] [+10437ms] [LOG] 2025-07-19T18:07:29.735Z
✅ ControlModule: 保存了 1 条任务历史

[602] [+10437ms] [LOG] 2025-07-19T18:07:29.735Z
✅ ControlModule: 保存了 1 条任务历史

[603] [+10437ms] [LOG] 2025-07-19T18:07:29.735Z
✅ ControlModule: 任务已添加到历史: 单个信号发射

[604] [+10437ms] [LOG] 2025-07-19T18:07:29.735Z
✅ ControlModule: 任务已添加到历史: 单个信号发射

[605] [+10450ms] [LOG] 2025-07-19T18:07:29.748Z
📊 StatusDisplay: updateSystemStatus 被调用 - status: idle, text: 待机

[606] [+10450ms] [LOG] 2025-07-19T18:07:29.748Z
📊 StatusDisplay: updateSystemStatus 被调用 - status: idle, text: 待机

[607] [+10450ms] [LOG] 2025-07-19T18:07:29.748Z
📊 StatusDisplay: 状态更新完成 - 显示文本: 待机, 颜色: var(--text-secondary)

[608] [+10450ms] [LOG] 2025-07-19T18:07:29.748Z
📊 StatusDisplay: 状态更新完成 - 显示文本: 待机, 颜色: var(--text-secondary)

[609] [+10450ms] [LOG] 2025-07-19T18:07:29.748Z
➕ UnifiedTimerManager: 添加定时器 [status_reset_after_stop], 间隔: 2000ms, 重复: false, 下次执行: 2025/7/20 02:07:31

[610] [+10450ms] [LOG] 2025-07-19T18:07:29.748Z
➕ UnifiedTimerManager: 添加定时器 [status_reset_after_stop], 间隔: 2000ms, 重复: false, 下次执行: 2025/7/20 02:07:31

[611] [+10450ms] [LOG] 2025-07-19T18:07:29.748Z
⚠️ UnifiedTimerManager: 主定时器已在运行

[612] [+10450ms] [LOG] 2025-07-19T18:07:29.748Z
⚠️ UnifiedTimerManager: 主定时器已在运行

[613] [+10451ms] [LOG] 2025-07-19T18:07:29.749Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[614] [+10451ms] [LOG] 2025-07-19T18:07:29.749Z
🔍 SystemMonitor ESP32通信器状态: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}
  参数2: {
  "esp32Exists": true,
  "esp32Type": "object",
  "esp32Connected": true,
  "esp32Methods": [
    "eventBus",
    "baseURL",
    "wsURL",
    "ws",
    "connected",
    "reconnectAttempts",
    "maxReconnectAttempts",
    "reconnectDelay",
    "requestQueue",
    "batchTimer",
    "batchDelay",
    "performance"
  ],
  "requestESP32Method": "function"
}

[615] [+10451ms] [LOG] 2025-07-19T18:07:29.749Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[616] [+10451ms] [LOG] 2025-07-19T18:07:29.749Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/stats",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[617] [+10451ms] [LOG] 2025-07-19T18:07:29.749Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[618] [+10451ms] [LOG] 2025-07-19T18:07:29.749Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/memory",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[619] [+10451ms] [LOG] 2025-07-19T18:07:29.749Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[620] [+10451ms] [LOG] 2025-07-19T18:07:29.749Z
🔍 ESP32请求调试信息: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}
  参数2: {
  "url": "http://***********:8000/api/system/performance",
  "method": "GET",
  "headers": {
    "Content-Type": "application/json"
  },
  "bodyLength": 0,
  "bodyPreview": null
}

[621] [+11314ms] [LOG] 2025-07-19T18:07:30.612Z
⏰ UnifiedTimerManager: 1 个定时器到期

[622] [+11314ms] [LOG] 2025-07-19T18:07:30.612Z
⏰ UnifiedTimerManager: 1 个定时器到期

[623] [+11314ms] [LOG] 2025-07-19T18:07:30.612Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 12ms

[624] [+11314ms] [LOG] 2025-07-19T18:07:30.612Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 12ms

[625] [+11314ms] [LOG] 2025-07-19T18:07:30.612Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[626] [+11314ms] [LOG] 2025-07-19T18:07:30.612Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[627] [+11315ms] [LOG] 2025-07-19T18:07:30.613Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:31

[628] [+11315ms] [LOG] 2025-07-19T18:07:30.613Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:31

[629] [+12415ms] [LOG] 2025-07-19T18:07:31.713Z
⏰ UnifiedTimerManager: 1 个定时器到期

[630] [+12415ms] [LOG] 2025-07-19T18:07:31.713Z
⏰ UnifiedTimerManager: 1 个定时器到期

[631] [+12415ms] [LOG] 2025-07-19T18:07:31.713Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 101ms

[632] [+12415ms] [LOG] 2025-07-19T18:07:31.713Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 101ms

[633] [+12415ms] [LOG] 2025-07-19T18:07:31.713Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[634] [+12415ms] [LOG] 2025-07-19T18:07:31.713Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[635] [+12415ms] [LOG] 2025-07-19T18:07:31.713Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:32

[636] [+12415ms] [LOG] 2025-07-19T18:07:31.713Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:32

[637] [+12507ms] [LOG] 2025-07-19T18:07:31.805Z
⏰ UnifiedTimerManager: 1 个定时器到期

[638] [+12507ms] [LOG] 2025-07-19T18:07:31.805Z
⏰ UnifiedTimerManager: 1 个定时器到期

[639] [+12507ms] [LOG] 2025-07-19T18:07:31.805Z
🔥 UnifiedTimerManager: 执行定时器 [status_reset_after_stop], 延迟: 57ms

[640] [+12507ms] [LOG] 2025-07-19T18:07:31.805Z
🔥 UnifiedTimerManager: 执行定时器 [status_reset_after_stop], 延迟: 57ms

[641] [+12508ms] [LOG] 2025-07-19T18:07:31.806Z
📊 StatusDisplay: 任务显示已完全清空到初始化状态

[642] [+12508ms] [LOG] 2025-07-19T18:07:31.806Z
📊 StatusDisplay: 任务显示已完全清空到初始化状态

[643] [+12508ms] [LOG] 2025-07-19T18:07:31.806Z
📊 StatusDisplay: updateSystemStatus 被调用 - status: idle, text: 待机

[644] [+12508ms] [LOG] 2025-07-19T18:07:31.806Z
📊 StatusDisplay: updateSystemStatus 被调用 - status: idle, text: 待机

[645] [+12508ms] [LOG] 2025-07-19T18:07:31.806Z
📊 StatusDisplay: 状态更新完成 - 显示文本: 待机, 颜色: var(--text-secondary)

[646] [+12508ms] [LOG] 2025-07-19T18:07:31.806Z
📊 StatusDisplay: 状态更新完成 - 显示文本: 待机, 颜色: var(--text-secondary)

[647] [+12508ms] [LOG] 2025-07-19T18:07:31.806Z
✅ UnifiedTimerManager: 定时器 [status_reset_after_stop] 执行成功

[648] [+12508ms] [LOG] 2025-07-19T18:07:31.806Z
✅ UnifiedTimerManager: 定时器 [status_reset_after_stop] 执行成功

[649] [+12508ms] [LOG] 2025-07-19T18:07:31.806Z
🗑️ UnifiedTimerManager: 一次性定时器 [status_reset_after_stop] 执行完成，已删除

[650] [+12508ms] [LOG] 2025-07-19T18:07:31.806Z
🗑️ UnifiedTimerManager: 一次性定时器 [status_reset_after_stop] 执行完成，已删除

[651] [+12714ms] [LOG] 2025-07-19T18:07:32.012Z
🔄 [direct] switchModule开始: controlModule -> signalManager (12859.300ms)

[652] [+12714ms] [LOG] 2025-07-19T18:07:32.012Z
🔄 [direct] switchModule开始: controlModule -> signalManager (12859.300ms)

[653] [+12714ms] [LOG] 2025-07-19T18:07:32.012Z
🔄 [direct] 步骤1-状态更新: 0.000ms

[654] [+12714ms] [LOG] 2025-07-19T18:07:32.012Z
🔄 [direct] 步骤1-状态更新: 0.000ms

[655] [+12714ms] [LOG] 2025-07-19T18:07:32.012Z
🔧 [direct] 模块ID映射: controlModule -> control-module, signalManager -> signal-manager

[656] [+12714ms] [LOG] 2025-07-19T18:07:32.012Z
🔧 [direct] 模块ID映射: controlModule -> control-module, signalManager -> signal-manager

[657] [+12714ms] [LOG] 2025-07-19T18:07:32.012Z
🔄 [direct] 步骤2-DOM查询: 0.200ms {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}
  参数2: {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}

[658] [+12714ms] [LOG] 2025-07-19T18:07:32.012Z
🔄 [direct] 步骤2-DOM查询: 0.200ms {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}
  参数2: {
  "currentPanel": true,
  "newPanel": true,
  "allTabsCount": 3,
  "activeTab": true
}

[659] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🔄 [direct] 隐藏面板: controlModule (ID: control-module)

[660] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🔄 [direct] 隐藏面板: controlModule (ID: control-module)

[661] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🎯 [direct] 隐藏前CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[662] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🎯 [direct] 隐藏前CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[663] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🎯 [direct] 隐藏后CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[664] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🎯 [direct] 隐藏后CSS状态: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}
  参数2: {
  "opacity": "1",
  "visibility": "visible",
  "display": "block"
}

[665] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🔄 [direct] 步骤3-隐藏面板: 0.700ms

[666] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🔄 [direct] 步骤3-隐藏面板: 0.700ms

[667] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🔄 [direct] 显示面板: signalManager (ID: signal-manager)

[668] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🔄 [direct] 显示面板: signalManager (ID: signal-manager)

[669] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🎯 [direct] 显示前CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[670] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🎯 [direct] 显示前CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[671] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🎯 [direct] 显示后CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[672] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🎯 [direct] 显示后CSS状态: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}
  参数2: {
  "opacity": "0",
  "visibility": "hidden",
  "display": "block"
}

[673] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🔄 [direct] 步骤4-显示面板: 0.300ms

[674] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🔄 [direct] 步骤4-显示面板: 0.300ms

[675] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🔄 [direct] 更新3个标签状态

[676] [+12715ms] [LOG] 2025-07-19T18:07:32.013Z
🔄 [direct] 更新3个标签状态

[677] [+12716ms] [LOG] 2025-07-19T18:07:32.014Z
🔄 [direct] 步骤5-清除标签: 0.100ms

[678] [+12716ms] [LOG] 2025-07-19T18:07:32.014Z
🔄 [direct] 步骤5-清除标签: 0.100ms

[679] [+12716ms] [LOG] 2025-07-19T18:07:32.014Z
🔄 [direct] 激活标签: signalManager (data-module: signal-manager)

[680] [+12716ms] [LOG] 2025-07-19T18:07:32.014Z
🔄 [direct] 激活标签: signalManager (data-module: signal-manager)

[681] [+12716ms] [LOG] 2025-07-19T18:07:32.014Z
🔄 [direct] 步骤6-激活标签: 0.000ms

[682] [+12716ms] [LOG] 2025-07-19T18:07:32.014Z
🔄 [direct] 步骤6-激活标签: 0.000ms

[683] [+12716ms] [LOG] 2025-07-19T18:07:32.014Z
🏁 [direct] switchModule完成: 1.900ms

[684] [+12716ms] [LOG] 2025-07-19T18:07:32.014Z
🏁 [direct] switchModule完成: 1.900ms

[685] [+12716ms] [LOG] 2025-07-19T18:07:32.014Z
📊 [direct] 性能分解: {
  "状态更新": "0.000ms",
  "DOM查询": "0.200ms",
  "隐藏面板": "0.700ms",
  "显示面板": "0.300ms",
  "清除标签": "0.100ms",
  "激活标签": "0.000ms",
  "总计": "1.900ms"
}
  参数2: {
  "状态更新": "0.000ms",
  "DOM查询": "0.200ms",
  "隐藏面板": "0.700ms",
  "显示面板": "0.300ms",
  "清除标签": "0.100ms",
  "激活标签": "0.000ms",
  "总计": "1.900ms"
}

[686] [+12716ms] [LOG] 2025-07-19T18:07:32.014Z
📊 [direct] 性能分解: {
  "状态更新": "0.000ms",
  "DOM查询": "0.200ms",
  "隐藏面板": "0.700ms",
  "显示面板": "0.300ms",
  "清除标签": "0.100ms",
  "激活标签": "0.000ms",
  "总计": "1.900ms"
}
  参数2: {
  "状态更新": "0.000ms",
  "DOM查询": "0.200ms",
  "隐藏面板": "0.700ms",
  "显示面板": "0.300ms",
  "清除标签": "0.100ms",
  "激活标签": "0.000ms",
  "总计": "1.900ms"
}

[687] [+13505ms] [LOG] 2025-07-19T18:07:32.803Z
⏰ UnifiedTimerManager: 1 个定时器到期

[688] [+13505ms] [LOG] 2025-07-19T18:07:32.803Z
⏰ UnifiedTimerManager: 1 个定时器到期

[689] [+13505ms] [LOG] 2025-07-19T18:07:32.803Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 90ms

[690] [+13505ms] [LOG] 2025-07-19T18:07:32.803Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 90ms

[691] [+13505ms] [LOG] 2025-07-19T18:07:32.803Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[692] [+13505ms] [LOG] 2025-07-19T18:07:32.803Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[693] [+13505ms] [LOG] 2025-07-19T18:07:32.803Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:33

[694] [+13505ms] [LOG] 2025-07-19T18:07:32.803Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:33

[695] [+14509ms] [LOG] 2025-07-19T18:07:33.807Z
⏰ UnifiedTimerManager: 1 个定时器到期

[696] [+14509ms] [LOG] 2025-07-19T18:07:33.807Z
⏰ UnifiedTimerManager: 1 个定时器到期

[697] [+14509ms] [LOG] 2025-07-19T18:07:33.807Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[698] [+14509ms] [LOG] 2025-07-19T18:07:33.807Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[699] [+14510ms] [LOG] 2025-07-19T18:07:33.808Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[700] [+14510ms] [LOG] 2025-07-19T18:07:33.808Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[701] [+14510ms] [LOG] 2025-07-19T18:07:33.808Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:34

[702] [+14510ms] [LOG] 2025-07-19T18:07:33.808Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:34

[703] [+15601ms] [LOG] 2025-07-19T18:07:34.899Z
⏰ UnifiedTimerManager: 1 个定时器到期

[704] [+15601ms] [LOG] 2025-07-19T18:07:34.899Z
⏰ UnifiedTimerManager: 1 个定时器到期

[705] [+15601ms] [LOG] 2025-07-19T18:07:34.899Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[706] [+15601ms] [LOG] 2025-07-19T18:07:34.899Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[707] [+15601ms] [LOG] 2025-07-19T18:07:34.899Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[708] [+15602ms] [LOG] 2025-07-19T18:07:34.900Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[709] [+15602ms] [LOG] 2025-07-19T18:07:34.900Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:35

[710] [+15602ms] [LOG] 2025-07-19T18:07:34.900Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:35

[711] [+16613ms] [LOG] 2025-07-19T18:07:35.911Z
⏰ UnifiedTimerManager: 1 个定时器到期

[712] [+16613ms] [LOG] 2025-07-19T18:07:35.911Z
⏰ UnifiedTimerManager: 1 个定时器到期

[713] [+16613ms] [LOG] 2025-07-19T18:07:35.911Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 12ms

[714] [+16613ms] [LOG] 2025-07-19T18:07:35.911Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 12ms

[715] [+16613ms] [LOG] 2025-07-19T18:07:35.911Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[716] [+16613ms] [LOG] 2025-07-19T18:07:35.911Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[717] [+16613ms] [LOG] 2025-07-19T18:07:35.911Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:36

[718] [+16613ms] [LOG] 2025-07-19T18:07:35.911Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:36

[719] [+17711ms] [LOG] 2025-07-19T18:07:37.009Z
⏰ UnifiedTimerManager: 1 个定时器到期

[720] [+17711ms] [LOG] 2025-07-19T18:07:37.009Z
⏰ UnifiedTimerManager: 1 个定时器到期

[721] [+17712ms] [LOG] 2025-07-19T18:07:37.010Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[722] [+17712ms] [LOG] 2025-07-19T18:07:37.010Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[723] [+17712ms] [LOG] 2025-07-19T18:07:37.010Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[724] [+17712ms] [LOG] 2025-07-19T18:07:37.010Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[725] [+17712ms] [LOG] 2025-07-19T18:07:37.010Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:38

[726] [+17713ms] [LOG] 2025-07-19T18:07:37.011Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:38

[727] [+18802ms] [LOG] 2025-07-19T18:07:38.100Z
⏰ UnifiedTimerManager: 1 个定时器到期

[728] [+18802ms] [LOG] 2025-07-19T18:07:38.100Z
⏰ UnifiedTimerManager: 1 个定时器到期

[729] [+18802ms] [LOG] 2025-07-19T18:07:38.100Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[730] [+18802ms] [LOG] 2025-07-19T18:07:38.100Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[731] [+18802ms] [LOG] 2025-07-19T18:07:38.100Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[732] [+18802ms] [LOG] 2025-07-19T18:07:38.100Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[733] [+18802ms] [LOG] 2025-07-19T18:07:38.100Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:39

[734] [+18802ms] [LOG] 2025-07-19T18:07:38.100Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:39

[735] [+19804ms] [LOG] 2025-07-19T18:07:39.102Z
⏰ UnifiedTimerManager: 1 个定时器到期

[736] [+19804ms] [LOG] 2025-07-19T18:07:39.102Z
⏰ UnifiedTimerManager: 1 个定时器到期

[737] [+19804ms] [LOG] 2025-07-19T18:07:39.102Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[738] [+19804ms] [LOG] 2025-07-19T18:07:39.102Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[739] [+19804ms] [LOG] 2025-07-19T18:07:39.102Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[740] [+19804ms] [LOG] 2025-07-19T18:07:39.102Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[741] [+19805ms] [LOG] 2025-07-19T18:07:39.103Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:40

[742] [+19805ms] [LOG] 2025-07-19T18:07:39.103Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:40

[743] [+20911ms] [LOG] 2025-07-19T18:07:40.209Z
⏰ UnifiedTimerManager: 1 个定时器到期

[744] [+20911ms] [LOG] 2025-07-19T18:07:40.209Z
⏰ UnifiedTimerManager: 1 个定时器到期

[745] [+20911ms] [LOG] 2025-07-19T18:07:40.209Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 107ms

[746] [+20911ms] [LOG] 2025-07-19T18:07:40.209Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 107ms

[747] [+20912ms] [LOG] 2025-07-19T18:07:40.210Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[748] [+20912ms] [LOG] 2025-07-19T18:07:40.210Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[749] [+20912ms] [LOG] 2025-07-19T18:07:40.210Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:41

[750] [+20912ms] [LOG] 2025-07-19T18:07:40.210Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:41

[751] [+22010ms] [LOG] 2025-07-19T18:07:41.308Z
⏰ UnifiedTimerManager: 1 个定时器到期

[752] [+22011ms] [LOG] 2025-07-19T18:07:41.309Z
⏰ UnifiedTimerManager: 1 个定时器到期

[753] [+22011ms] [LOG] 2025-07-19T18:07:41.309Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[754] [+22011ms] [LOG] 2025-07-19T18:07:41.309Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[755] [+22011ms] [LOG] 2025-07-19T18:07:41.309Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[756] [+22011ms] [LOG] 2025-07-19T18:07:41.309Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[757] [+22011ms] [LOG] 2025-07-19T18:07:41.309Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:42

[758] [+22011ms] [LOG] 2025-07-19T18:07:41.309Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:42

[759] [+23104ms] [LOG] 2025-07-19T18:07:42.402Z
⏰ UnifiedTimerManager: 1 个定时器到期

[760] [+23104ms] [LOG] 2025-07-19T18:07:42.402Z
⏰ UnifiedTimerManager: 1 个定时器到期

[761] [+23104ms] [LOG] 2025-07-19T18:07:42.402Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[762] [+23104ms] [LOG] 2025-07-19T18:07:42.402Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[763] [+23104ms] [LOG] 2025-07-19T18:07:42.402Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[764] [+23104ms] [LOG] 2025-07-19T18:07:42.402Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[765] [+23104ms] [LOG] 2025-07-19T18:07:42.402Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:43

[766] [+23105ms] [LOG] 2025-07-19T18:07:42.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:43

[767] [+24104ms] [LOG] 2025-07-19T18:07:43.402Z
⏰ UnifiedTimerManager: 1 个定时器到期

[768] [+24104ms] [LOG] 2025-07-19T18:07:43.402Z
⏰ UnifiedTimerManager: 1 个定时器到期

[769] [+24104ms] [LOG] 2025-07-19T18:07:43.402Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[770] [+24104ms] [LOG] 2025-07-19T18:07:43.402Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[771] [+24104ms] [LOG] 2025-07-19T18:07:43.402Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[772] [+24104ms] [LOG] 2025-07-19T18:07:43.402Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[773] [+24105ms] [LOG] 2025-07-19T18:07:43.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:44

[774] [+24105ms] [LOG] 2025-07-19T18:07:43.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:44

[775] [+25201ms] [LOG] 2025-07-19T18:07:44.499Z
⏰ UnifiedTimerManager: 1 个定时器到期

[776] [+25201ms] [LOG] 2025-07-19T18:07:44.499Z
⏰ UnifiedTimerManager: 1 个定时器到期

[777] [+25201ms] [LOG] 2025-07-19T18:07:44.499Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[778] [+25201ms] [LOG] 2025-07-19T18:07:44.499Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[779] [+25201ms] [LOG] 2025-07-19T18:07:44.499Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[780] [+25201ms] [LOG] 2025-07-19T18:07:44.499Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[781] [+25201ms] [LOG] 2025-07-19T18:07:44.499Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:45

[782] [+25201ms] [LOG] 2025-07-19T18:07:44.499Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:45

[783] [+26208ms] [LOG] 2025-07-19T18:07:45.506Z
⏰ UnifiedTimerManager: 1 个定时器到期

[784] [+26208ms] [LOG] 2025-07-19T18:07:45.506Z
⏰ UnifiedTimerManager: 1 个定时器到期

[785] [+26208ms] [LOG] 2025-07-19T18:07:45.506Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 7ms

[786] [+26208ms] [LOG] 2025-07-19T18:07:45.506Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 7ms

[787] [+26208ms] [LOG] 2025-07-19T18:07:45.506Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[788] [+26208ms] [LOG] 2025-07-19T18:07:45.506Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[789] [+26208ms] [LOG] 2025-07-19T18:07:45.506Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:46

[790] [+26208ms] [LOG] 2025-07-19T18:07:45.506Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:46

[791] [+27309ms] [LOG] 2025-07-19T18:07:46.607Z
⏰ UnifiedTimerManager: 1 个定时器到期

[792] [+27309ms] [LOG] 2025-07-19T18:07:46.607Z
⏰ UnifiedTimerManager: 1 个定时器到期

[793] [+27309ms] [LOG] 2025-07-19T18:07:46.607Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 101ms

[794] [+27309ms] [LOG] 2025-07-19T18:07:46.607Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 101ms

[795] [+27309ms] [LOG] 2025-07-19T18:07:46.607Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[796] [+27309ms] [LOG] 2025-07-19T18:07:46.607Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[797] [+27309ms] [LOG] 2025-07-19T18:07:46.607Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:47

[798] [+27309ms] [LOG] 2025-07-19T18:07:46.607Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:47

[799] [+28408ms] [LOG] 2025-07-19T18:07:47.706Z
⏰ UnifiedTimerManager: 1 个定时器到期

[800] [+28408ms] [LOG] 2025-07-19T18:07:47.706Z
⏰ UnifiedTimerManager: 1 个定时器到期

[801] [+28408ms] [LOG] 2025-07-19T18:07:47.706Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[802] [+28408ms] [LOG] 2025-07-19T18:07:47.706Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[803] [+28408ms] [LOG] 2025-07-19T18:07:47.706Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[804] [+28408ms] [LOG] 2025-07-19T18:07:47.706Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[805] [+28409ms] [LOG] 2025-07-19T18:07:47.707Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:48

[806] [+28409ms] [LOG] 2025-07-19T18:07:47.707Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:48

[807] [+29507ms] [LOG] 2025-07-19T18:07:48.805Z
⏰ UnifiedTimerManager: 1 个定时器到期

[808] [+29507ms] [LOG] 2025-07-19T18:07:48.805Z
⏰ UnifiedTimerManager: 1 个定时器到期

[809] [+29507ms] [LOG] 2025-07-19T18:07:48.805Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[810] [+29507ms] [LOG] 2025-07-19T18:07:48.805Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[811] [+29507ms] [LOG] 2025-07-19T18:07:48.805Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[812] [+29507ms] [LOG] 2025-07-19T18:07:48.805Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[813] [+29508ms] [LOG] 2025-07-19T18:07:48.806Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:49

[814] [+29508ms] [LOG] 2025-07-19T18:07:48.806Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:49

[815] [+30507ms] [LOG] 2025-07-19T18:07:49.805Z
⏰ UnifiedTimerManager: 1 个定时器到期

[816] [+30507ms] [LOG] 2025-07-19T18:07:49.805Z
⏰ UnifiedTimerManager: 1 个定时器到期

[817] [+30507ms] [LOG] 2025-07-19T18:07:49.805Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[818] [+30507ms] [LOG] 2025-07-19T18:07:49.805Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[819] [+30507ms] [LOG] 2025-07-19T18:07:49.805Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[820] [+30507ms] [LOG] 2025-07-19T18:07:49.805Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[821] [+30507ms] [LOG] 2025-07-19T18:07:49.805Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:50

[822] [+30507ms] [LOG] 2025-07-19T18:07:49.805Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:50

[823] [+31507ms] [LOG] 2025-07-19T18:07:50.805Z
⏰ UnifiedTimerManager: 1 个定时器到期

[824] [+31507ms] [LOG] 2025-07-19T18:07:50.805Z
⏰ UnifiedTimerManager: 1 个定时器到期

[825] [+31507ms] [LOG] 2025-07-19T18:07:50.805Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[826] [+31507ms] [LOG] 2025-07-19T18:07:50.805Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[827] [+31507ms] [LOG] 2025-07-19T18:07:50.805Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[828] [+31507ms] [LOG] 2025-07-19T18:07:50.805Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[829] [+31507ms] [LOG] 2025-07-19T18:07:50.805Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:51

[830] [+31507ms] [LOG] 2025-07-19T18:07:50.805Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:51

[831] [+32507ms] [LOG] 2025-07-19T18:07:51.805Z
⏰ UnifiedTimerManager: 1 个定时器到期

[832] [+32507ms] [LOG] 2025-07-19T18:07:51.805Z
⏰ UnifiedTimerManager: 1 个定时器到期

[833] [+32507ms] [LOG] 2025-07-19T18:07:51.805Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[834] [+32507ms] [LOG] 2025-07-19T18:07:51.805Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[835] [+32507ms] [LOG] 2025-07-19T18:07:51.805Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[836] [+32507ms] [LOG] 2025-07-19T18:07:51.805Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[837] [+32507ms] [LOG] 2025-07-19T18:07:51.805Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:52

[838] [+32507ms] [LOG] 2025-07-19T18:07:51.805Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:52

[839] [+33606ms] [LOG] 2025-07-19T18:07:52.904Z
⏰ UnifiedTimerManager: 1 个定时器到期

[840] [+33606ms] [LOG] 2025-07-19T18:07:52.904Z
⏰ UnifiedTimerManager: 1 个定时器到期

[841] [+33606ms] [LOG] 2025-07-19T18:07:52.904Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[842] [+33606ms] [LOG] 2025-07-19T18:07:52.904Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[843] [+33606ms] [LOG] 2025-07-19T18:07:52.904Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[844] [+33606ms] [LOG] 2025-07-19T18:07:52.904Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[845] [+33606ms] [LOG] 2025-07-19T18:07:52.904Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:53

[846] [+33606ms] [LOG] 2025-07-19T18:07:52.904Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:53

[847] [+34605ms] [LOG] 2025-07-19T18:07:53.903Z
⏰ UnifiedTimerManager: 1 个定时器到期

[848] [+34605ms] [LOG] 2025-07-19T18:07:53.903Z
⏰ UnifiedTimerManager: 1 个定时器到期

[849] [+34605ms] [LOG] 2025-07-19T18:07:53.903Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[850] [+34605ms] [LOG] 2025-07-19T18:07:53.903Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[851] [+34606ms] [LOG] 2025-07-19T18:07:53.904Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[852] [+34606ms] [LOG] 2025-07-19T18:07:53.904Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[853] [+34606ms] [LOG] 2025-07-19T18:07:53.904Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:54

[854] [+34606ms] [LOG] 2025-07-19T18:07:53.904Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:54

[855] [+35704ms] [LOG] 2025-07-19T18:07:55.002Z
⏰ UnifiedTimerManager: 1 个定时器到期

[856] [+35704ms] [LOG] 2025-07-19T18:07:55.002Z
⏰ UnifiedTimerManager: 1 个定时器到期

[857] [+35704ms] [LOG] 2025-07-19T18:07:55.002Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[858] [+35704ms] [LOG] 2025-07-19T18:07:55.002Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[859] [+35704ms] [LOG] 2025-07-19T18:07:55.002Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[860] [+35704ms] [LOG] 2025-07-19T18:07:55.002Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[861] [+35704ms] [LOG] 2025-07-19T18:07:55.002Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:56

[862] [+35704ms] [LOG] 2025-07-19T18:07:55.002Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:56

[863] [+36704ms] [LOG] 2025-07-19T18:07:56.002Z
⏰ UnifiedTimerManager: 1 个定时器到期

[864] [+36704ms] [LOG] 2025-07-19T18:07:56.002Z
⏰ UnifiedTimerManager: 1 个定时器到期

[865] [+36705ms] [LOG] 2025-07-19T18:07:56.003Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[866] [+36705ms] [LOG] 2025-07-19T18:07:56.003Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[867] [+36705ms] [LOG] 2025-07-19T18:07:56.003Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[868] [+36705ms] [LOG] 2025-07-19T18:07:56.003Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[869] [+36705ms] [LOG] 2025-07-19T18:07:56.003Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:57

[870] [+36705ms] [LOG] 2025-07-19T18:07:56.003Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:57

[871] [+37803ms] [LOG] 2025-07-19T18:07:57.101Z
⏰ UnifiedTimerManager: 1 个定时器到期

[872] [+37803ms] [LOG] 2025-07-19T18:07:57.101Z
⏰ UnifiedTimerManager: 1 个定时器到期

[873] [+37803ms] [LOG] 2025-07-19T18:07:57.101Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[874] [+37803ms] [LOG] 2025-07-19T18:07:57.101Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[875] [+37803ms] [LOG] 2025-07-19T18:07:57.101Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[876] [+37803ms] [LOG] 2025-07-19T18:07:57.101Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[877] [+37803ms] [LOG] 2025-07-19T18:07:57.101Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:58

[878] [+37803ms] [LOG] 2025-07-19T18:07:57.101Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:58

[879] [+38803ms] [LOG] 2025-07-19T18:07:58.101Z
⏰ UnifiedTimerManager: 1 个定时器到期

[880] [+38803ms] [LOG] 2025-07-19T18:07:58.101Z
⏰ UnifiedTimerManager: 1 个定时器到期

[881] [+38803ms] [LOG] 2025-07-19T18:07:58.101Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[882] [+38803ms] [LOG] 2025-07-19T18:07:58.101Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[883] [+38804ms] [LOG] 2025-07-19T18:07:58.102Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[884] [+38804ms] [LOG] 2025-07-19T18:07:58.102Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[885] [+38804ms] [LOG] 2025-07-19T18:07:58.102Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:59

[886] [+38804ms] [LOG] 2025-07-19T18:07:58.102Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:07:59

[887] [+39903ms] [LOG] 2025-07-19T18:07:59.201Z
⏰ UnifiedTimerManager: 1 个定时器到期

[888] [+39903ms] [LOG] 2025-07-19T18:07:59.201Z
⏰ UnifiedTimerManager: 1 个定时器到期

[889] [+39903ms] [LOG] 2025-07-19T18:07:59.201Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[890] [+39903ms] [LOG] 2025-07-19T18:07:59.201Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[891] [+39904ms] [LOG] 2025-07-19T18:07:59.202Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[892] [+39904ms] [LOG] 2025-07-19T18:07:59.202Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[893] [+39904ms] [LOG] 2025-07-19T18:07:59.202Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:00

[894] [+39904ms] [LOG] 2025-07-19T18:07:59.202Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:00

[895] [+40902ms] [LOG] 2025-07-19T18:08:00.200Z
⏰ UnifiedTimerManager: 1 个定时器到期

[896] [+40903ms] [LOG] 2025-07-19T18:08:00.201Z
⏰ UnifiedTimerManager: 1 个定时器到期

[897] [+40903ms] [LOG] 2025-07-19T18:08:00.201Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[898] [+40903ms] [LOG] 2025-07-19T18:08:00.201Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[899] [+40903ms] [LOG] 2025-07-19T18:08:00.201Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[900] [+40903ms] [LOG] 2025-07-19T18:08:00.201Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[901] [+40903ms] [LOG] 2025-07-19T18:08:00.201Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:01

[902] [+40903ms] [LOG] 2025-07-19T18:08:00.201Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:01

[903] [+42001ms] [LOG] 2025-07-19T18:08:01.299Z
⏰ UnifiedTimerManager: 1 个定时器到期

[904] [+42001ms] [LOG] 2025-07-19T18:08:01.299Z
⏰ UnifiedTimerManager: 1 个定时器到期

[905] [+42002ms] [LOG] 2025-07-19T18:08:01.300Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[906] [+42002ms] [LOG] 2025-07-19T18:08:01.300Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[907] [+42002ms] [LOG] 2025-07-19T18:08:01.300Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[908] [+42002ms] [LOG] 2025-07-19T18:08:01.300Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[909] [+42002ms] [LOG] 2025-07-19T18:08:01.300Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:02

[910] [+42002ms] [LOG] 2025-07-19T18:08:01.300Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:02

[911] [+43002ms] [LOG] 2025-07-19T18:08:02.300Z
⏰ UnifiedTimerManager: 1 个定时器到期

[912] [+43002ms] [LOG] 2025-07-19T18:08:02.300Z
⏰ UnifiedTimerManager: 1 个定时器到期

[913] [+43002ms] [LOG] 2025-07-19T18:08:02.300Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[914] [+43002ms] [LOG] 2025-07-19T18:08:02.300Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[915] [+43002ms] [LOG] 2025-07-19T18:08:02.300Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[916] [+43002ms] [LOG] 2025-07-19T18:08:02.300Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[917] [+43002ms] [LOG] 2025-07-19T18:08:02.300Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:03

[918] [+43002ms] [LOG] 2025-07-19T18:08:02.300Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:03

[919] [+44006ms] [LOG] 2025-07-19T18:08:03.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[920] [+44006ms] [LOG] 2025-07-19T18:08:03.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[921] [+44006ms] [LOG] 2025-07-19T18:08:03.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 5ms

[922] [+44006ms] [LOG] 2025-07-19T18:08:03.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 5ms

[923] [+44006ms] [LOG] 2025-07-19T18:08:03.304Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[924] [+44006ms] [LOG] 2025-07-19T18:08:03.304Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[925] [+44006ms] [LOG] 2025-07-19T18:08:03.304Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:04

[926] [+44006ms] [LOG] 2025-07-19T18:08:03.304Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:04

[927] [+45008ms] [LOG] 2025-07-19T18:08:04.306Z
⏰ UnifiedTimerManager: 1 个定时器到期

[928] [+45008ms] [LOG] 2025-07-19T18:08:04.306Z
⏰ UnifiedTimerManager: 1 个定时器到期

[929] [+45008ms] [LOG] 2025-07-19T18:08:04.306Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[930] [+45008ms] [LOG] 2025-07-19T18:08:04.306Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[931] [+45008ms] [LOG] 2025-07-19T18:08:04.306Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[932] [+45008ms] [LOG] 2025-07-19T18:08:04.306Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[933] [+45008ms] [LOG] 2025-07-19T18:08:04.306Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:05

[934] [+45008ms] [LOG] 2025-07-19T18:08:04.306Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:05

[935] [+46012ms] [LOG] 2025-07-19T18:08:05.310Z
⏰ UnifiedTimerManager: 1 个定时器到期

[936] [+46012ms] [LOG] 2025-07-19T18:08:05.310Z
⏰ UnifiedTimerManager: 1 个定时器到期

[937] [+46012ms] [LOG] 2025-07-19T18:08:05.310Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[938] [+46012ms] [LOG] 2025-07-19T18:08:05.310Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[939] [+46012ms] [LOG] 2025-07-19T18:08:05.310Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[940] [+46012ms] [LOG] 2025-07-19T18:08:05.310Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[941] [+46013ms] [LOG] 2025-07-19T18:08:05.311Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:06

[942] [+46013ms] [LOG] 2025-07-19T18:08:05.311Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:06

[943] [+47112ms] [LOG] 2025-07-19T18:08:06.410Z
⏰ UnifiedTimerManager: 1 个定时器到期

[944] [+47112ms] [LOG] 2025-07-19T18:08:06.410Z
⏰ UnifiedTimerManager: 1 个定时器到期

[945] [+47112ms] [LOG] 2025-07-19T18:08:06.410Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[946] [+47112ms] [LOG] 2025-07-19T18:08:06.410Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[947] [+47112ms] [LOG] 2025-07-19T18:08:06.410Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[948] [+47112ms] [LOG] 2025-07-19T18:08:06.410Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[949] [+47112ms] [LOG] 2025-07-19T18:08:06.410Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:07

[950] [+47112ms] [LOG] 2025-07-19T18:08:06.410Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:07

[951] [+48115ms] [LOG] 2025-07-19T18:08:07.413Z
⏰ UnifiedTimerManager: 1 个定时器到期

[952] [+48115ms] [LOG] 2025-07-19T18:08:07.413Z
⏰ UnifiedTimerManager: 1 个定时器到期

[953] [+48115ms] [LOG] 2025-07-19T18:08:07.413Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[954] [+48115ms] [LOG] 2025-07-19T18:08:07.413Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[955] [+48115ms] [LOG] 2025-07-19T18:08:07.413Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[956] [+48115ms] [LOG] 2025-07-19T18:08:07.413Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[957] [+48115ms] [LOG] 2025-07-19T18:08:07.413Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:08

[958] [+48115ms] [LOG] 2025-07-19T18:08:07.413Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:08

[959] [+49114ms] [LOG] 2025-07-19T18:08:08.412Z
⏰ UnifiedTimerManager: 1 个定时器到期

[960] [+49114ms] [LOG] 2025-07-19T18:08:08.412Z
⏰ UnifiedTimerManager: 1 个定时器到期

[961] [+49114ms] [LOG] 2025-07-19T18:08:08.412Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[962] [+49114ms] [LOG] 2025-07-19T18:08:08.412Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[963] [+49114ms] [LOG] 2025-07-19T18:08:08.412Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[964] [+49114ms] [LOG] 2025-07-19T18:08:08.412Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[965] [+49114ms] [LOG] 2025-07-19T18:08:08.412Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:09

[966] [+49114ms] [LOG] 2025-07-19T18:08:08.412Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:09

[967] [+50206ms] [LOG] 2025-07-19T18:08:09.504Z
⏰ UnifiedTimerManager: 1 个定时器到期

[968] [+50206ms] [LOG] 2025-07-19T18:08:09.504Z
⏰ UnifiedTimerManager: 1 个定时器到期

[969] [+50206ms] [LOG] 2025-07-19T18:08:09.504Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[970] [+50207ms] [LOG] 2025-07-19T18:08:09.505Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[971] [+50207ms] [LOG] 2025-07-19T18:08:09.505Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[972] [+50207ms] [LOG] 2025-07-19T18:08:09.505Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[973] [+50207ms] [LOG] 2025-07-19T18:08:09.505Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:10

[974] [+50207ms] [LOG] 2025-07-19T18:08:09.505Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:10

[975] [+51314ms] [LOG] 2025-07-19T18:08:10.612Z
⏰ UnifiedTimerManager: 1 个定时器到期

[976] [+51314ms] [LOG] 2025-07-19T18:08:10.612Z
⏰ UnifiedTimerManager: 1 个定时器到期

[977] [+51315ms] [LOG] 2025-07-19T18:08:10.613Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 108ms

[978] [+51315ms] [LOG] 2025-07-19T18:08:10.613Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 108ms

[979] [+51315ms] [LOG] 2025-07-19T18:08:10.613Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[980] [+51315ms] [LOG] 2025-07-19T18:08:10.613Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[981] [+51315ms] [LOG] 2025-07-19T18:08:10.613Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:11

[982] [+51315ms] [LOG] 2025-07-19T18:08:10.613Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:11

[983] [+52408ms] [LOG] 2025-07-19T18:08:11.706Z
⏰ UnifiedTimerManager: 1 个定时器到期

[984] [+52408ms] [LOG] 2025-07-19T18:08:11.706Z
⏰ UnifiedTimerManager: 1 个定时器到期

[985] [+52408ms] [LOG] 2025-07-19T18:08:11.706Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[986] [+52408ms] [LOG] 2025-07-19T18:08:11.706Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[987] [+52408ms] [LOG] 2025-07-19T18:08:11.706Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[988] [+52408ms] [LOG] 2025-07-19T18:08:11.706Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[989] [+52408ms] [LOG] 2025-07-19T18:08:11.706Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:12

[990] [+52408ms] [LOG] 2025-07-19T18:08:11.706Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:12

[991] [+53413ms] [LOG] 2025-07-19T18:08:12.711Z
⏰ UnifiedTimerManager: 1 个定时器到期

[992] [+53413ms] [LOG] 2025-07-19T18:08:12.711Z
⏰ UnifiedTimerManager: 1 个定时器到期

[993] [+53413ms] [LOG] 2025-07-19T18:08:12.711Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 5ms

[994] [+53413ms] [LOG] 2025-07-19T18:08:12.711Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 5ms

[995] [+53413ms] [LOG] 2025-07-19T18:08:12.711Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[996] [+53413ms] [LOG] 2025-07-19T18:08:12.711Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[997] [+53413ms] [LOG] 2025-07-19T18:08:12.711Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:13

[998] [+53413ms] [LOG] 2025-07-19T18:08:12.711Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:13

[999] [+54413ms] [LOG] 2025-07-19T18:08:13.711Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1000] [+54413ms] [LOG] 2025-07-19T18:08:13.711Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1001] [+54413ms] [LOG] 2025-07-19T18:08:13.711Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1002] [+54413ms] [LOG] 2025-07-19T18:08:13.711Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1003] [+54413ms] [LOG] 2025-07-19T18:08:13.711Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1004] [+54413ms] [LOG] 2025-07-19T18:08:13.711Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1005] [+54414ms] [LOG] 2025-07-19T18:08:13.712Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:14

[1006] [+54414ms] [LOG] 2025-07-19T18:08:13.712Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:14

[1007] [+55507ms] [LOG] 2025-07-19T18:08:14.805Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1008] [+55507ms] [LOG] 2025-07-19T18:08:14.805Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1009] [+55507ms] [LOG] 2025-07-19T18:08:14.805Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[1010] [+55507ms] [LOG] 2025-07-19T18:08:14.805Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[1011] [+55508ms] [LOG] 2025-07-19T18:08:14.806Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1012] [+55508ms] [LOG] 2025-07-19T18:08:14.806Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1013] [+55508ms] [LOG] 2025-07-19T18:08:14.806Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:15

[1014] [+55508ms] [LOG] 2025-07-19T18:08:14.806Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:15

[1015] [+56611ms] [LOG] 2025-07-19T18:08:15.909Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1016] [+56611ms] [LOG] 2025-07-19T18:08:15.909Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1017] [+56611ms] [LOG] 2025-07-19T18:08:15.909Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 104ms

[1018] [+56612ms] [LOG] 2025-07-19T18:08:15.910Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 104ms

[1019] [+56612ms] [LOG] 2025-07-19T18:08:15.910Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1020] [+56612ms] [LOG] 2025-07-19T18:08:15.910Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1021] [+56612ms] [LOG] 2025-07-19T18:08:15.910Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:16

[1022] [+56612ms] [LOG] 2025-07-19T18:08:15.910Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:16

[1023] [+57611ms] [LOG] 2025-07-19T18:08:16.909Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1024] [+57611ms] [LOG] 2025-07-19T18:08:16.909Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1025] [+57611ms] [LOG] 2025-07-19T18:08:16.909Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1026] [+57611ms] [LOG] 2025-07-19T18:08:16.909Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1027] [+57611ms] [LOG] 2025-07-19T18:08:16.909Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1028] [+57611ms] [LOG] 2025-07-19T18:08:16.909Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1029] [+57611ms] [LOG] 2025-07-19T18:08:16.909Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:17

[1030] [+57611ms] [LOG] 2025-07-19T18:08:16.909Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:17

[1031] [+58705ms] [LOG] 2025-07-19T18:08:18.003Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1032] [+58705ms] [LOG] 2025-07-19T18:08:18.003Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1033] [+58706ms] [LOG] 2025-07-19T18:08:18.004Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[1034] [+58706ms] [LOG] 2025-07-19T18:08:18.004Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[1035] [+58706ms] [LOG] 2025-07-19T18:08:18.004Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1036] [+58706ms] [LOG] 2025-07-19T18:08:18.004Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1037] [+58706ms] [LOG] 2025-07-19T18:08:18.004Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:19

[1038] [+58706ms] [LOG] 2025-07-19T18:08:18.004Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:19

[1039] [+59706ms] [LOG] 2025-07-19T18:08:19.004Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1040] [+59706ms] [LOG] 2025-07-19T18:08:19.004Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1041] [+59706ms] [LOG] 2025-07-19T18:08:19.004Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1042] [+59706ms] [LOG] 2025-07-19T18:08:19.004Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1043] [+59706ms] [LOG] 2025-07-19T18:08:19.004Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1044] [+59706ms] [LOG] 2025-07-19T18:08:19.004Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1045] [+59706ms] [LOG] 2025-07-19T18:08:19.004Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:20

[1046] [+59706ms] [LOG] 2025-07-19T18:08:19.004Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:20

[1047] [+60706ms] [LOG] 2025-07-19T18:08:20.004Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1048] [+60706ms] [LOG] 2025-07-19T18:08:20.004Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1049] [+60707ms] [LOG] 2025-07-19T18:08:20.005Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1050] [+60707ms] [LOG] 2025-07-19T18:08:20.005Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1051] [+60707ms] [LOG] 2025-07-19T18:08:20.005Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1052] [+60707ms] [LOG] 2025-07-19T18:08:20.005Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1053] [+60707ms] [LOG] 2025-07-19T18:08:20.005Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:21

[1054] [+60707ms] [LOG] 2025-07-19T18:08:20.005Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:21

[1055] [+61091ms] [LOG] 2025-07-19T18:08:20.389Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 183508
}
  参数2: {
  "type": "ping",
  "timestamp": 183508
}

[1056] [+61091ms] [LOG] 2025-07-19T18:08:20.389Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 183508
}
  参数2: {
  "type": "ping",
  "timestamp": 183508
}

[1057] [+61091ms] [LOG] 2025-07-19T18:08:20.389Z
🏓 回复pong消息

[1058] [+61091ms] [LOG] 2025-07-19T18:08:20.389Z
🏓 回复pong消息

[1059] [+61710ms] [LOG] 2025-07-19T18:08:21.008Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1060] [+61710ms] [LOG] 2025-07-19T18:08:21.008Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1061] [+61710ms] [LOG] 2025-07-19T18:08:21.008Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[1062] [+61710ms] [LOG] 2025-07-19T18:08:21.008Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[1063] [+61711ms] [LOG] 2025-07-19T18:08:21.009Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1064] [+61711ms] [LOG] 2025-07-19T18:08:21.009Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1065] [+61711ms] [LOG] 2025-07-19T18:08:21.009Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:22

[1066] [+61711ms] [LOG] 2025-07-19T18:08:21.009Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:22

[1067] [+62802ms] [LOG] 2025-07-19T18:08:22.100Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1068] [+62802ms] [LOG] 2025-07-19T18:08:22.100Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1069] [+62802ms] [LOG] 2025-07-19T18:08:22.100Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[1070] [+62802ms] [LOG] 2025-07-19T18:08:22.100Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[1071] [+62802ms] [LOG] 2025-07-19T18:08:22.100Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1072] [+62802ms] [LOG] 2025-07-19T18:08:22.100Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1073] [+62802ms] [LOG] 2025-07-19T18:08:22.100Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:23

[1074] [+62802ms] [LOG] 2025-07-19T18:08:22.100Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:23

[1075] [+63803ms] [LOG] 2025-07-19T18:08:23.101Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1076] [+63803ms] [LOG] 2025-07-19T18:08:23.101Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1077] [+63803ms] [LOG] 2025-07-19T18:08:23.101Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1078] [+63803ms] [LOG] 2025-07-19T18:08:23.101Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1079] [+63803ms] [LOG] 2025-07-19T18:08:23.101Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1080] [+63803ms] [LOG] 2025-07-19T18:08:23.101Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1081] [+63803ms] [LOG] 2025-07-19T18:08:23.101Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:24

[1082] [+63803ms] [LOG] 2025-07-19T18:08:23.101Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:24

[1083] [+64803ms] [LOG] 2025-07-19T18:08:24.101Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1084] [+64803ms] [LOG] 2025-07-19T18:08:24.101Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1085] [+64803ms] [LOG] 2025-07-19T18:08:24.101Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1086] [+64803ms] [LOG] 2025-07-19T18:08:24.101Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1087] [+64803ms] [LOG] 2025-07-19T18:08:24.101Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1088] [+64803ms] [LOG] 2025-07-19T18:08:24.101Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1089] [+64803ms] [LOG] 2025-07-19T18:08:24.101Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:25

[1090] [+64803ms] [LOG] 2025-07-19T18:08:24.101Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:25

[1091] [+65807ms] [LOG] 2025-07-19T18:08:25.105Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1092] [+65807ms] [LOG] 2025-07-19T18:08:25.105Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1093] [+65807ms] [LOG] 2025-07-19T18:08:25.105Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[1094] [+65807ms] [LOG] 2025-07-19T18:08:25.105Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[1095] [+65807ms] [LOG] 2025-07-19T18:08:25.105Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1096] [+65807ms] [LOG] 2025-07-19T18:08:25.105Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1097] [+65807ms] [LOG] 2025-07-19T18:08:25.105Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:26

[1098] [+65808ms] [LOG] 2025-07-19T18:08:25.106Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:26

[1099] [+66807ms] [LOG] 2025-07-19T18:08:26.105Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1100] [+66808ms] [LOG] 2025-07-19T18:08:26.106Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1101] [+66808ms] [LOG] 2025-07-19T18:08:26.106Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1102] [+66808ms] [LOG] 2025-07-19T18:08:26.106Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1103] [+66808ms] [LOG] 2025-07-19T18:08:26.106Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1104] [+66808ms] [LOG] 2025-07-19T18:08:26.106Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1105] [+66808ms] [LOG] 2025-07-19T18:08:26.106Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:27

[1106] [+66808ms] [LOG] 2025-07-19T18:08:26.106Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:27

[1107] [+67907ms] [LOG] 2025-07-19T18:08:27.205Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1108] [+67907ms] [LOG] 2025-07-19T18:08:27.205Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1109] [+67907ms] [LOG] 2025-07-19T18:08:27.205Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1110] [+67907ms] [LOG] 2025-07-19T18:08:27.205Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1111] [+67907ms] [LOG] 2025-07-19T18:08:27.205Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1112] [+67907ms] [LOG] 2025-07-19T18:08:27.205Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1113] [+67907ms] [LOG] 2025-07-19T18:08:27.205Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:28

[1114] [+67907ms] [LOG] 2025-07-19T18:08:27.205Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:28

[1115] [+69006ms] [LOG] 2025-07-19T18:08:28.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1116] [+69006ms] [LOG] 2025-07-19T18:08:28.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1117] [+69006ms] [LOG] 2025-07-19T18:08:28.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[1118] [+69006ms] [LOG] 2025-07-19T18:08:28.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[1119] [+69006ms] [LOG] 2025-07-19T18:08:28.304Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1120] [+69006ms] [LOG] 2025-07-19T18:08:28.304Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1121] [+69006ms] [LOG] 2025-07-19T18:08:28.304Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:29

[1122] [+69006ms] [LOG] 2025-07-19T18:08:28.304Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:29

[1123] [+70106ms] [LOG] 2025-07-19T18:08:29.404Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1124] [+70106ms] [LOG] 2025-07-19T18:08:29.404Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1125] [+70106ms] [LOG] 2025-07-19T18:08:29.404Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1126] [+70106ms] [LOG] 2025-07-19T18:08:29.404Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1127] [+70106ms] [LOG] 2025-07-19T18:08:29.404Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1128] [+70106ms] [LOG] 2025-07-19T18:08:29.404Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1129] [+70107ms] [LOG] 2025-07-19T18:08:29.405Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:30

[1130] [+70107ms] [LOG] 2025-07-19T18:08:29.405Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:30

[1131] [+71105ms] [LOG] 2025-07-19T18:08:30.403Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1132] [+71105ms] [LOG] 2025-07-19T18:08:30.403Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1133] [+71105ms] [LOG] 2025-07-19T18:08:30.403Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1134] [+71105ms] [LOG] 2025-07-19T18:08:30.403Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1135] [+71105ms] [LOG] 2025-07-19T18:08:30.403Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1136] [+71105ms] [LOG] 2025-07-19T18:08:30.403Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1137] [+71105ms] [LOG] 2025-07-19T18:08:30.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:31

[1138] [+71105ms] [LOG] 2025-07-19T18:08:30.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:31

[1139] [+72105ms] [LOG] 2025-07-19T18:08:31.403Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1140] [+72105ms] [LOG] 2025-07-19T18:08:31.403Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1141] [+72105ms] [LOG] 2025-07-19T18:08:31.403Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1142] [+72105ms] [LOG] 2025-07-19T18:08:31.403Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1143] [+72105ms] [LOG] 2025-07-19T18:08:31.403Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1144] [+72105ms] [LOG] 2025-07-19T18:08:31.403Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1145] [+72105ms] [LOG] 2025-07-19T18:08:31.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:32

[1146] [+72105ms] [LOG] 2025-07-19T18:08:31.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:32

[1147] [+73205ms] [LOG] 2025-07-19T18:08:32.503Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1148] [+73205ms] [LOG] 2025-07-19T18:08:32.503Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1149] [+73205ms] [LOG] 2025-07-19T18:08:32.503Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1150] [+73205ms] [LOG] 2025-07-19T18:08:32.503Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1151] [+73205ms] [LOG] 2025-07-19T18:08:32.503Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1152] [+73205ms] [LOG] 2025-07-19T18:08:32.503Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1153] [+73205ms] [LOG] 2025-07-19T18:08:32.503Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:33

[1154] [+73205ms] [LOG] 2025-07-19T18:08:32.503Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:33

[1155] [+74204ms] [LOG] 2025-07-19T18:08:33.502Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1156] [+74204ms] [LOG] 2025-07-19T18:08:33.502Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1157] [+74204ms] [LOG] 2025-07-19T18:08:33.502Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1158] [+74204ms] [LOG] 2025-07-19T18:08:33.502Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1159] [+74205ms] [LOG] 2025-07-19T18:08:33.503Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1160] [+74205ms] [LOG] 2025-07-19T18:08:33.503Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1161] [+74205ms] [LOG] 2025-07-19T18:08:33.503Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:34

[1162] [+74205ms] [LOG] 2025-07-19T18:08:33.503Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:34

[1163] [+75304ms] [LOG] 2025-07-19T18:08:34.602Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1164] [+75304ms] [LOG] 2025-07-19T18:08:34.602Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1165] [+75304ms] [LOG] 2025-07-19T18:08:34.602Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[1166] [+75304ms] [LOG] 2025-07-19T18:08:34.602Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[1167] [+75304ms] [LOG] 2025-07-19T18:08:34.602Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1168] [+75304ms] [LOG] 2025-07-19T18:08:34.602Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1169] [+75304ms] [LOG] 2025-07-19T18:08:34.602Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:35

[1170] [+75304ms] [LOG] 2025-07-19T18:08:34.602Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:35

[1171] [+76403ms] [LOG] 2025-07-19T18:08:35.701Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1172] [+76403ms] [LOG] 2025-07-19T18:08:35.701Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1173] [+76404ms] [LOG] 2025-07-19T18:08:35.702Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1174] [+76404ms] [LOG] 2025-07-19T18:08:35.702Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1175] [+76404ms] [LOG] 2025-07-19T18:08:35.702Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1176] [+76404ms] [LOG] 2025-07-19T18:08:35.702Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1177] [+76404ms] [LOG] 2025-07-19T18:08:35.702Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:36

[1178] [+76404ms] [LOG] 2025-07-19T18:08:35.702Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:36

[1179] [+77501ms] [LOG] 2025-07-19T18:08:36.799Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1180] [+77501ms] [LOG] 2025-07-19T18:08:36.799Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1181] [+77501ms] [LOG] 2025-07-19T18:08:36.799Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[1182] [+77501ms] [LOG] 2025-07-19T18:08:36.799Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[1183] [+77501ms] [LOG] 2025-07-19T18:08:36.799Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1184] [+77501ms] [LOG] 2025-07-19T18:08:36.799Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1185] [+77501ms] [LOG] 2025-07-19T18:08:36.799Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:37

[1186] [+77501ms] [LOG] 2025-07-19T18:08:36.799Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:37

[1187] [+78501ms] [LOG] 2025-07-19T18:08:37.799Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1188] [+78501ms] [LOG] 2025-07-19T18:08:37.799Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1189] [+78501ms] [LOG] 2025-07-19T18:08:37.799Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1190] [+78501ms] [LOG] 2025-07-19T18:08:37.799Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1191] [+78502ms] [LOG] 2025-07-19T18:08:37.800Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1192] [+78502ms] [LOG] 2025-07-19T18:08:37.800Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1193] [+78502ms] [LOG] 2025-07-19T18:08:37.800Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:38

[1194] [+78502ms] [LOG] 2025-07-19T18:08:37.800Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:38

[1195] [+79501ms] [LOG] 2025-07-19T18:08:38.799Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1196] [+79501ms] [LOG] 2025-07-19T18:08:38.799Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1197] [+79501ms] [LOG] 2025-07-19T18:08:38.799Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1198] [+79501ms] [LOG] 2025-07-19T18:08:38.799Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1199] [+79501ms] [LOG] 2025-07-19T18:08:38.799Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1200] [+79501ms] [LOG] 2025-07-19T18:08:38.799Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1201] [+79501ms] [LOG] 2025-07-19T18:08:38.799Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:39

[1202] [+79501ms] [LOG] 2025-07-19T18:08:38.799Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:39

[1203] [+80502ms] [LOG] 2025-07-19T18:08:39.800Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1204] [+80502ms] [LOG] 2025-07-19T18:08:39.800Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1205] [+80502ms] [LOG] 2025-07-19T18:08:39.800Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1206] [+80502ms] [LOG] 2025-07-19T18:08:39.800Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1207] [+80502ms] [LOG] 2025-07-19T18:08:39.800Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1208] [+80502ms] [LOG] 2025-07-19T18:08:39.800Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1209] [+80502ms] [LOG] 2025-07-19T18:08:39.800Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:40

[1210] [+80502ms] [LOG] 2025-07-19T18:08:39.800Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:40

[1211] [+81514ms] [LOG] 2025-07-19T18:08:40.812Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1212] [+81514ms] [LOG] 2025-07-19T18:08:40.812Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1213] [+81514ms] [LOG] 2025-07-19T18:08:40.812Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 11ms

[1214] [+81514ms] [LOG] 2025-07-19T18:08:40.812Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 11ms

[1215] [+81514ms] [LOG] 2025-07-19T18:08:40.812Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1216] [+81514ms] [LOG] 2025-07-19T18:08:40.812Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1217] [+81514ms] [LOG] 2025-07-19T18:08:40.812Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:41

[1218] [+81514ms] [LOG] 2025-07-19T18:08:40.812Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:41

[1219] [+82606ms] [LOG] 2025-07-19T18:08:41.904Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1220] [+82606ms] [LOG] 2025-07-19T18:08:41.904Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1221] [+82606ms] [LOG] 2025-07-19T18:08:41.904Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[1222] [+82606ms] [LOG] 2025-07-19T18:08:41.904Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[1223] [+82606ms] [LOG] 2025-07-19T18:08:41.904Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1224] [+82606ms] [LOG] 2025-07-19T18:08:41.904Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1225] [+82606ms] [LOG] 2025-07-19T18:08:41.904Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:42

[1226] [+82606ms] [LOG] 2025-07-19T18:08:41.904Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:42

[1227] [+83710ms] [LOG] 2025-07-19T18:08:43.008Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1228] [+83710ms] [LOG] 2025-07-19T18:08:43.008Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1229] [+83710ms] [LOG] 2025-07-19T18:08:43.008Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 104ms

[1230] [+83710ms] [LOG] 2025-07-19T18:08:43.008Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 104ms

[1231] [+83710ms] [LOG] 2025-07-19T18:08:43.008Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1232] [+83710ms] [LOG] 2025-07-19T18:08:43.008Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1233] [+83710ms] [LOG] 2025-07-19T18:08:43.008Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:44

[1234] [+83710ms] [LOG] 2025-07-19T18:08:43.008Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:44

[1235] [+84802ms] [LOG] 2025-07-19T18:08:44.100Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1236] [+84802ms] [LOG] 2025-07-19T18:08:44.100Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1237] [+84802ms] [LOG] 2025-07-19T18:08:44.100Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[1238] [+84802ms] [LOG] 2025-07-19T18:08:44.100Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[1239] [+84802ms] [LOG] 2025-07-19T18:08:44.100Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1240] [+84802ms] [LOG] 2025-07-19T18:08:44.100Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1241] [+84802ms] [LOG] 2025-07-19T18:08:44.100Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:45

[1242] [+84802ms] [LOG] 2025-07-19T18:08:44.100Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:45

[1243] [+85815ms] [LOG] 2025-07-19T18:08:45.113Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1244] [+85815ms] [LOG] 2025-07-19T18:08:45.113Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1245] [+85815ms] [LOG] 2025-07-19T18:08:45.113Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 13ms

[1246] [+85815ms] [LOG] 2025-07-19T18:08:45.113Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 13ms

[1247] [+85815ms] [LOG] 2025-07-19T18:08:45.113Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1248] [+85815ms] [LOG] 2025-07-19T18:08:45.113Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1249] [+85815ms] [LOG] 2025-07-19T18:08:45.113Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:46

[1250] [+85815ms] [LOG] 2025-07-19T18:08:45.113Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:46

[1251] [+86904ms] [LOG] 2025-07-19T18:08:46.202Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1252] [+86905ms] [LOG] 2025-07-19T18:08:46.203Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1253] [+86905ms] [LOG] 2025-07-19T18:08:46.203Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 89ms

[1254] [+86905ms] [LOG] 2025-07-19T18:08:46.203Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 89ms

[1255] [+86905ms] [LOG] 2025-07-19T18:08:46.203Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1256] [+86905ms] [LOG] 2025-07-19T18:08:46.203Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1257] [+86905ms] [LOG] 2025-07-19T18:08:46.203Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:47

[1258] [+86905ms] [LOG] 2025-07-19T18:08:46.203Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:47

[1259] [+88012ms] [LOG] 2025-07-19T18:08:47.310Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1260] [+88012ms] [LOG] 2025-07-19T18:08:47.310Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1261] [+88012ms] [LOG] 2025-07-19T18:08:47.310Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 108ms

[1262] [+88012ms] [LOG] 2025-07-19T18:08:47.310Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 108ms

[1263] [+88012ms] [LOG] 2025-07-19T18:08:47.310Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1264] [+88012ms] [LOG] 2025-07-19T18:08:47.310Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1265] [+88012ms] [LOG] 2025-07-19T18:08:47.310Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:48

[1266] [+88012ms] [LOG] 2025-07-19T18:08:47.310Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:48

[1267] [+89104ms] [LOG] 2025-07-19T18:08:48.402Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1268] [+89104ms] [LOG] 2025-07-19T18:08:48.402Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1269] [+89104ms] [LOG] 2025-07-19T18:08:48.402Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[1270] [+89104ms] [LOG] 2025-07-19T18:08:48.402Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[1271] [+89104ms] [LOG] 2025-07-19T18:08:48.402Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1272] [+89104ms] [LOG] 2025-07-19T18:08:48.402Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1273] [+89104ms] [LOG] 2025-07-19T18:08:48.402Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:49

[1274] [+89104ms] [LOG] 2025-07-19T18:08:48.402Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:49

[1275] [+90212ms] [LOG] 2025-07-19T18:08:49.510Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1276] [+90212ms] [LOG] 2025-07-19T18:08:49.510Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1277] [+90212ms] [LOG] 2025-07-19T18:08:49.510Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 108ms

[1278] [+90212ms] [LOG] 2025-07-19T18:08:49.510Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 108ms

[1279] [+90212ms] [LOG] 2025-07-19T18:08:49.510Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1280] [+90212ms] [LOG] 2025-07-19T18:08:49.510Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1281] [+90213ms] [LOG] 2025-07-19T18:08:49.511Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:50

[1282] [+90213ms] [LOG] 2025-07-19T18:08:49.511Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:50

[1283] [+91304ms] [LOG] 2025-07-19T18:08:50.602Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1284] [+91304ms] [LOG] 2025-07-19T18:08:50.602Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1285] [+91304ms] [LOG] 2025-07-19T18:08:50.602Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[1286] [+91304ms] [LOG] 2025-07-19T18:08:50.602Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[1287] [+91304ms] [LOG] 2025-07-19T18:08:50.602Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1288] [+91304ms] [LOG] 2025-07-19T18:08:50.602Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1289] [+91304ms] [LOG] 2025-07-19T18:08:50.602Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:51

[1290] [+91304ms] [LOG] 2025-07-19T18:08:50.602Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:51

[1291] [+92411ms] [LOG] 2025-07-19T18:08:51.709Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1292] [+92411ms] [LOG] 2025-07-19T18:08:51.709Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1293] [+92411ms] [LOG] 2025-07-19T18:08:51.709Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 107ms

[1294] [+92411ms] [LOG] 2025-07-19T18:08:51.709Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 107ms

[1295] [+92411ms] [LOG] 2025-07-19T18:08:51.709Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1296] [+92411ms] [LOG] 2025-07-19T18:08:51.709Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1297] [+92411ms] [LOG] 2025-07-19T18:08:51.709Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:52

[1298] [+92411ms] [LOG] 2025-07-19T18:08:51.709Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:52

[1299] [+93502ms] [LOG] 2025-07-19T18:08:52.800Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1300] [+93502ms] [LOG] 2025-07-19T18:08:52.800Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1301] [+93502ms] [LOG] 2025-07-19T18:08:52.800Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[1302] [+93502ms] [LOG] 2025-07-19T18:08:52.800Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[1303] [+93502ms] [LOG] 2025-07-19T18:08:52.800Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1304] [+93502ms] [LOG] 2025-07-19T18:08:52.800Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1305] [+93502ms] [LOG] 2025-07-19T18:08:52.800Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:53

[1306] [+93502ms] [LOG] 2025-07-19T18:08:52.800Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:53

[1307] [+94511ms] [LOG] 2025-07-19T18:08:53.809Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1308] [+94511ms] [LOG] 2025-07-19T18:08:53.809Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1309] [+94511ms] [LOG] 2025-07-19T18:08:53.809Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 9ms

[1310] [+94511ms] [LOG] 2025-07-19T18:08:53.809Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 9ms

[1311] [+94511ms] [LOG] 2025-07-19T18:08:53.809Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1312] [+94511ms] [LOG] 2025-07-19T18:08:53.809Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1313] [+94511ms] [LOG] 2025-07-19T18:08:53.809Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:54

[1314] [+94511ms] [LOG] 2025-07-19T18:08:53.809Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:54

[1315] [+95605ms] [LOG] 2025-07-19T18:08:54.903Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1316] [+95605ms] [LOG] 2025-07-19T18:08:54.903Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1317] [+95605ms] [LOG] 2025-07-19T18:08:54.903Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[1318] [+95605ms] [LOG] 2025-07-19T18:08:54.903Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[1319] [+95605ms] [LOG] 2025-07-19T18:08:54.903Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1320] [+95605ms] [LOG] 2025-07-19T18:08:54.903Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1321] [+95605ms] [LOG] 2025-07-19T18:08:54.903Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:55

[1322] [+95605ms] [LOG] 2025-07-19T18:08:54.903Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:55

[1323] [+96710ms] [LOG] 2025-07-19T18:08:56.008Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1324] [+96710ms] [LOG] 2025-07-19T18:08:56.008Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1325] [+96710ms] [LOG] 2025-07-19T18:08:56.008Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[1326] [+96710ms] [LOG] 2025-07-19T18:08:56.008Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[1327] [+96710ms] [LOG] 2025-07-19T18:08:56.008Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1328] [+96710ms] [LOG] 2025-07-19T18:08:56.008Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1329] [+96710ms] [LOG] 2025-07-19T18:08:56.008Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:57

[1330] [+96710ms] [LOG] 2025-07-19T18:08:56.008Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:57

[1331] [+97806ms] [LOG] 2025-07-19T18:08:57.104Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1332] [+97806ms] [LOG] 2025-07-19T18:08:57.104Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1333] [+97806ms] [LOG] 2025-07-19T18:08:57.104Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 95ms

[1334] [+97806ms] [LOG] 2025-07-19T18:08:57.104Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 95ms

[1335] [+97806ms] [LOG] 2025-07-19T18:08:57.104Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1336] [+97806ms] [LOG] 2025-07-19T18:08:57.104Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1337] [+97806ms] [LOG] 2025-07-19T18:08:57.104Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:58

[1338] [+97806ms] [LOG] 2025-07-19T18:08:57.104Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:58

[1339] [+98909ms] [LOG] 2025-07-19T18:08:58.207Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1340] [+98909ms] [LOG] 2025-07-19T18:08:58.207Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1341] [+98909ms] [LOG] 2025-07-19T18:08:58.207Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 104ms

[1342] [+98909ms] [LOG] 2025-07-19T18:08:58.207Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 104ms

[1343] [+98909ms] [LOG] 2025-07-19T18:08:58.207Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1344] [+98909ms] [LOG] 2025-07-19T18:08:58.207Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1345] [+98909ms] [LOG] 2025-07-19T18:08:58.207Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:59

[1346] [+98909ms] [LOG] 2025-07-19T18:08:58.207Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:08:59

[1347] [+100002ms] [LOG] 2025-07-19T18:08:59.300Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1348] [+100002ms] [LOG] 2025-07-19T18:08:59.300Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1349] [+100002ms] [LOG] 2025-07-19T18:08:59.300Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[1350] [+100002ms] [LOG] 2025-07-19T18:08:59.300Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[1351] [+100002ms] [LOG] 2025-07-19T18:08:59.300Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1352] [+100002ms] [LOG] 2025-07-19T18:08:59.300Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1353] [+100002ms] [LOG] 2025-07-19T18:08:59.300Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:00

[1354] [+100002ms] [LOG] 2025-07-19T18:08:59.300Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:00

[1355] [+101008ms] [LOG] 2025-07-19T18:09:00.306Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1356] [+101008ms] [LOG] 2025-07-19T18:09:00.306Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1357] [+101008ms] [LOG] 2025-07-19T18:09:00.306Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 6ms

[1358] [+101008ms] [LOG] 2025-07-19T18:09:00.306Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 6ms

[1359] [+101008ms] [LOG] 2025-07-19T18:09:00.306Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1360] [+101008ms] [LOG] 2025-07-19T18:09:00.306Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1361] [+101008ms] [LOG] 2025-07-19T18:09:00.306Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:01

[1362] [+101008ms] [LOG] 2025-07-19T18:09:00.306Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:01

[1363] [+102008ms] [LOG] 2025-07-19T18:09:01.306Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1364] [+102008ms] [LOG] 2025-07-19T18:09:01.306Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1365] [+102008ms] [LOG] 2025-07-19T18:09:01.306Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1366] [+102008ms] [LOG] 2025-07-19T18:09:01.306Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1367] [+102008ms] [LOG] 2025-07-19T18:09:01.306Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1368] [+102008ms] [LOG] 2025-07-19T18:09:01.306Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1369] [+102008ms] [LOG] 2025-07-19T18:09:01.306Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:02

[1370] [+102008ms] [LOG] 2025-07-19T18:09:01.306Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:02

[1371] [+103107ms] [LOG] 2025-07-19T18:09:02.405Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1372] [+103107ms] [LOG] 2025-07-19T18:09:02.405Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1373] [+103107ms] [LOG] 2025-07-19T18:09:02.405Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1374] [+103107ms] [LOG] 2025-07-19T18:09:02.405Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1375] [+103107ms] [LOG] 2025-07-19T18:09:02.405Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1376] [+103107ms] [LOG] 2025-07-19T18:09:02.405Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1377] [+103108ms] [LOG] 2025-07-19T18:09:02.406Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:03

[1378] [+103108ms] [LOG] 2025-07-19T18:09:02.406Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:03

[1379] [+104107ms] [LOG] 2025-07-19T18:09:03.405Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1380] [+104107ms] [LOG] 2025-07-19T18:09:03.405Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1381] [+104107ms] [LOG] 2025-07-19T18:09:03.405Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1382] [+104107ms] [LOG] 2025-07-19T18:09:03.405Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1383] [+104107ms] [LOG] 2025-07-19T18:09:03.405Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1384] [+104107ms] [LOG] 2025-07-19T18:09:03.405Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1385] [+104107ms] [LOG] 2025-07-19T18:09:03.405Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:04

[1386] [+104107ms] [LOG] 2025-07-19T18:09:03.405Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:04

[1387] [+105206ms] [LOG] 2025-07-19T18:09:04.504Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1388] [+105206ms] [LOG] 2025-07-19T18:09:04.504Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1389] [+105206ms] [LOG] 2025-07-19T18:09:04.504Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1390] [+105206ms] [LOG] 2025-07-19T18:09:04.504Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1391] [+105207ms] [LOG] 2025-07-19T18:09:04.505Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1392] [+105207ms] [LOG] 2025-07-19T18:09:04.505Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1393] [+105207ms] [LOG] 2025-07-19T18:09:04.505Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:05

[1394] [+105207ms] [LOG] 2025-07-19T18:09:04.505Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:05

[1395] [+106206ms] [LOG] 2025-07-19T18:09:05.504Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1396] [+106206ms] [LOG] 2025-07-19T18:09:05.504Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1397] [+106206ms] [LOG] 2025-07-19T18:09:05.504Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1398] [+106206ms] [LOG] 2025-07-19T18:09:05.504Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1399] [+106206ms] [LOG] 2025-07-19T18:09:05.504Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1400] [+106206ms] [LOG] 2025-07-19T18:09:05.504Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1401] [+106206ms] [LOG] 2025-07-19T18:09:05.504Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:06

[1402] [+106206ms] [LOG] 2025-07-19T18:09:05.504Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:06

[1403] [+107305ms] [LOG] 2025-07-19T18:09:06.603Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1404] [+107305ms] [LOG] 2025-07-19T18:09:06.603Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1405] [+107305ms] [LOG] 2025-07-19T18:09:06.603Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1406] [+107305ms] [LOG] 2025-07-19T18:09:06.603Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1407] [+107305ms] [LOG] 2025-07-19T18:09:06.603Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1408] [+107305ms] [LOG] 2025-07-19T18:09:06.603Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1409] [+107305ms] [LOG] 2025-07-19T18:09:06.603Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:07

[1410] [+107305ms] [LOG] 2025-07-19T18:09:06.603Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:07

[1411] [+108305ms] [LOG] 2025-07-19T18:09:07.603Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1412] [+108305ms] [LOG] 2025-07-19T18:09:07.603Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1413] [+108305ms] [LOG] 2025-07-19T18:09:07.603Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1414] [+108305ms] [LOG] 2025-07-19T18:09:07.603Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1415] [+108305ms] [LOG] 2025-07-19T18:09:07.603Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1416] [+108305ms] [LOG] 2025-07-19T18:09:07.603Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1417] [+108305ms] [LOG] 2025-07-19T18:09:07.603Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:08

[1418] [+108305ms] [LOG] 2025-07-19T18:09:07.603Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:08

[1419] [+109404ms] [LOG] 2025-07-19T18:09:08.702Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1420] [+109404ms] [LOG] 2025-07-19T18:09:08.702Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1421] [+109405ms] [LOG] 2025-07-19T18:09:08.703Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1422] [+109405ms] [LOG] 2025-07-19T18:09:08.703Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1423] [+109405ms] [LOG] 2025-07-19T18:09:08.703Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1424] [+109405ms] [LOG] 2025-07-19T18:09:08.703Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1425] [+109405ms] [LOG] 2025-07-19T18:09:08.703Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:09

[1426] [+109405ms] [LOG] 2025-07-19T18:09:08.703Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:09

[1427] [+110404ms] [LOG] 2025-07-19T18:09:09.702Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1428] [+110404ms] [LOG] 2025-07-19T18:09:09.702Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1429] [+110404ms] [LOG] 2025-07-19T18:09:09.702Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1430] [+110404ms] [LOG] 2025-07-19T18:09:09.702Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1431] [+110404ms] [LOG] 2025-07-19T18:09:09.702Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1432] [+110404ms] [LOG] 2025-07-19T18:09:09.702Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1433] [+110404ms] [LOG] 2025-07-19T18:09:09.702Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:10

[1434] [+110404ms] [LOG] 2025-07-19T18:09:09.702Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:10

[1435] [+111503ms] [LOG] 2025-07-19T18:09:10.801Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1436] [+111504ms] [LOG] 2025-07-19T18:09:10.802Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1437] [+111504ms] [LOG] 2025-07-19T18:09:10.802Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1438] [+111504ms] [LOG] 2025-07-19T18:09:10.802Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1439] [+111504ms] [LOG] 2025-07-19T18:09:10.802Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1440] [+111504ms] [LOG] 2025-07-19T18:09:10.802Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1441] [+111504ms] [LOG] 2025-07-19T18:09:10.802Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:11

[1442] [+111504ms] [LOG] 2025-07-19T18:09:10.802Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:11

[1443] [+112503ms] [LOG] 2025-07-19T18:09:11.801Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1444] [+112503ms] [LOG] 2025-07-19T18:09:11.801Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1445] [+112503ms] [LOG] 2025-07-19T18:09:11.801Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1446] [+112503ms] [LOG] 2025-07-19T18:09:11.801Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1447] [+112503ms] [LOG] 2025-07-19T18:09:11.801Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1448] [+112503ms] [LOG] 2025-07-19T18:09:11.801Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1449] [+112503ms] [LOG] 2025-07-19T18:09:11.801Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:12

[1450] [+112503ms] [LOG] 2025-07-19T18:09:11.801Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:12

[1451] [+113602ms] [LOG] 2025-07-19T18:09:12.900Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1452] [+113603ms] [LOG] 2025-07-19T18:09:12.901Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1453] [+113603ms] [LOG] 2025-07-19T18:09:12.901Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1454] [+113603ms] [LOG] 2025-07-19T18:09:12.901Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1455] [+113603ms] [LOG] 2025-07-19T18:09:12.901Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1456] [+113603ms] [LOG] 2025-07-19T18:09:12.901Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1457] [+113603ms] [LOG] 2025-07-19T18:09:12.901Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:13

[1458] [+113603ms] [LOG] 2025-07-19T18:09:12.901Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:13

[1459] [+114602ms] [LOG] 2025-07-19T18:09:13.900Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1460] [+114602ms] [LOG] 2025-07-19T18:09:13.900Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1461] [+114602ms] [LOG] 2025-07-19T18:09:13.900Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1462] [+114602ms] [LOG] 2025-07-19T18:09:13.900Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1463] [+114602ms] [LOG] 2025-07-19T18:09:13.900Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1464] [+114602ms] [LOG] 2025-07-19T18:09:13.900Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1465] [+114602ms] [LOG] 2025-07-19T18:09:13.900Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:14

[1466] [+114602ms] [LOG] 2025-07-19T18:09:13.900Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:14

[1467] [+115602ms] [LOG] 2025-07-19T18:09:14.900Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1468] [+115602ms] [LOG] 2025-07-19T18:09:14.900Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1469] [+115602ms] [LOG] 2025-07-19T18:09:14.900Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1470] [+115602ms] [LOG] 2025-07-19T18:09:14.900Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1471] [+115602ms] [LOG] 2025-07-19T18:09:14.900Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1472] [+115602ms] [LOG] 2025-07-19T18:09:14.900Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1473] [+115602ms] [LOG] 2025-07-19T18:09:14.900Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:15

[1474] [+115602ms] [LOG] 2025-07-19T18:09:14.900Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:15

[1475] [+116701ms] [LOG] 2025-07-19T18:09:15.999Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1476] [+116701ms] [LOG] 2025-07-19T18:09:15.999Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1477] [+116701ms] [LOG] 2025-07-19T18:09:15.999Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1478] [+116701ms] [LOG] 2025-07-19T18:09:15.999Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1479] [+116701ms] [LOG] 2025-07-19T18:09:15.999Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1480] [+116701ms] [LOG] 2025-07-19T18:09:15.999Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1481] [+116701ms] [LOG] 2025-07-19T18:09:15.999Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:16

[1482] [+116701ms] [LOG] 2025-07-19T18:09:15.999Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:16

[1483] [+117714ms] [LOG] 2025-07-19T18:09:17.012Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1484] [+117714ms] [LOG] 2025-07-19T18:09:17.012Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1485] [+117715ms] [LOG] 2025-07-19T18:09:17.013Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 13ms

[1486] [+117715ms] [LOG] 2025-07-19T18:09:17.013Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 13ms

[1487] [+117715ms] [LOG] 2025-07-19T18:09:17.013Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1488] [+117715ms] [LOG] 2025-07-19T18:09:17.013Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1489] [+117715ms] [LOG] 2025-07-19T18:09:17.013Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:18

[1490] [+117715ms] [LOG] 2025-07-19T18:09:17.013Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:18

[1491] [+118813ms] [LOG] 2025-07-19T18:09:18.111Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1492] [+118813ms] [LOG] 2025-07-19T18:09:18.111Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1493] [+118813ms] [LOG] 2025-07-19T18:09:18.111Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1494] [+118813ms] [LOG] 2025-07-19T18:09:18.111Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1495] [+118813ms] [LOG] 2025-07-19T18:09:18.111Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1496] [+118813ms] [LOG] 2025-07-19T18:09:18.111Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1497] [+118813ms] [LOG] 2025-07-19T18:09:18.111Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:19

[1498] [+118813ms] [LOG] 2025-07-19T18:09:18.111Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:19

[1499] [+119814ms] [LOG] 2025-07-19T18:09:19.112Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1500] [+119814ms] [LOG] 2025-07-19T18:09:19.112Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1501] [+119814ms] [LOG] 2025-07-19T18:09:19.112Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1502] [+119814ms] [LOG] 2025-07-19T18:09:19.112Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1503] [+119814ms] [LOG] 2025-07-19T18:09:19.112Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1504] [+119814ms] [LOG] 2025-07-19T18:09:19.112Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1505] [+119814ms] [LOG] 2025-07-19T18:09:19.112Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:20

[1506] [+119814ms] [LOG] 2025-07-19T18:09:19.112Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:20

[1507] [+120905ms] [LOG] 2025-07-19T18:09:20.203Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1508] [+120905ms] [LOG] 2025-07-19T18:09:20.203Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1509] [+120905ms] [LOG] 2025-07-19T18:09:20.203Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[1510] [+120905ms] [LOG] 2025-07-19T18:09:20.203Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[1511] [+120905ms] [LOG] 2025-07-19T18:09:20.203Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1512] [+120905ms] [LOG] 2025-07-19T18:09:20.203Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1513] [+120905ms] [LOG] 2025-07-19T18:09:20.203Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:21

[1514] [+120905ms] [LOG] 2025-07-19T18:09:20.203Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:21

[1515] [+121083ms] [LOG] 2025-07-19T18:09:20.381Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 243509
}
  参数2: {
  "type": "ping",
  "timestamp": 243509
}

[1516] [+121083ms] [LOG] 2025-07-19T18:09:20.381Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 243509
}
  参数2: {
  "type": "ping",
  "timestamp": 243509
}

[1517] [+121083ms] [LOG] 2025-07-19T18:09:20.381Z
🏓 回复pong消息

[1518] [+121083ms] [LOG] 2025-07-19T18:09:20.381Z
🏓 回复pong消息

[1519] [+121906ms] [LOG] 2025-07-19T18:09:21.204Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1520] [+121906ms] [LOG] 2025-07-19T18:09:21.204Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1521] [+121906ms] [LOG] 2025-07-19T18:09:21.204Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1522] [+121906ms] [LOG] 2025-07-19T18:09:21.204Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1523] [+121906ms] [LOG] 2025-07-19T18:09:21.204Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1524] [+121906ms] [LOG] 2025-07-19T18:09:21.204Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1525] [+121906ms] [LOG] 2025-07-19T18:09:21.204Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:22

[1526] [+121906ms] [LOG] 2025-07-19T18:09:21.204Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:22

[1527] [+122915ms] [LOG] 2025-07-19T18:09:22.213Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1528] [+122915ms] [LOG] 2025-07-19T18:09:22.213Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1529] [+122916ms] [LOG] 2025-07-19T18:09:22.214Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 9ms

[1530] [+122916ms] [LOG] 2025-07-19T18:09:22.214Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 9ms

[1531] [+122916ms] [LOG] 2025-07-19T18:09:22.214Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1532] [+122916ms] [LOG] 2025-07-19T18:09:22.214Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1533] [+122916ms] [LOG] 2025-07-19T18:09:22.214Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:23

[1534] [+122916ms] [LOG] 2025-07-19T18:09:22.214Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:23

[1535] [+124006ms] [LOG] 2025-07-19T18:09:23.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1536] [+124006ms] [LOG] 2025-07-19T18:09:23.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1537] [+124006ms] [LOG] 2025-07-19T18:09:23.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[1538] [+124006ms] [LOG] 2025-07-19T18:09:23.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[1539] [+124006ms] [LOG] 2025-07-19T18:09:23.304Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1540] [+124006ms] [LOG] 2025-07-19T18:09:23.304Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1541] [+124006ms] [LOG] 2025-07-19T18:09:23.304Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:24

[1542] [+124006ms] [LOG] 2025-07-19T18:09:23.304Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:24

[1543] [+125009ms] [LOG] 2025-07-19T18:09:24.307Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1544] [+125010ms] [LOG] 2025-07-19T18:09:24.308Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1545] [+125010ms] [LOG] 2025-07-19T18:09:24.308Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[1546] [+125010ms] [LOG] 2025-07-19T18:09:24.308Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[1547] [+125010ms] [LOG] 2025-07-19T18:09:24.308Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1548] [+125010ms] [LOG] 2025-07-19T18:09:24.308Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1549] [+125010ms] [LOG] 2025-07-19T18:09:24.308Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:25

[1550] [+125010ms] [LOG] 2025-07-19T18:09:24.308Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:25

[1551] [+126114ms] [LOG] 2025-07-19T18:09:25.412Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1552] [+126114ms] [LOG] 2025-07-19T18:09:25.412Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1553] [+126114ms] [LOG] 2025-07-19T18:09:25.412Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[1554] [+126114ms] [LOG] 2025-07-19T18:09:25.412Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[1555] [+126115ms] [LOG] 2025-07-19T18:09:25.413Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1556] [+126115ms] [LOG] 2025-07-19T18:09:25.413Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1557] [+126115ms] [LOG] 2025-07-19T18:09:25.413Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:26

[1558] [+126115ms] [LOG] 2025-07-19T18:09:25.413Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:26

[1559] [+127211ms] [LOG] 2025-07-19T18:09:26.509Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1560] [+127211ms] [LOG] 2025-07-19T18:09:26.509Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1561] [+127211ms] [LOG] 2025-07-19T18:09:26.509Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[1562] [+127211ms] [LOG] 2025-07-19T18:09:26.509Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[1563] [+127211ms] [LOG] 2025-07-19T18:09:26.509Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1564] [+127211ms] [LOG] 2025-07-19T18:09:26.509Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1565] [+127211ms] [LOG] 2025-07-19T18:09:26.509Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:27

[1566] [+127211ms] [LOG] 2025-07-19T18:09:26.509Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:27

[1567] [+128304ms] [LOG] 2025-07-19T18:09:27.602Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1568] [+128305ms] [LOG] 2025-07-19T18:09:27.603Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1569] [+128305ms] [LOG] 2025-07-19T18:09:27.603Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[1570] [+128305ms] [LOG] 2025-07-19T18:09:27.603Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[1571] [+128305ms] [LOG] 2025-07-19T18:09:27.603Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1572] [+128305ms] [LOG] 2025-07-19T18:09:27.603Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1573] [+128305ms] [LOG] 2025-07-19T18:09:27.603Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:28

[1574] [+128305ms] [LOG] 2025-07-19T18:09:27.603Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:28

[1575] [+129412ms] [LOG] 2025-07-19T18:09:28.710Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1576] [+129412ms] [LOG] 2025-07-19T18:09:28.710Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1577] [+129413ms] [LOG] 2025-07-19T18:09:28.711Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 108ms

[1578] [+129413ms] [LOG] 2025-07-19T18:09:28.711Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 108ms

[1579] [+129413ms] [LOG] 2025-07-19T18:09:28.711Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1580] [+129413ms] [LOG] 2025-07-19T18:09:28.711Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1581] [+129414ms] [LOG] 2025-07-19T18:09:28.712Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:29

[1582] [+129414ms] [LOG] 2025-07-19T18:09:28.712Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:29

[1583] [+130412ms] [LOG] 2025-07-19T18:09:29.710Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1584] [+130412ms] [LOG] 2025-07-19T18:09:29.710Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1585] [+130412ms] [LOG] 2025-07-19T18:09:29.710Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1586] [+130412ms] [LOG] 2025-07-19T18:09:29.710Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1587] [+130412ms] [LOG] 2025-07-19T18:09:29.710Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1588] [+130412ms] [LOG] 2025-07-19T18:09:29.710Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1589] [+130412ms] [LOG] 2025-07-19T18:09:29.710Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:30

[1590] [+130412ms] [LOG] 2025-07-19T18:09:29.710Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:30

[1591] [+131510ms] [LOG] 2025-07-19T18:09:30.808Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1592] [+131510ms] [LOG] 2025-07-19T18:09:30.808Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1593] [+131510ms] [LOG] 2025-07-19T18:09:30.808Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[1594] [+131510ms] [LOG] 2025-07-19T18:09:30.808Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[1595] [+131511ms] [LOG] 2025-07-19T18:09:30.809Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1596] [+131511ms] [LOG] 2025-07-19T18:09:30.809Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1597] [+131511ms] [LOG] 2025-07-19T18:09:30.809Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:31

[1598] [+131511ms] [LOG] 2025-07-19T18:09:30.809Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:31

[1599] [+132511ms] [LOG] 2025-07-19T18:09:31.809Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1600] [+132511ms] [LOG] 2025-07-19T18:09:31.809Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1601] [+132511ms] [LOG] 2025-07-19T18:09:31.809Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1602] [+132511ms] [LOG] 2025-07-19T18:09:31.809Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1603] [+132511ms] [LOG] 2025-07-19T18:09:31.809Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1604] [+132511ms] [LOG] 2025-07-19T18:09:31.809Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1605] [+132511ms] [LOG] 2025-07-19T18:09:31.809Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:32

[1606] [+132511ms] [LOG] 2025-07-19T18:09:31.809Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:32

[1607] [+133602ms] [LOG] 2025-07-19T18:09:32.900Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1608] [+133602ms] [LOG] 2025-07-19T18:09:32.900Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1609] [+133602ms] [LOG] 2025-07-19T18:09:32.900Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[1610] [+133602ms] [LOG] 2025-07-19T18:09:32.900Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[1611] [+133602ms] [LOG] 2025-07-19T18:09:32.900Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1612] [+133602ms] [LOG] 2025-07-19T18:09:32.900Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1613] [+133602ms] [LOG] 2025-07-19T18:09:32.900Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:33

[1614] [+133602ms] [LOG] 2025-07-19T18:09:32.900Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:33

[1615] [+134610ms] [LOG] 2025-07-19T18:09:33.908Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1616] [+134610ms] [LOG] 2025-07-19T18:09:33.908Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1617] [+134610ms] [LOG] 2025-07-19T18:09:33.908Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 8ms

[1618] [+134610ms] [LOG] 2025-07-19T18:09:33.908Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 8ms

[1619] [+134610ms] [LOG] 2025-07-19T18:09:33.908Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1620] [+134610ms] [LOG] 2025-07-19T18:09:33.908Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1621] [+134610ms] [LOG] 2025-07-19T18:09:33.908Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:34

[1622] [+134610ms] [LOG] 2025-07-19T18:09:33.908Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:34

[1623] [+135610ms] [LOG] 2025-07-19T18:09:34.908Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1624] [+135610ms] [LOG] 2025-07-19T18:09:34.908Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1625] [+135610ms] [LOG] 2025-07-19T18:09:34.908Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1626] [+135610ms] [LOG] 2025-07-19T18:09:34.908Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1627] [+135610ms] [LOG] 2025-07-19T18:09:34.908Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1628] [+135610ms] [LOG] 2025-07-19T18:09:34.908Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1629] [+135610ms] [LOG] 2025-07-19T18:09:34.908Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:35

[1630] [+135610ms] [LOG] 2025-07-19T18:09:34.908Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:35

[1631] [+136709ms] [LOG] 2025-07-19T18:09:36.007Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1632] [+136709ms] [LOG] 2025-07-19T18:09:36.007Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1633] [+136709ms] [LOG] 2025-07-19T18:09:36.007Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1634] [+136709ms] [LOG] 2025-07-19T18:09:36.007Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1635] [+136709ms] [LOG] 2025-07-19T18:09:36.007Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1636] [+136709ms] [LOG] 2025-07-19T18:09:36.007Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1637] [+136709ms] [LOG] 2025-07-19T18:09:36.007Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:37

[1638] [+136709ms] [LOG] 2025-07-19T18:09:36.007Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:37

[1639] [+137709ms] [LOG] 2025-07-19T18:09:37.007Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1640] [+137709ms] [LOG] 2025-07-19T18:09:37.007Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1641] [+137709ms] [LOG] 2025-07-19T18:09:37.007Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1642] [+137709ms] [LOG] 2025-07-19T18:09:37.007Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1643] [+137709ms] [LOG] 2025-07-19T18:09:37.007Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1644] [+137709ms] [LOG] 2025-07-19T18:09:37.007Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1645] [+137710ms] [LOG] 2025-07-19T18:09:37.008Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:38

[1646] [+137710ms] [LOG] 2025-07-19T18:09:37.008Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:38

[1647] [+138807ms] [LOG] 2025-07-19T18:09:38.105Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1648] [+138807ms] [LOG] 2025-07-19T18:09:38.105Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1649] [+138807ms] [LOG] 2025-07-19T18:09:38.105Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[1650] [+138807ms] [LOG] 2025-07-19T18:09:38.105Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[1651] [+138807ms] [LOG] 2025-07-19T18:09:38.105Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1652] [+138807ms] [LOG] 2025-07-19T18:09:38.105Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1653] [+138808ms] [LOG] 2025-07-19T18:09:38.106Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:39

[1654] [+138808ms] [LOG] 2025-07-19T18:09:38.106Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:39

[1655] [+139808ms] [LOG] 2025-07-19T18:09:39.106Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1656] [+139808ms] [LOG] 2025-07-19T18:09:39.106Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1657] [+139808ms] [LOG] 2025-07-19T18:09:39.106Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1658] [+139808ms] [LOG] 2025-07-19T18:09:39.106Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1659] [+139808ms] [LOG] 2025-07-19T18:09:39.106Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1660] [+139808ms] [LOG] 2025-07-19T18:09:39.106Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1661] [+139808ms] [LOG] 2025-07-19T18:09:39.106Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:40

[1662] [+139808ms] [LOG] 2025-07-19T18:09:39.106Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:40

[1663] [+140907ms] [LOG] 2025-07-19T18:09:40.205Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1664] [+140908ms] [LOG] 2025-07-19T18:09:40.206Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1665] [+140908ms] [LOG] 2025-07-19T18:09:40.206Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1666] [+140908ms] [LOG] 2025-07-19T18:09:40.206Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1667] [+140908ms] [LOG] 2025-07-19T18:09:40.206Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1668] [+140908ms] [LOG] 2025-07-19T18:09:40.206Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1669] [+140908ms] [LOG] 2025-07-19T18:09:40.206Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:41

[1670] [+140908ms] [LOG] 2025-07-19T18:09:40.206Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:41

[1671] [+141907ms] [LOG] 2025-07-19T18:09:41.205Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1672] [+141907ms] [LOG] 2025-07-19T18:09:41.205Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1673] [+141907ms] [LOG] 2025-07-19T18:09:41.205Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1674] [+141907ms] [LOG] 2025-07-19T18:09:41.205Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1675] [+141907ms] [LOG] 2025-07-19T18:09:41.205Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1676] [+141907ms] [LOG] 2025-07-19T18:09:41.205Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1677] [+141907ms] [LOG] 2025-07-19T18:09:41.205Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:42

[1678] [+141907ms] [LOG] 2025-07-19T18:09:41.205Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:42

[1679] [+143006ms] [LOG] 2025-07-19T18:09:42.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1680] [+143006ms] [LOG] 2025-07-19T18:09:42.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1681] [+143006ms] [LOG] 2025-07-19T18:09:42.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1682] [+143006ms] [LOG] 2025-07-19T18:09:42.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1683] [+143007ms] [LOG] 2025-07-19T18:09:42.305Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1684] [+143007ms] [LOG] 2025-07-19T18:09:42.305Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1685] [+143007ms] [LOG] 2025-07-19T18:09:42.305Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:43

[1686] [+143007ms] [LOG] 2025-07-19T18:09:42.305Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:43

[1687] [+144006ms] [LOG] 2025-07-19T18:09:43.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1688] [+144007ms] [LOG] 2025-07-19T18:09:43.305Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1689] [+144007ms] [LOG] 2025-07-19T18:09:43.305Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1690] [+144007ms] [LOG] 2025-07-19T18:09:43.305Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1691] [+144007ms] [LOG] 2025-07-19T18:09:43.305Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1692] [+144007ms] [LOG] 2025-07-19T18:09:43.305Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1693] [+144008ms] [LOG] 2025-07-19T18:09:43.306Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:44

[1694] [+144008ms] [LOG] 2025-07-19T18:09:43.306Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:44

[1695] [+145006ms] [LOG] 2025-07-19T18:09:44.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1696] [+145006ms] [LOG] 2025-07-19T18:09:44.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1697] [+145007ms] [LOG] 2025-07-19T18:09:44.305Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1698] [+145007ms] [LOG] 2025-07-19T18:09:44.305Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1699] [+145007ms] [LOG] 2025-07-19T18:09:44.305Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1700] [+145007ms] [LOG] 2025-07-19T18:09:44.305Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1701] [+145007ms] [LOG] 2025-07-19T18:09:44.305Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:45

[1702] [+145007ms] [LOG] 2025-07-19T18:09:44.305Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:45

[1703] [+146104ms] [LOG] 2025-07-19T18:09:45.402Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1704] [+146104ms] [LOG] 2025-07-19T18:09:45.402Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1705] [+146104ms] [LOG] 2025-07-19T18:09:45.402Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[1706] [+146104ms] [LOG] 2025-07-19T18:09:45.402Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[1707] [+146104ms] [LOG] 2025-07-19T18:09:45.402Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1708] [+146105ms] [LOG] 2025-07-19T18:09:45.403Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1709] [+146105ms] [LOG] 2025-07-19T18:09:45.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:46

[1710] [+146105ms] [LOG] 2025-07-19T18:09:45.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:46

[1711] [+147104ms] [LOG] 2025-07-19T18:09:46.402Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1712] [+147105ms] [LOG] 2025-07-19T18:09:46.403Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1713] [+147105ms] [LOG] 2025-07-19T18:09:46.403Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1714] [+147105ms] [LOG] 2025-07-19T18:09:46.403Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1715] [+147105ms] [LOG] 2025-07-19T18:09:46.403Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1716] [+147105ms] [LOG] 2025-07-19T18:09:46.403Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1717] [+147105ms] [LOG] 2025-07-19T18:09:46.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:47

[1718] [+147105ms] [LOG] 2025-07-19T18:09:46.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:47

[1719] [+148104ms] [LOG] 2025-07-19T18:09:47.402Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1720] [+148104ms] [LOG] 2025-07-19T18:09:47.402Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1721] [+148104ms] [LOG] 2025-07-19T18:09:47.402Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1722] [+148104ms] [LOG] 2025-07-19T18:09:47.402Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1723] [+148104ms] [LOG] 2025-07-19T18:09:47.402Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1724] [+148104ms] [LOG] 2025-07-19T18:09:47.402Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1725] [+148104ms] [LOG] 2025-07-19T18:09:47.402Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:48

[1726] [+148104ms] [LOG] 2025-07-19T18:09:47.402Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:48

[1727] [+149203ms] [LOG] 2025-07-19T18:09:48.501Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1728] [+149203ms] [LOG] 2025-07-19T18:09:48.501Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1729] [+149203ms] [LOG] 2025-07-19T18:09:48.501Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1730] [+149203ms] [LOG] 2025-07-19T18:09:48.501Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1731] [+149203ms] [LOG] 2025-07-19T18:09:48.501Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1732] [+149203ms] [LOG] 2025-07-19T18:09:48.501Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1733] [+149203ms] [LOG] 2025-07-19T18:09:48.501Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:49

[1734] [+149203ms] [LOG] 2025-07-19T18:09:48.501Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:49

[1735] [+150204ms] [LOG] 2025-07-19T18:09:49.502Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1736] [+150204ms] [LOG] 2025-07-19T18:09:49.502Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1737] [+150204ms] [LOG] 2025-07-19T18:09:49.502Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1738] [+150204ms] [LOG] 2025-07-19T18:09:49.502Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1739] [+150204ms] [LOG] 2025-07-19T18:09:49.502Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1740] [+150204ms] [LOG] 2025-07-19T18:09:49.502Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1741] [+150205ms] [LOG] 2025-07-19T18:09:49.503Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:50

[1742] [+150205ms] [LOG] 2025-07-19T18:09:49.503Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:50

[1743] [+151303ms] [LOG] 2025-07-19T18:09:50.601Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1744] [+151303ms] [LOG] 2025-07-19T18:09:50.601Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1745] [+151303ms] [LOG] 2025-07-19T18:09:50.601Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[1746] [+151303ms] [LOG] 2025-07-19T18:09:50.601Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[1747] [+151303ms] [LOG] 2025-07-19T18:09:50.601Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1748] [+151303ms] [LOG] 2025-07-19T18:09:50.601Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1749] [+151303ms] [LOG] 2025-07-19T18:09:50.601Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:51

[1750] [+151303ms] [LOG] 2025-07-19T18:09:50.601Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:51

[1751] [+152302ms] [LOG] 2025-07-19T18:09:51.600Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1752] [+152303ms] [LOG] 2025-07-19T18:09:51.601Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1753] [+152303ms] [LOG] 2025-07-19T18:09:51.601Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1754] [+152303ms] [LOG] 2025-07-19T18:09:51.601Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1755] [+152303ms] [LOG] 2025-07-19T18:09:51.601Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1756] [+152303ms] [LOG] 2025-07-19T18:09:51.601Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1757] [+152303ms] [LOG] 2025-07-19T18:09:51.601Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:52

[1758] [+152303ms] [LOG] 2025-07-19T18:09:51.601Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:52

[1759] [+153302ms] [LOG] 2025-07-19T18:09:52.600Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1760] [+153302ms] [LOG] 2025-07-19T18:09:52.600Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1761] [+153302ms] [LOG] 2025-07-19T18:09:52.600Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1762] [+153302ms] [LOG] 2025-07-19T18:09:52.600Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1763] [+153302ms] [LOG] 2025-07-19T18:09:52.600Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1764] [+153302ms] [LOG] 2025-07-19T18:09:52.600Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1765] [+153302ms] [LOG] 2025-07-19T18:09:52.600Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:53

[1766] [+153302ms] [LOG] 2025-07-19T18:09:52.600Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:53

[1767] [+154401ms] [LOG] 2025-07-19T18:09:53.699Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1768] [+154401ms] [LOG] 2025-07-19T18:09:53.699Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1769] [+154401ms] [LOG] 2025-07-19T18:09:53.699Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1770] [+154401ms] [LOG] 2025-07-19T18:09:53.699Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1771] [+154401ms] [LOG] 2025-07-19T18:09:53.699Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1772] [+154401ms] [LOG] 2025-07-19T18:09:53.699Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1773] [+154401ms] [LOG] 2025-07-19T18:09:53.699Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:54

[1774] [+154401ms] [LOG] 2025-07-19T18:09:53.699Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:54

[1775] [+155402ms] [LOG] 2025-07-19T18:09:54.700Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1776] [+155402ms] [LOG] 2025-07-19T18:09:54.700Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1777] [+155402ms] [LOG] 2025-07-19T18:09:54.700Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1778] [+155402ms] [LOG] 2025-07-19T18:09:54.700Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1779] [+155402ms] [LOG] 2025-07-19T18:09:54.700Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1780] [+155402ms] [LOG] 2025-07-19T18:09:54.700Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1781] [+155402ms] [LOG] 2025-07-19T18:09:54.700Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:55

[1782] [+155402ms] [LOG] 2025-07-19T18:09:54.700Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:55

[1783] [+156401ms] [LOG] 2025-07-19T18:09:55.699Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1784] [+156401ms] [LOG] 2025-07-19T18:09:55.699Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1785] [+156402ms] [LOG] 2025-07-19T18:09:55.700Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1786] [+156402ms] [LOG] 2025-07-19T18:09:55.700Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1787] [+156402ms] [LOG] 2025-07-19T18:09:55.700Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1788] [+156402ms] [LOG] 2025-07-19T18:09:55.700Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1789] [+156402ms] [LOG] 2025-07-19T18:09:55.700Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:56

[1790] [+156402ms] [LOG] 2025-07-19T18:09:55.700Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:56

[1791] [+157407ms] [LOG] 2025-07-19T18:09:56.705Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1792] [+157407ms] [LOG] 2025-07-19T18:09:56.705Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1793] [+157407ms] [LOG] 2025-07-19T18:09:56.705Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 6ms

[1794] [+157407ms] [LOG] 2025-07-19T18:09:56.705Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 6ms

[1795] [+157408ms] [LOG] 2025-07-19T18:09:56.706Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1796] [+157408ms] [LOG] 2025-07-19T18:09:56.706Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1797] [+157408ms] [LOG] 2025-07-19T18:09:56.706Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:57

[1798] [+157408ms] [LOG] 2025-07-19T18:09:56.706Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:57

[1799] [+158512ms] [LOG] 2025-07-19T18:09:57.810Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1800] [+158513ms] [LOG] 2025-07-19T18:09:57.811Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1801] [+158513ms] [LOG] 2025-07-19T18:09:57.811Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[1802] [+158513ms] [LOG] 2025-07-19T18:09:57.811Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[1803] [+158513ms] [LOG] 2025-07-19T18:09:57.811Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1804] [+158513ms] [LOG] 2025-07-19T18:09:57.811Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1805] [+158513ms] [LOG] 2025-07-19T18:09:57.811Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:58

[1806] [+158513ms] [LOG] 2025-07-19T18:09:57.811Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:58

[1807] [+159513ms] [LOG] 2025-07-19T18:09:58.811Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1808] [+159513ms] [LOG] 2025-07-19T18:09:58.811Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1809] [+159513ms] [LOG] 2025-07-19T18:09:58.811Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1810] [+159513ms] [LOG] 2025-07-19T18:09:58.811Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1811] [+159513ms] [LOG] 2025-07-19T18:09:58.811Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1812] [+159514ms] [LOG] 2025-07-19T18:09:58.812Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1813] [+159514ms] [LOG] 2025-07-19T18:09:58.812Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:59

[1814] [+159514ms] [LOG] 2025-07-19T18:09:58.812Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:09:59

[1815] [+160606ms] [LOG] 2025-07-19T18:09:59.904Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1816] [+160606ms] [LOG] 2025-07-19T18:09:59.904Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1817] [+160606ms] [LOG] 2025-07-19T18:09:59.904Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[1818] [+160606ms] [LOG] 2025-07-19T18:09:59.904Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[1819] [+160606ms] [LOG] 2025-07-19T18:09:59.904Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1820] [+160606ms] [LOG] 2025-07-19T18:09:59.904Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1821] [+160606ms] [LOG] 2025-07-19T18:09:59.904Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:00

[1822] [+160606ms] [LOG] 2025-07-19T18:09:59.904Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:00

[1823] [+161713ms] [LOG] 2025-07-19T18:10:01.011Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1824] [+161713ms] [LOG] 2025-07-19T18:10:01.011Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1825] [+161713ms] [LOG] 2025-07-19T18:10:01.011Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 107ms

[1826] [+161713ms] [LOG] 2025-07-19T18:10:01.011Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 107ms

[1827] [+161713ms] [LOG] 2025-07-19T18:10:01.011Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1828] [+161713ms] [LOG] 2025-07-19T18:10:01.011Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1829] [+161713ms] [LOG] 2025-07-19T18:10:01.011Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:02

[1830] [+161713ms] [LOG] 2025-07-19T18:10:01.011Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:02

[1831] [+162806ms] [LOG] 2025-07-19T18:10:02.104Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1832] [+162806ms] [LOG] 2025-07-19T18:10:02.104Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1833] [+162806ms] [LOG] 2025-07-19T18:10:02.104Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[1834] [+162806ms] [LOG] 2025-07-19T18:10:02.104Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[1835] [+162806ms] [LOG] 2025-07-19T18:10:02.104Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1836] [+162806ms] [LOG] 2025-07-19T18:10:02.104Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1837] [+162806ms] [LOG] 2025-07-19T18:10:02.104Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:03

[1838] [+162806ms] [LOG] 2025-07-19T18:10:02.104Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:03

[1839] [+163913ms] [LOG] 2025-07-19T18:10:03.211Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1840] [+163913ms] [LOG] 2025-07-19T18:10:03.211Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1841] [+163913ms] [LOG] 2025-07-19T18:10:03.211Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 106ms

[1842] [+163913ms] [LOG] 2025-07-19T18:10:03.211Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 106ms

[1843] [+163913ms] [LOG] 2025-07-19T18:10:03.211Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1844] [+163913ms] [LOG] 2025-07-19T18:10:03.211Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1845] [+163913ms] [LOG] 2025-07-19T18:10:03.211Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:04

[1846] [+163913ms] [LOG] 2025-07-19T18:10:03.211Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:04

[1847] [+164913ms] [LOG] 2025-07-19T18:10:04.211Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1848] [+164913ms] [LOG] 2025-07-19T18:10:04.211Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1849] [+164913ms] [LOG] 2025-07-19T18:10:04.211Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1850] [+164913ms] [LOG] 2025-07-19T18:10:04.211Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1851] [+164913ms] [LOG] 2025-07-19T18:10:04.211Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1852] [+164913ms] [LOG] 2025-07-19T18:10:04.211Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1853] [+164914ms] [LOG] 2025-07-19T18:10:04.212Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:05

[1854] [+164914ms] [LOG] 2025-07-19T18:10:04.212Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:05

[1855] [+166003ms] [LOG] 2025-07-19T18:10:05.301Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1856] [+166004ms] [LOG] 2025-07-19T18:10:05.302Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1857] [+166004ms] [LOG] 2025-07-19T18:10:05.302Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 90ms

[1858] [+166004ms] [LOG] 2025-07-19T18:10:05.302Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 90ms

[1859] [+166004ms] [LOG] 2025-07-19T18:10:05.302Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1860] [+166004ms] [LOG] 2025-07-19T18:10:05.302Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1861] [+166004ms] [LOG] 2025-07-19T18:10:05.302Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:06

[1862] [+166004ms] [LOG] 2025-07-19T18:10:05.302Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:06

[1863] [+167013ms] [LOG] 2025-07-19T18:10:06.311Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1864] [+167013ms] [LOG] 2025-07-19T18:10:06.311Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1865] [+167013ms] [LOG] 2025-07-19T18:10:06.311Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 9ms

[1866] [+167013ms] [LOG] 2025-07-19T18:10:06.311Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 9ms

[1867] [+167013ms] [LOG] 2025-07-19T18:10:06.311Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1868] [+167013ms] [LOG] 2025-07-19T18:10:06.311Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1869] [+167013ms] [LOG] 2025-07-19T18:10:06.311Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:07

[1870] [+167013ms] [LOG] 2025-07-19T18:10:06.311Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:07

[1871] [+168012ms] [LOG] 2025-07-19T18:10:07.310Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1872] [+168012ms] [LOG] 2025-07-19T18:10:07.310Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1873] [+168012ms] [LOG] 2025-07-19T18:10:07.310Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1874] [+168012ms] [LOG] 2025-07-19T18:10:07.310Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1875] [+168012ms] [LOG] 2025-07-19T18:10:07.310Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1876] [+168013ms] [LOG] 2025-07-19T18:10:07.311Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1877] [+168013ms] [LOG] 2025-07-19T18:10:07.311Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:08

[1878] [+168013ms] [LOG] 2025-07-19T18:10:07.311Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:08

[1879] [+169110ms] [LOG] 2025-07-19T18:10:08.408Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1880] [+169110ms] [LOG] 2025-07-19T18:10:08.408Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1881] [+169110ms] [LOG] 2025-07-19T18:10:08.408Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[1882] [+169110ms] [LOG] 2025-07-19T18:10:08.408Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[1883] [+169110ms] [LOG] 2025-07-19T18:10:08.408Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1884] [+169110ms] [LOG] 2025-07-19T18:10:08.408Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1885] [+169110ms] [LOG] 2025-07-19T18:10:08.408Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:09

[1886] [+169110ms] [LOG] 2025-07-19T18:10:08.408Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:09

[1887] [+170202ms] [LOG] 2025-07-19T18:10:09.500Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1888] [+170202ms] [LOG] 2025-07-19T18:10:09.500Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1889] [+170202ms] [LOG] 2025-07-19T18:10:09.500Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[1890] [+170202ms] [LOG] 2025-07-19T18:10:09.500Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[1891] [+170202ms] [LOG] 2025-07-19T18:10:09.500Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1892] [+170202ms] [LOG] 2025-07-19T18:10:09.500Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1893] [+170202ms] [LOG] 2025-07-19T18:10:09.500Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:10

[1894] [+170202ms] [LOG] 2025-07-19T18:10:09.500Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:10

[1895] [+171205ms] [LOG] 2025-07-19T18:10:10.503Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1896] [+171205ms] [LOG] 2025-07-19T18:10:10.503Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1897] [+171206ms] [LOG] 2025-07-19T18:10:10.504Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[1898] [+171206ms] [LOG] 2025-07-19T18:10:10.504Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[1899] [+171206ms] [LOG] 2025-07-19T18:10:10.504Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1900] [+171206ms] [LOG] 2025-07-19T18:10:10.504Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1901] [+171206ms] [LOG] 2025-07-19T18:10:10.504Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:11

[1902] [+171206ms] [LOG] 2025-07-19T18:10:10.504Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:11

[1903] [+172310ms] [LOG] 2025-07-19T18:10:11.608Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1904] [+172310ms] [LOG] 2025-07-19T18:10:11.608Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1905] [+172310ms] [LOG] 2025-07-19T18:10:11.608Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[1906] [+172310ms] [LOG] 2025-07-19T18:10:11.608Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[1907] [+172310ms] [LOG] 2025-07-19T18:10:11.608Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1908] [+172310ms] [LOG] 2025-07-19T18:10:11.608Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1909] [+172310ms] [LOG] 2025-07-19T18:10:11.608Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:12

[1910] [+172310ms] [LOG] 2025-07-19T18:10:11.608Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:12

[1911] [+173310ms] [LOG] 2025-07-19T18:10:12.608Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1912] [+173310ms] [LOG] 2025-07-19T18:10:12.608Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1913] [+173310ms] [LOG] 2025-07-19T18:10:12.608Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1914] [+173310ms] [LOG] 2025-07-19T18:10:12.608Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1915] [+173310ms] [LOG] 2025-07-19T18:10:12.608Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1916] [+173310ms] [LOG] 2025-07-19T18:10:12.608Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1917] [+173310ms] [LOG] 2025-07-19T18:10:12.608Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:13

[1918] [+173310ms] [LOG] 2025-07-19T18:10:12.608Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:13

[1919] [+174404ms] [LOG] 2025-07-19T18:10:13.702Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1920] [+174404ms] [LOG] 2025-07-19T18:10:13.702Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1921] [+174404ms] [LOG] 2025-07-19T18:10:13.702Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[1922] [+174405ms] [LOG] 2025-07-19T18:10:13.703Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[1923] [+174405ms] [LOG] 2025-07-19T18:10:13.703Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1924] [+174405ms] [LOG] 2025-07-19T18:10:13.703Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1925] [+174405ms] [LOG] 2025-07-19T18:10:13.703Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:14

[1926] [+174405ms] [LOG] 2025-07-19T18:10:13.703Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:14

[1927] [+175404ms] [LOG] 2025-07-19T18:10:14.702Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1928] [+175404ms] [LOG] 2025-07-19T18:10:14.702Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1929] [+175404ms] [LOG] 2025-07-19T18:10:14.702Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1930] [+175404ms] [LOG] 2025-07-19T18:10:14.702Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1931] [+175404ms] [LOG] 2025-07-19T18:10:14.702Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1932] [+175404ms] [LOG] 2025-07-19T18:10:14.702Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1933] [+175404ms] [LOG] 2025-07-19T18:10:14.702Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:15

[1934] [+175404ms] [LOG] 2025-07-19T18:10:14.702Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:15

[1935] [+176509ms] [LOG] 2025-07-19T18:10:15.807Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1936] [+176509ms] [LOG] 2025-07-19T18:10:15.807Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1937] [+176510ms] [LOG] 2025-07-19T18:10:15.808Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[1938] [+176510ms] [LOG] 2025-07-19T18:10:15.808Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[1939] [+176510ms] [LOG] 2025-07-19T18:10:15.808Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1940] [+176510ms] [LOG] 2025-07-19T18:10:15.808Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1941] [+176510ms] [LOG] 2025-07-19T18:10:15.808Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:16

[1942] [+176510ms] [LOG] 2025-07-19T18:10:15.808Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:16

[1943] [+177605ms] [LOG] 2025-07-19T18:10:16.903Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1944] [+177606ms] [LOG] 2025-07-19T18:10:16.904Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1945] [+177606ms] [LOG] 2025-07-19T18:10:16.904Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 96ms

[1946] [+177606ms] [LOG] 2025-07-19T18:10:16.904Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 96ms

[1947] [+177606ms] [LOG] 2025-07-19T18:10:16.904Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1948] [+177606ms] [LOG] 2025-07-19T18:10:16.904Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1949] [+177606ms] [LOG] 2025-07-19T18:10:16.904Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:17

[1950] [+177606ms] [LOG] 2025-07-19T18:10:16.904Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:17

[1951] [+178607ms] [LOG] 2025-07-19T18:10:17.905Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1952] [+178607ms] [LOG] 2025-07-19T18:10:17.905Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1953] [+178608ms] [LOG] 2025-07-19T18:10:17.906Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[1954] [+178608ms] [LOG] 2025-07-19T18:10:17.906Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[1955] [+178608ms] [LOG] 2025-07-19T18:10:17.906Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1956] [+178608ms] [LOG] 2025-07-19T18:10:17.906Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1957] [+178608ms] [LOG] 2025-07-19T18:10:17.906Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:18

[1958] [+178608ms] [LOG] 2025-07-19T18:10:17.906Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:18

[1959] [+179607ms] [LOG] 2025-07-19T18:10:18.905Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1960] [+179607ms] [LOG] 2025-07-19T18:10:18.905Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1961] [+179607ms] [LOG] 2025-07-19T18:10:18.905Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1962] [+179607ms] [LOG] 2025-07-19T18:10:18.905Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1963] [+179607ms] [LOG] 2025-07-19T18:10:18.905Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1964] [+179607ms] [LOG] 2025-07-19T18:10:18.905Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1965] [+179607ms] [LOG] 2025-07-19T18:10:18.905Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:19

[1966] [+179607ms] [LOG] 2025-07-19T18:10:18.905Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:19

[1967] [+180705ms] [LOG] 2025-07-19T18:10:20.003Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1968] [+180705ms] [LOG] 2025-07-19T18:10:20.003Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1969] [+180705ms] [LOG] 2025-07-19T18:10:20.003Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[1970] [+180706ms] [LOG] 2025-07-19T18:10:20.004Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[1971] [+180706ms] [LOG] 2025-07-19T18:10:20.004Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1972] [+180706ms] [LOG] 2025-07-19T18:10:20.004Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1973] [+180706ms] [LOG] 2025-07-19T18:10:20.004Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:21

[1974] [+180706ms] [LOG] 2025-07-19T18:10:20.004Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:21

[1975] [+181082ms] [LOG] 2025-07-19T18:10:20.380Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 303510
}
  参数2: {
  "type": "ping",
  "timestamp": 303510
}

[1976] [+181082ms] [LOG] 2025-07-19T18:10:20.380Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 303510
}
  参数2: {
  "type": "ping",
  "timestamp": 303510
}

[1977] [+181082ms] [LOG] 2025-07-19T18:10:20.380Z
🏓 回复pong消息

[1978] [+181082ms] [LOG] 2025-07-19T18:10:20.380Z
🏓 回复pong消息

[1979] [+181706ms] [LOG] 2025-07-19T18:10:21.004Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1980] [+181706ms] [LOG] 2025-07-19T18:10:21.004Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1981] [+181706ms] [LOG] 2025-07-19T18:10:21.004Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1982] [+181706ms] [LOG] 2025-07-19T18:10:21.004Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[1983] [+181706ms] [LOG] 2025-07-19T18:10:21.004Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1984] [+181706ms] [LOG] 2025-07-19T18:10:21.004Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1985] [+181706ms] [LOG] 2025-07-19T18:10:21.004Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:22

[1986] [+181706ms] [LOG] 2025-07-19T18:10:21.004Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:22

[1987] [+182805ms] [LOG] 2025-07-19T18:10:22.103Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1988] [+182805ms] [LOG] 2025-07-19T18:10:22.103Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1989] [+182805ms] [LOG] 2025-07-19T18:10:22.103Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1990] [+182805ms] [LOG] 2025-07-19T18:10:22.103Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[1991] [+182806ms] [LOG] 2025-07-19T18:10:22.104Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1992] [+182806ms] [LOG] 2025-07-19T18:10:22.104Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[1993] [+182806ms] [LOG] 2025-07-19T18:10:22.104Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:23

[1994] [+182806ms] [LOG] 2025-07-19T18:10:22.104Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:23

[1995] [+183806ms] [LOG] 2025-07-19T18:10:23.104Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1996] [+183806ms] [LOG] 2025-07-19T18:10:23.104Z
⏰ UnifiedTimerManager: 1 个定时器到期

[1997] [+183806ms] [LOG] 2025-07-19T18:10:23.104Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1998] [+183806ms] [LOG] 2025-07-19T18:10:23.104Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[1999] [+183806ms] [LOG] 2025-07-19T18:10:23.104Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2000] [+183806ms] [LOG] 2025-07-19T18:10:23.104Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2001] [+183806ms] [LOG] 2025-07-19T18:10:23.104Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:24

[2002] [+183806ms] [LOG] 2025-07-19T18:10:23.104Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:24

[2003] [+184901ms] [LOG] 2025-07-19T18:10:24.199Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2004] [+184901ms] [LOG] 2025-07-19T18:10:24.199Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2005] [+184901ms] [LOG] 2025-07-19T18:10:24.199Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 96ms

[2006] [+184901ms] [LOG] 2025-07-19T18:10:24.199Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 96ms

[2007] [+184901ms] [LOG] 2025-07-19T18:10:24.199Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2008] [+184901ms] [LOG] 2025-07-19T18:10:24.199Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2009] [+184901ms] [LOG] 2025-07-19T18:10:24.199Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:25

[2010] [+184901ms] [LOG] 2025-07-19T18:10:24.199Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:25

[2011] [+185904ms] [LOG] 2025-07-19T18:10:25.202Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2012] [+185904ms] [LOG] 2025-07-19T18:10:25.202Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2013] [+185904ms] [LOG] 2025-07-19T18:10:25.202Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[2014] [+185904ms] [LOG] 2025-07-19T18:10:25.202Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[2015] [+185904ms] [LOG] 2025-07-19T18:10:25.202Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2016] [+185904ms] [LOG] 2025-07-19T18:10:25.202Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2017] [+185904ms] [LOG] 2025-07-19T18:10:25.202Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:26

[2018] [+185904ms] [LOG] 2025-07-19T18:10:25.202Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:26

[2019] [+186904ms] [LOG] 2025-07-19T18:10:26.202Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2020] [+186904ms] [LOG] 2025-07-19T18:10:26.202Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2021] [+186904ms] [LOG] 2025-07-19T18:10:26.202Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2022] [+186905ms] [LOG] 2025-07-19T18:10:26.203Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2023] [+186905ms] [LOG] 2025-07-19T18:10:26.203Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2024] [+186905ms] [LOG] 2025-07-19T18:10:26.203Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2025] [+186905ms] [LOG] 2025-07-19T18:10:26.203Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:27

[2026] [+186905ms] [LOG] 2025-07-19T18:10:26.203Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:27

[2027] [+188004ms] [LOG] 2025-07-19T18:10:27.302Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2028] [+188004ms] [LOG] 2025-07-19T18:10:27.302Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2029] [+188004ms] [LOG] 2025-07-19T18:10:27.302Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2030] [+188004ms] [LOG] 2025-07-19T18:10:27.302Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2031] [+188004ms] [LOG] 2025-07-19T18:10:27.302Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2032] [+188004ms] [LOG] 2025-07-19T18:10:27.302Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2033] [+188004ms] [LOG] 2025-07-19T18:10:27.302Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:28

[2034] [+188004ms] [LOG] 2025-07-19T18:10:27.302Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:28

[2035] [+189103ms] [LOG] 2025-07-19T18:10:28.401Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2036] [+189103ms] [LOG] 2025-07-19T18:10:28.401Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2037] [+189104ms] [LOG] 2025-07-19T18:10:28.402Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2038] [+189104ms] [LOG] 2025-07-19T18:10:28.402Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2039] [+189104ms] [LOG] 2025-07-19T18:10:28.402Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2040] [+189104ms] [LOG] 2025-07-19T18:10:28.402Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2041] [+189104ms] [LOG] 2025-07-19T18:10:28.402Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:29

[2042] [+189104ms] [LOG] 2025-07-19T18:10:28.402Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:29

[2043] [+190202ms] [LOG] 2025-07-19T18:10:29.500Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2044] [+190202ms] [LOG] 2025-07-19T18:10:29.500Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2045] [+190202ms] [LOG] 2025-07-19T18:10:29.500Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2046] [+190202ms] [LOG] 2025-07-19T18:10:29.500Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2047] [+190202ms] [LOG] 2025-07-19T18:10:29.500Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2048] [+190202ms] [LOG] 2025-07-19T18:10:29.500Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2049] [+190202ms] [LOG] 2025-07-19T18:10:29.500Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:30

[2050] [+190202ms] [LOG] 2025-07-19T18:10:29.500Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:30

[2051] [+191202ms] [LOG] 2025-07-19T18:10:30.500Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2052] [+191202ms] [LOG] 2025-07-19T18:10:30.500Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2053] [+191202ms] [LOG] 2025-07-19T18:10:30.500Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2054] [+191202ms] [LOG] 2025-07-19T18:10:30.500Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2055] [+191202ms] [LOG] 2025-07-19T18:10:30.500Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2056] [+191202ms] [LOG] 2025-07-19T18:10:30.500Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2057] [+191203ms] [LOG] 2025-07-19T18:10:30.501Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:31

[2058] [+191203ms] [LOG] 2025-07-19T18:10:30.501Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:31

[2059] [+192301ms] [LOG] 2025-07-19T18:10:31.599Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2060] [+192301ms] [LOG] 2025-07-19T18:10:31.599Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2061] [+192301ms] [LOG] 2025-07-19T18:10:31.599Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2062] [+192301ms] [LOG] 2025-07-19T18:10:31.599Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2063] [+192302ms] [LOG] 2025-07-19T18:10:31.600Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2064] [+192302ms] [LOG] 2025-07-19T18:10:31.600Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2065] [+192302ms] [LOG] 2025-07-19T18:10:31.600Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:32

[2066] [+192302ms] [LOG] 2025-07-19T18:10:31.600Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:32

[2067] [+193301ms] [LOG] 2025-07-19T18:10:32.599Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2068] [+193302ms] [LOG] 2025-07-19T18:10:32.600Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2069] [+193302ms] [LOG] 2025-07-19T18:10:32.600Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2070] [+193302ms] [LOG] 2025-07-19T18:10:32.600Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2071] [+193302ms] [LOG] 2025-07-19T18:10:32.600Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2072] [+193302ms] [LOG] 2025-07-19T18:10:32.600Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2073] [+193302ms] [LOG] 2025-07-19T18:10:32.600Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:33

[2074] [+193302ms] [LOG] 2025-07-19T18:10:32.600Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:33

[2075] [+194301ms] [LOG] 2025-07-19T18:10:33.599Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2076] [+194301ms] [LOG] 2025-07-19T18:10:33.599Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2077] [+194301ms] [LOG] 2025-07-19T18:10:33.599Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2078] [+194301ms] [LOG] 2025-07-19T18:10:33.599Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2079] [+194301ms] [LOG] 2025-07-19T18:10:33.599Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2080] [+194301ms] [LOG] 2025-07-19T18:10:33.599Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2081] [+194302ms] [LOG] 2025-07-19T18:10:33.600Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:34

[2082] [+194302ms] [LOG] 2025-07-19T18:10:33.600Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:34

[2083] [+195304ms] [LOG] 2025-07-19T18:10:34.602Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2084] [+195304ms] [LOG] 2025-07-19T18:10:34.602Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2085] [+195305ms] [LOG] 2025-07-19T18:10:34.603Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[2086] [+195305ms] [LOG] 2025-07-19T18:10:34.603Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[2087] [+195305ms] [LOG] 2025-07-19T18:10:34.603Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2088] [+195305ms] [LOG] 2025-07-19T18:10:34.603Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2089] [+195305ms] [LOG] 2025-07-19T18:10:34.603Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:35

[2090] [+195305ms] [LOG] 2025-07-19T18:10:34.603Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:35

[2091] [+196305ms] [LOG] 2025-07-19T18:10:35.603Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2092] [+196306ms] [LOG] 2025-07-19T18:10:35.604Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2093] [+196306ms] [LOG] 2025-07-19T18:10:35.604Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[2094] [+196306ms] [LOG] 2025-07-19T18:10:35.604Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[2095] [+196306ms] [LOG] 2025-07-19T18:10:35.604Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2096] [+196306ms] [LOG] 2025-07-19T18:10:35.604Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2097] [+196306ms] [LOG] 2025-07-19T18:10:35.604Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:36

[2098] [+196306ms] [LOG] 2025-07-19T18:10:35.604Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:36

[2099] [+197414ms] [LOG] 2025-07-19T18:10:36.712Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2100] [+197414ms] [LOG] 2025-07-19T18:10:36.712Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2101] [+197414ms] [LOG] 2025-07-19T18:10:36.712Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 109ms

[2102] [+197414ms] [LOG] 2025-07-19T18:10:36.712Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 109ms

[2103] [+197414ms] [LOG] 2025-07-19T18:10:36.712Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2104] [+197414ms] [LOG] 2025-07-19T18:10:36.712Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2105] [+197414ms] [LOG] 2025-07-19T18:10:36.712Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:37

[2106] [+197414ms] [LOG] 2025-07-19T18:10:36.712Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:37

[2107] [+198506ms] [LOG] 2025-07-19T18:10:37.804Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2108] [+198506ms] [LOG] 2025-07-19T18:10:37.804Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2109] [+198506ms] [LOG] 2025-07-19T18:10:37.804Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[2110] [+198506ms] [LOG] 2025-07-19T18:10:37.804Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[2111] [+198506ms] [LOG] 2025-07-19T18:10:37.804Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2112] [+198506ms] [LOG] 2025-07-19T18:10:37.804Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2113] [+198506ms] [LOG] 2025-07-19T18:10:37.804Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:38

[2114] [+198506ms] [LOG] 2025-07-19T18:10:37.804Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:38

[2115] [+199614ms] [LOG] 2025-07-19T18:10:38.912Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2116] [+199614ms] [LOG] 2025-07-19T18:10:38.912Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2117] [+199614ms] [LOG] 2025-07-19T18:10:38.912Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 107ms

[2118] [+199614ms] [LOG] 2025-07-19T18:10:38.912Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 107ms

[2119] [+199614ms] [LOG] 2025-07-19T18:10:38.912Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2120] [+199614ms] [LOG] 2025-07-19T18:10:38.912Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2121] [+199614ms] [LOG] 2025-07-19T18:10:38.912Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:39

[2122] [+199614ms] [LOG] 2025-07-19T18:10:38.912Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:39

[2123] [+200715ms] [LOG] 2025-07-19T18:10:40.013Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2124] [+200715ms] [LOG] 2025-07-19T18:10:40.013Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2125] [+200715ms] [LOG] 2025-07-19T18:10:40.013Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 101ms

[2126] [+200716ms] [LOG] 2025-07-19T18:10:40.014Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 101ms

[2127] [+200716ms] [LOG] 2025-07-19T18:10:40.014Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2128] [+200716ms] [LOG] 2025-07-19T18:10:40.014Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2129] [+200716ms] [LOG] 2025-07-19T18:10:40.014Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:41

[2130] [+200716ms] [LOG] 2025-07-19T18:10:40.014Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:41

[2131] [+201714ms] [LOG] 2025-07-19T18:10:41.012Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2132] [+201715ms] [LOG] 2025-07-19T18:10:41.013Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2133] [+201715ms] [LOG] 2025-07-19T18:10:41.013Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2134] [+201715ms] [LOG] 2025-07-19T18:10:41.013Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2135] [+201715ms] [LOG] 2025-07-19T18:10:41.013Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2136] [+201715ms] [LOG] 2025-07-19T18:10:41.013Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2137] [+201715ms] [LOG] 2025-07-19T18:10:41.013Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:42

[2138] [+201715ms] [LOG] 2025-07-19T18:10:41.013Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:42

[2139] [+202813ms] [LOG] 2025-07-19T18:10:42.111Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2140] [+202814ms] [LOG] 2025-07-19T18:10:42.112Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2141] [+202814ms] [LOG] 2025-07-19T18:10:42.112Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2142] [+202814ms] [LOG] 2025-07-19T18:10:42.112Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2143] [+202814ms] [LOG] 2025-07-19T18:10:42.112Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2144] [+202814ms] [LOG] 2025-07-19T18:10:42.112Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2145] [+202814ms] [LOG] 2025-07-19T18:10:42.112Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:43

[2146] [+202814ms] [LOG] 2025-07-19T18:10:42.112Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:43

[2147] [+203813ms] [LOG] 2025-07-19T18:10:43.111Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2148] [+203814ms] [LOG] 2025-07-19T18:10:43.112Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2149] [+203814ms] [LOG] 2025-07-19T18:10:43.112Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2150] [+203814ms] [LOG] 2025-07-19T18:10:43.112Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2151] [+203814ms] [LOG] 2025-07-19T18:10:43.112Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2152] [+203814ms] [LOG] 2025-07-19T18:10:43.112Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2153] [+203814ms] [LOG] 2025-07-19T18:10:43.112Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:44

[2154] [+203814ms] [LOG] 2025-07-19T18:10:43.112Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:44

[2155] [+204813ms] [LOG] 2025-07-19T18:10:44.111Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2156] [+204813ms] [LOG] 2025-07-19T18:10:44.111Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2157] [+204813ms] [LOG] 2025-07-19T18:10:44.111Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2158] [+204813ms] [LOG] 2025-07-19T18:10:44.111Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2159] [+204814ms] [LOG] 2025-07-19T18:10:44.112Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2160] [+204814ms] [LOG] 2025-07-19T18:10:44.112Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2161] [+204814ms] [LOG] 2025-07-19T18:10:44.112Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:45

[2162] [+204814ms] [LOG] 2025-07-19T18:10:44.112Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:45

[2163] [+205910ms] [LOG] 2025-07-19T18:10:45.208Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2164] [+205910ms] [LOG] 2025-07-19T18:10:45.208Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2165] [+205910ms] [LOG] 2025-07-19T18:10:45.208Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[2166] [+205910ms] [LOG] 2025-07-19T18:10:45.208Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[2167] [+205911ms] [LOG] 2025-07-19T18:10:45.209Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2168] [+205911ms] [LOG] 2025-07-19T18:10:45.209Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2169] [+205911ms] [LOG] 2025-07-19T18:10:45.209Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:46

[2170] [+205911ms] [LOG] 2025-07-19T18:10:45.209Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:46

[2171] [+207005ms] [LOG] 2025-07-19T18:10:46.303Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2172] [+207005ms] [LOG] 2025-07-19T18:10:46.303Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2173] [+207005ms] [LOG] 2025-07-19T18:10:46.303Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[2174] [+207005ms] [LOG] 2025-07-19T18:10:46.303Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 94ms

[2175] [+207005ms] [LOG] 2025-07-19T18:10:46.303Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2176] [+207005ms] [LOG] 2025-07-19T18:10:46.303Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2177] [+207005ms] [LOG] 2025-07-19T18:10:46.303Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:47

[2178] [+207005ms] [LOG] 2025-07-19T18:10:46.303Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:47

[2179] [+208004ms] [LOG] 2025-07-19T18:10:47.302Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2180] [+208004ms] [LOG] 2025-07-19T18:10:47.302Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2181] [+208004ms] [LOG] 2025-07-19T18:10:47.302Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2182] [+208004ms] [LOG] 2025-07-19T18:10:47.302Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2183] [+208005ms] [LOG] 2025-07-19T18:10:47.303Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2184] [+208005ms] [LOG] 2025-07-19T18:10:47.303Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2185] [+208005ms] [LOG] 2025-07-19T18:10:47.303Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:48

[2186] [+208005ms] [LOG] 2025-07-19T18:10:47.303Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:48

[2187] [+209111ms] [LOG] 2025-07-19T18:10:48.409Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2188] [+209111ms] [LOG] 2025-07-19T18:10:48.409Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2189] [+209111ms] [LOG] 2025-07-19T18:10:48.409Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 106ms

[2190] [+209111ms] [LOG] 2025-07-19T18:10:48.409Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 106ms

[2191] [+209111ms] [LOG] 2025-07-19T18:10:48.409Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2192] [+209111ms] [LOG] 2025-07-19T18:10:48.409Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2193] [+209111ms] [LOG] 2025-07-19T18:10:48.409Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:49

[2194] [+209111ms] [LOG] 2025-07-19T18:10:48.409Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:49

[2195] [+210110ms] [LOG] 2025-07-19T18:10:49.408Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2196] [+210110ms] [LOG] 2025-07-19T18:10:49.408Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2197] [+210110ms] [LOG] 2025-07-19T18:10:49.408Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2198] [+210110ms] [LOG] 2025-07-19T18:10:49.408Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2199] [+210110ms] [LOG] 2025-07-19T18:10:49.408Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2200] [+210110ms] [LOG] 2025-07-19T18:10:49.408Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2201] [+210110ms] [LOG] 2025-07-19T18:10:49.408Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:50

[2202] [+210110ms] [LOG] 2025-07-19T18:10:49.408Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:50

[2203] [+211207ms] [LOG] 2025-07-19T18:10:50.505Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2204] [+211207ms] [LOG] 2025-07-19T18:10:50.505Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2205] [+211207ms] [LOG] 2025-07-19T18:10:50.505Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[2206] [+211207ms] [LOG] 2025-07-19T18:10:50.505Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[2207] [+211207ms] [LOG] 2025-07-19T18:10:50.505Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2208] [+211207ms] [LOG] 2025-07-19T18:10:50.505Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2209] [+211207ms] [LOG] 2025-07-19T18:10:50.505Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:51

[2210] [+211207ms] [LOG] 2025-07-19T18:10:50.505Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:51

[2211] [+212209ms] [LOG] 2025-07-19T18:10:51.507Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2212] [+212210ms] [LOG] 2025-07-19T18:10:51.508Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2213] [+212210ms] [LOG] 2025-07-19T18:10:51.508Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2214] [+212210ms] [LOG] 2025-07-19T18:10:51.508Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2215] [+212210ms] [LOG] 2025-07-19T18:10:51.508Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2216] [+212210ms] [LOG] 2025-07-19T18:10:51.508Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2217] [+212210ms] [LOG] 2025-07-19T18:10:51.508Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:52

[2218] [+212210ms] [LOG] 2025-07-19T18:10:51.508Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:52

[2219] [+213209ms] [LOG] 2025-07-19T18:10:52.507Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2220] [+213209ms] [LOG] 2025-07-19T18:10:52.507Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2221] [+213209ms] [LOG] 2025-07-19T18:10:52.507Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2222] [+213209ms] [LOG] 2025-07-19T18:10:52.507Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2223] [+213209ms] [LOG] 2025-07-19T18:10:52.507Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2224] [+213209ms] [LOG] 2025-07-19T18:10:52.507Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2225] [+213209ms] [LOG] 2025-07-19T18:10:52.507Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:53

[2226] [+213209ms] [LOG] 2025-07-19T18:10:52.507Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:53

[2227] [+214209ms] [LOG] 2025-07-19T18:10:53.507Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2228] [+214209ms] [LOG] 2025-07-19T18:10:53.507Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2229] [+214209ms] [LOG] 2025-07-19T18:10:53.507Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2230] [+214209ms] [LOG] 2025-07-19T18:10:53.507Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2231] [+214209ms] [LOG] 2025-07-19T18:10:53.507Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2232] [+214209ms] [LOG] 2025-07-19T18:10:53.507Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2233] [+214210ms] [LOG] 2025-07-19T18:10:53.508Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:54

[2234] [+214210ms] [LOG] 2025-07-19T18:10:53.508Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:54

[2235] [+215305ms] [LOG] 2025-07-19T18:10:54.603Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2236] [+215306ms] [LOG] 2025-07-19T18:10:54.604Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2237] [+215306ms] [LOG] 2025-07-19T18:10:54.604Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 96ms

[2238] [+215306ms] [LOG] 2025-07-19T18:10:54.604Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 96ms

[2239] [+215306ms] [LOG] 2025-07-19T18:10:54.604Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2240] [+215306ms] [LOG] 2025-07-19T18:10:54.604Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2241] [+215306ms] [LOG] 2025-07-19T18:10:54.604Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:55

[2242] [+215306ms] [LOG] 2025-07-19T18:10:54.604Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:55

[2243] [+216306ms] [LOG] 2025-07-19T18:10:55.604Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2244] [+216306ms] [LOG] 2025-07-19T18:10:55.604Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2245] [+216306ms] [LOG] 2025-07-19T18:10:55.604Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[2246] [+216306ms] [LOG] 2025-07-19T18:10:55.604Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[2247] [+216307ms] [LOG] 2025-07-19T18:10:55.605Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2248] [+216307ms] [LOG] 2025-07-19T18:10:55.605Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2249] [+216307ms] [LOG] 2025-07-19T18:10:55.605Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:56

[2250] [+216307ms] [LOG] 2025-07-19T18:10:55.605Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:56

[2251] [+217407ms] [LOG] 2025-07-19T18:10:56.705Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2252] [+217407ms] [LOG] 2025-07-19T18:10:56.705Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2253] [+217407ms] [LOG] 2025-07-19T18:10:56.705Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2254] [+217407ms] [LOG] 2025-07-19T18:10:56.705Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2255] [+217407ms] [LOG] 2025-07-19T18:10:56.705Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2256] [+217407ms] [LOG] 2025-07-19T18:10:56.705Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2257] [+217407ms] [LOG] 2025-07-19T18:10:56.705Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:57

[2258] [+217407ms] [LOG] 2025-07-19T18:10:56.705Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:57

[2259] [+218406ms] [LOG] 2025-07-19T18:10:57.704Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2260] [+218406ms] [LOG] 2025-07-19T18:10:57.704Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2261] [+218406ms] [LOG] 2025-07-19T18:10:57.704Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2262] [+218406ms] [LOG] 2025-07-19T18:10:57.704Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2263] [+218406ms] [LOG] 2025-07-19T18:10:57.704Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2264] [+218406ms] [LOG] 2025-07-19T18:10:57.704Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2265] [+218406ms] [LOG] 2025-07-19T18:10:57.704Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:58

[2266] [+218406ms] [LOG] 2025-07-19T18:10:57.704Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:58

[2267] [+219406ms] [LOG] 2025-07-19T18:10:58.704Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2268] [+219406ms] [LOG] 2025-07-19T18:10:58.704Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2269] [+219406ms] [LOG] 2025-07-19T18:10:58.704Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2270] [+219406ms] [LOG] 2025-07-19T18:10:58.704Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2271] [+219406ms] [LOG] 2025-07-19T18:10:58.704Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2272] [+219406ms] [LOG] 2025-07-19T18:10:58.704Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2273] [+219406ms] [LOG] 2025-07-19T18:10:58.704Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:59

[2274] [+219406ms] [LOG] 2025-07-19T18:10:58.704Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:10:59

[2275] [+220406ms] [LOG] 2025-07-19T18:10:59.704Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2276] [+220406ms] [LOG] 2025-07-19T18:10:59.704Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2277] [+220406ms] [LOG] 2025-07-19T18:10:59.704Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2278] [+220406ms] [LOG] 2025-07-19T18:10:59.704Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2279] [+220406ms] [LOG] 2025-07-19T18:10:59.704Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2280] [+220406ms] [LOG] 2025-07-19T18:10:59.704Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2281] [+220406ms] [LOG] 2025-07-19T18:10:59.704Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:00

[2282] [+220406ms] [LOG] 2025-07-19T18:10:59.704Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:00

[2283] [+221505ms] [LOG] 2025-07-19T18:11:00.803Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2284] [+221506ms] [LOG] 2025-07-19T18:11:00.804Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2285] [+221506ms] [LOG] 2025-07-19T18:11:00.804Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2286] [+221506ms] [LOG] 2025-07-19T18:11:00.804Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2287] [+221506ms] [LOG] 2025-07-19T18:11:00.804Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2288] [+221506ms] [LOG] 2025-07-19T18:11:00.804Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2289] [+221506ms] [LOG] 2025-07-19T18:11:00.804Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:01

[2290] [+221506ms] [LOG] 2025-07-19T18:11:00.804Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:01

[2291] [+222505ms] [LOG] 2025-07-19T18:11:01.803Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2292] [+222505ms] [LOG] 2025-07-19T18:11:01.803Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2293] [+222505ms] [LOG] 2025-07-19T18:11:01.803Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2294] [+222505ms] [LOG] 2025-07-19T18:11:01.803Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2295] [+222506ms] [LOG] 2025-07-19T18:11:01.804Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2296] [+222506ms] [LOG] 2025-07-19T18:11:01.804Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2297] [+222506ms] [LOG] 2025-07-19T18:11:01.804Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:02

[2298] [+222506ms] [LOG] 2025-07-19T18:11:01.804Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:02

[2299] [+223603ms] [LOG] 2025-07-19T18:11:02.901Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2300] [+223603ms] [LOG] 2025-07-19T18:11:02.901Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2301] [+223603ms] [LOG] 2025-07-19T18:11:02.901Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[2302] [+223603ms] [LOG] 2025-07-19T18:11:02.901Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 98ms

[2303] [+223603ms] [LOG] 2025-07-19T18:11:02.901Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2304] [+223603ms] [LOG] 2025-07-19T18:11:02.901Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2305] [+223603ms] [LOG] 2025-07-19T18:11:02.901Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:03

[2306] [+223603ms] [LOG] 2025-07-19T18:11:02.901Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:03

[2307] [+224704ms] [LOG] 2025-07-19T18:11:04.002Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2308] [+224704ms] [LOG] 2025-07-19T18:11:04.002Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2309] [+224704ms] [LOG] 2025-07-19T18:11:04.002Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2310] [+224704ms] [LOG] 2025-07-19T18:11:04.002Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2311] [+224704ms] [LOG] 2025-07-19T18:11:04.002Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2312] [+224704ms] [LOG] 2025-07-19T18:11:04.002Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2313] [+224704ms] [LOG] 2025-07-19T18:11:04.002Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:05

[2314] [+224704ms] [LOG] 2025-07-19T18:11:04.002Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:05

[2315] [+225703ms] [LOG] 2025-07-19T18:11:05.001Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2316] [+225703ms] [LOG] 2025-07-19T18:11:05.001Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2317] [+225703ms] [LOG] 2025-07-19T18:11:05.001Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2318] [+225703ms] [LOG] 2025-07-19T18:11:05.001Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2319] [+225703ms] [LOG] 2025-07-19T18:11:05.001Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2320] [+225703ms] [LOG] 2025-07-19T18:11:05.001Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2321] [+225703ms] [LOG] 2025-07-19T18:11:05.001Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:06

[2322] [+225703ms] [LOG] 2025-07-19T18:11:05.001Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:06

[2323] [+226703ms] [LOG] 2025-07-19T18:11:06.001Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2324] [+226703ms] [LOG] 2025-07-19T18:11:06.001Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2325] [+226703ms] [LOG] 2025-07-19T18:11:06.001Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2326] [+226703ms] [LOG] 2025-07-19T18:11:06.001Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2327] [+226703ms] [LOG] 2025-07-19T18:11:06.001Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2328] [+226703ms] [LOG] 2025-07-19T18:11:06.001Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2329] [+226703ms] [LOG] 2025-07-19T18:11:06.001Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:07

[2330] [+226703ms] [LOG] 2025-07-19T18:11:06.001Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:07

[2331] [+227803ms] [LOG] 2025-07-19T18:11:07.101Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2332] [+227803ms] [LOG] 2025-07-19T18:11:07.101Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2333] [+227803ms] [LOG] 2025-07-19T18:11:07.101Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2334] [+227803ms] [LOG] 2025-07-19T18:11:07.101Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2335] [+227803ms] [LOG] 2025-07-19T18:11:07.101Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2336] [+227803ms] [LOG] 2025-07-19T18:11:07.101Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2337] [+227803ms] [LOG] 2025-07-19T18:11:07.101Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:08

[2338] [+227803ms] [LOG] 2025-07-19T18:11:07.101Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:08

[2339] [+228902ms] [LOG] 2025-07-19T18:11:08.200Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2340] [+228902ms] [LOG] 2025-07-19T18:11:08.200Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2341] [+228902ms] [LOG] 2025-07-19T18:11:08.200Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2342] [+228902ms] [LOG] 2025-07-19T18:11:08.200Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2343] [+228902ms] [LOG] 2025-07-19T18:11:08.200Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2344] [+228902ms] [LOG] 2025-07-19T18:11:08.200Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2345] [+228902ms] [LOG] 2025-07-19T18:11:08.200Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:09

[2346] [+228902ms] [LOG] 2025-07-19T18:11:08.200Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:09

[2347] [+230001ms] [LOG] 2025-07-19T18:11:09.299Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2348] [+230002ms] [LOG] 2025-07-19T18:11:09.300Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2349] [+230002ms] [LOG] 2025-07-19T18:11:09.300Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2350] [+230002ms] [LOG] 2025-07-19T18:11:09.300Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2351] [+230002ms] [LOG] 2025-07-19T18:11:09.300Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2352] [+230002ms] [LOG] 2025-07-19T18:11:09.300Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2353] [+230002ms] [LOG] 2025-07-19T18:11:09.300Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:10

[2354] [+230002ms] [LOG] 2025-07-19T18:11:09.300Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:10

[2355] [+231001ms] [LOG] 2025-07-19T18:11:10.299Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2356] [+231002ms] [LOG] 2025-07-19T18:11:10.300Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2357] [+231002ms] [LOG] 2025-07-19T18:11:10.300Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2358] [+231002ms] [LOG] 2025-07-19T18:11:10.300Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2359] [+231002ms] [LOG] 2025-07-19T18:11:10.300Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2360] [+231002ms] [LOG] 2025-07-19T18:11:10.300Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2361] [+231002ms] [LOG] 2025-07-19T18:11:10.300Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:11

[2362] [+231002ms] [LOG] 2025-07-19T18:11:10.300Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:11

[2363] [+232006ms] [LOG] 2025-07-19T18:11:11.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2364] [+232006ms] [LOG] 2025-07-19T18:11:11.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2365] [+232007ms] [LOG] 2025-07-19T18:11:11.305Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 5ms

[2366] [+232007ms] [LOG] 2025-07-19T18:11:11.305Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 5ms

[2367] [+232007ms] [LOG] 2025-07-19T18:11:11.305Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2368] [+232007ms] [LOG] 2025-07-19T18:11:11.305Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2369] [+232007ms] [LOG] 2025-07-19T18:11:11.305Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:12

[2370] [+232007ms] [LOG] 2025-07-19T18:11:11.305Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:12

[2371] [+233113ms] [LOG] 2025-07-19T18:11:12.411Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2372] [+233113ms] [LOG] 2025-07-19T18:11:12.411Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2373] [+233113ms] [LOG] 2025-07-19T18:11:12.411Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 107ms

[2374] [+233113ms] [LOG] 2025-07-19T18:11:12.411Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 107ms

[2375] [+233113ms] [LOG] 2025-07-19T18:11:12.411Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2376] [+233113ms] [LOG] 2025-07-19T18:11:12.411Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2377] [+233113ms] [LOG] 2025-07-19T18:11:12.411Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:13

[2378] [+233113ms] [LOG] 2025-07-19T18:11:12.411Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:13

[2379] [+234204ms] [LOG] 2025-07-19T18:11:13.502Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2380] [+234204ms] [LOG] 2025-07-19T18:11:13.502Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2381] [+234204ms] [LOG] 2025-07-19T18:11:13.502Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[2382] [+234204ms] [LOG] 2025-07-19T18:11:13.502Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[2383] [+234204ms] [LOG] 2025-07-19T18:11:13.502Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2384] [+234204ms] [LOG] 2025-07-19T18:11:13.502Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2385] [+234205ms] [LOG] 2025-07-19T18:11:13.503Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:14

[2386] [+234205ms] [LOG] 2025-07-19T18:11:13.503Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:14

[2387] [+235208ms] [LOG] 2025-07-19T18:11:14.506Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2388] [+235208ms] [LOG] 2025-07-19T18:11:14.506Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2389] [+235208ms] [LOG] 2025-07-19T18:11:14.506Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[2390] [+235208ms] [LOG] 2025-07-19T18:11:14.506Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[2391] [+235208ms] [LOG] 2025-07-19T18:11:14.506Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2392] [+235208ms] [LOG] 2025-07-19T18:11:14.506Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2393] [+235208ms] [LOG] 2025-07-19T18:11:14.506Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:15

[2394] [+235208ms] [LOG] 2025-07-19T18:11:14.506Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:15

[2395] [+236209ms] [LOG] 2025-07-19T18:11:15.507Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2396] [+236209ms] [LOG] 2025-07-19T18:11:15.507Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2397] [+236209ms] [LOG] 2025-07-19T18:11:15.507Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[2398] [+236209ms] [LOG] 2025-07-19T18:11:15.507Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[2399] [+236209ms] [LOG] 2025-07-19T18:11:15.507Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2400] [+236209ms] [LOG] 2025-07-19T18:11:15.507Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2401] [+236209ms] [LOG] 2025-07-19T18:11:15.507Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:16

[2402] [+236210ms] [LOG] 2025-07-19T18:11:15.508Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:16

[2403] [+237301ms] [LOG] 2025-07-19T18:11:16.599Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2404] [+237301ms] [LOG] 2025-07-19T18:11:16.599Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2405] [+237301ms] [LOG] 2025-07-19T18:11:16.599Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[2406] [+237301ms] [LOG] 2025-07-19T18:11:16.599Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[2407] [+237301ms] [LOG] 2025-07-19T18:11:16.599Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2408] [+237302ms] [LOG] 2025-07-19T18:11:16.600Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2409] [+237302ms] [LOG] 2025-07-19T18:11:16.600Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:17

[2410] [+237302ms] [LOG] 2025-07-19T18:11:16.600Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:17

[2411] [+238314ms] [LOG] 2025-07-19T18:11:17.612Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2412] [+238314ms] [LOG] 2025-07-19T18:11:17.612Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2413] [+238314ms] [LOG] 2025-07-19T18:11:17.612Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 13ms

[2414] [+238314ms] [LOG] 2025-07-19T18:11:17.612Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 13ms

[2415] [+238314ms] [LOG] 2025-07-19T18:11:17.612Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2416] [+238314ms] [LOG] 2025-07-19T18:11:17.612Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2417] [+238315ms] [LOG] 2025-07-19T18:11:17.613Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:18

[2418] [+238315ms] [LOG] 2025-07-19T18:11:17.613Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:18

[2419] [+239314ms] [LOG] 2025-07-19T18:11:18.612Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2420] [+239314ms] [LOG] 2025-07-19T18:11:18.612Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2421] [+239314ms] [LOG] 2025-07-19T18:11:18.612Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2422] [+239314ms] [LOG] 2025-07-19T18:11:18.612Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2423] [+239314ms] [LOG] 2025-07-19T18:11:18.612Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2424] [+239314ms] [LOG] 2025-07-19T18:11:18.612Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2425] [+239314ms] [LOG] 2025-07-19T18:11:18.612Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:19

[2426] [+239314ms] [LOG] 2025-07-19T18:11:18.612Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:19

[2427] [+240410ms] [LOG] 2025-07-19T18:11:19.708Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2428] [+240410ms] [LOG] 2025-07-19T18:11:19.708Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2429] [+240410ms] [LOG] 2025-07-19T18:11:19.708Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 96ms

[2430] [+240410ms] [LOG] 2025-07-19T18:11:19.708Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 96ms

[2431] [+240410ms] [LOG] 2025-07-19T18:11:19.708Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2432] [+240410ms] [LOG] 2025-07-19T18:11:19.708Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2433] [+240410ms] [LOG] 2025-07-19T18:11:19.708Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:20

[2434] [+240410ms] [LOG] 2025-07-19T18:11:19.708Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:20

[2435] [+241094ms] [LOG] 2025-07-19T18:11:20.392Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 363511
}
  参数2: {
  "type": "ping",
  "timestamp": 363511
}

[2436] [+241094ms] [LOG] 2025-07-19T18:11:20.392Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 363511
}
  参数2: {
  "type": "ping",
  "timestamp": 363511
}

[2437] [+241094ms] [LOG] 2025-07-19T18:11:20.392Z
🏓 回复pong消息

[2438] [+241094ms] [LOG] 2025-07-19T18:11:20.392Z
🏓 回复pong消息

[2439] [+241503ms] [LOG] 2025-07-19T18:11:20.801Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2440] [+241503ms] [LOG] 2025-07-19T18:11:20.801Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2441] [+241503ms] [LOG] 2025-07-19T18:11:20.801Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[2442] [+241503ms] [LOG] 2025-07-19T18:11:20.801Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[2443] [+241504ms] [LOG] 2025-07-19T18:11:20.802Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2444] [+241504ms] [LOG] 2025-07-19T18:11:20.802Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2445] [+241504ms] [LOG] 2025-07-19T18:11:20.802Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:21

[2446] [+241504ms] [LOG] 2025-07-19T18:11:20.802Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:21

[2447] [+242507ms] [LOG] 2025-07-19T18:11:21.805Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2448] [+242508ms] [LOG] 2025-07-19T18:11:21.806Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2449] [+242508ms] [LOG] 2025-07-19T18:11:21.806Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[2450] [+242508ms] [LOG] 2025-07-19T18:11:21.806Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[2451] [+242508ms] [LOG] 2025-07-19T18:11:21.806Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2452] [+242508ms] [LOG] 2025-07-19T18:11:21.806Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2453] [+242508ms] [LOG] 2025-07-19T18:11:21.806Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:22

[2454] [+242508ms] [LOG] 2025-07-19T18:11:21.806Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:22

[2455] [+243507ms] [LOG] 2025-07-19T18:11:22.805Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2456] [+243508ms] [LOG] 2025-07-19T18:11:22.806Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2457] [+243508ms] [LOG] 2025-07-19T18:11:22.806Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2458] [+243508ms] [LOG] 2025-07-19T18:11:22.806Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2459] [+243508ms] [LOG] 2025-07-19T18:11:22.806Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2460] [+243508ms] [LOG] 2025-07-19T18:11:22.806Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2461] [+243508ms] [LOG] 2025-07-19T18:11:22.806Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:23

[2462] [+243508ms] [LOG] 2025-07-19T18:11:22.806Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:23

[2463] [+244612ms] [LOG] 2025-07-19T18:11:23.910Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2464] [+244612ms] [LOG] 2025-07-19T18:11:23.910Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2465] [+244612ms] [LOG] 2025-07-19T18:11:23.910Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[2466] [+244612ms] [LOG] 2025-07-19T18:11:23.910Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[2467] [+244612ms] [LOG] 2025-07-19T18:11:23.910Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2468] [+244612ms] [LOG] 2025-07-19T18:11:23.910Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2469] [+244612ms] [LOG] 2025-07-19T18:11:23.910Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:24

[2470] [+244612ms] [LOG] 2025-07-19T18:11:23.910Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:24

[2471] [+245709ms] [LOG] 2025-07-19T18:11:25.007Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2472] [+245709ms] [LOG] 2025-07-19T18:11:25.007Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2473] [+245709ms] [LOG] 2025-07-19T18:11:25.007Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[2474] [+245709ms] [LOG] 2025-07-19T18:11:25.007Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[2475] [+245709ms] [LOG] 2025-07-19T18:11:25.007Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2476] [+245709ms] [LOG] 2025-07-19T18:11:25.007Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2477] [+245709ms] [LOG] 2025-07-19T18:11:25.007Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:26

[2478] [+245709ms] [LOG] 2025-07-19T18:11:25.007Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:26

[2479] [+246709ms] [LOG] 2025-07-19T18:11:26.007Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2480] [+246709ms] [LOG] 2025-07-19T18:11:26.007Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2481] [+246709ms] [LOG] 2025-07-19T18:11:26.007Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2482] [+246709ms] [LOG] 2025-07-19T18:11:26.007Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2483] [+246710ms] [LOG] 2025-07-19T18:11:26.008Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2484] [+246710ms] [LOG] 2025-07-19T18:11:26.008Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2485] [+246710ms] [LOG] 2025-07-19T18:11:26.008Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:27

[2486] [+246710ms] [LOG] 2025-07-19T18:11:26.008Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:27

[2487] [+247710ms] [LOG] 2025-07-19T18:11:27.008Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2488] [+247710ms] [LOG] 2025-07-19T18:11:27.008Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2489] [+247710ms] [LOG] 2025-07-19T18:11:27.008Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[2490] [+247710ms] [LOG] 2025-07-19T18:11:27.008Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[2491] [+247710ms] [LOG] 2025-07-19T18:11:27.008Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2492] [+247710ms] [LOG] 2025-07-19T18:11:27.008Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2493] [+247710ms] [LOG] 2025-07-19T18:11:27.008Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:28

[2494] [+247710ms] [LOG] 2025-07-19T18:11:27.008Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:28

[2495] [+248710ms] [LOG] 2025-07-19T18:11:28.008Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2496] [+248710ms] [LOG] 2025-07-19T18:11:28.008Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2497] [+248711ms] [LOG] 2025-07-19T18:11:28.009Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2498] [+248711ms] [LOG] 2025-07-19T18:11:28.009Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2499] [+248711ms] [LOG] 2025-07-19T18:11:28.009Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2500] [+248711ms] [LOG] 2025-07-19T18:11:28.009Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2501] [+248711ms] [LOG] 2025-07-19T18:11:28.009Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:29

[2502] [+248711ms] [LOG] 2025-07-19T18:11:28.009Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:29

[2503] [+249710ms] [LOG] 2025-07-19T18:11:29.008Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2504] [+249710ms] [LOG] 2025-07-19T18:11:29.008Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2505] [+249710ms] [LOG] 2025-07-19T18:11:29.008Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2506] [+249710ms] [LOG] 2025-07-19T18:11:29.008Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2507] [+249711ms] [LOG] 2025-07-19T18:11:29.009Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2508] [+249711ms] [LOG] 2025-07-19T18:11:29.009Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2509] [+249711ms] [LOG] 2025-07-19T18:11:29.009Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:30

[2510] [+249711ms] [LOG] 2025-07-19T18:11:29.009Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:30

[2511] [+250809ms] [LOG] 2025-07-19T18:11:30.107Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2512] [+250809ms] [LOG] 2025-07-19T18:11:30.107Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2513] [+250809ms] [LOG] 2025-07-19T18:11:30.107Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2514] [+250809ms] [LOG] 2025-07-19T18:11:30.107Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2515] [+250809ms] [LOG] 2025-07-19T18:11:30.107Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2516] [+250809ms] [LOG] 2025-07-19T18:11:30.107Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2517] [+250809ms] [LOG] 2025-07-19T18:11:30.107Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:31

[2518] [+250809ms] [LOG] 2025-07-19T18:11:30.107Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:31

[2519] [+251909ms] [LOG] 2025-07-19T18:11:31.207Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2520] [+251909ms] [LOG] 2025-07-19T18:11:31.207Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2521] [+251909ms] [LOG] 2025-07-19T18:11:31.207Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2522] [+251909ms] [LOG] 2025-07-19T18:11:31.207Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2523] [+251909ms] [LOG] 2025-07-19T18:11:31.207Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2524] [+251909ms] [LOG] 2025-07-19T18:11:31.207Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2525] [+251909ms] [LOG] 2025-07-19T18:11:31.207Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:32

[2526] [+251909ms] [LOG] 2025-07-19T18:11:31.207Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:32

[2527] [+253008ms] [LOG] 2025-07-19T18:11:32.306Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2528] [+253008ms] [LOG] 2025-07-19T18:11:32.306Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2529] [+253008ms] [LOG] 2025-07-19T18:11:32.306Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2530] [+253008ms] [LOG] 2025-07-19T18:11:32.306Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2531] [+253008ms] [LOG] 2025-07-19T18:11:32.306Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2532] [+253008ms] [LOG] 2025-07-19T18:11:32.306Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2533] [+253008ms] [LOG] 2025-07-19T18:11:32.306Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:33

[2534] [+253008ms] [LOG] 2025-07-19T18:11:32.306Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:33

[2535] [+254103ms] [LOG] 2025-07-19T18:11:33.401Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2536] [+254103ms] [LOG] 2025-07-19T18:11:33.401Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2537] [+254103ms] [LOG] 2025-07-19T18:11:33.401Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 95ms

[2538] [+254103ms] [LOG] 2025-07-19T18:11:33.401Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 95ms

[2539] [+254103ms] [LOG] 2025-07-19T18:11:33.401Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2540] [+254103ms] [LOG] 2025-07-19T18:11:33.401Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2541] [+254104ms] [LOG] 2025-07-19T18:11:33.402Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:34

[2542] [+254104ms] [LOG] 2025-07-19T18:11:33.402Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:34

[2543] [+255105ms] [LOG] 2025-07-19T18:11:34.403Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2544] [+255105ms] [LOG] 2025-07-19T18:11:34.403Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2545] [+255105ms] [LOG] 2025-07-19T18:11:34.403Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2546] [+255105ms] [LOG] 2025-07-19T18:11:34.403Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2547] [+255105ms] [LOG] 2025-07-19T18:11:34.403Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2548] [+255105ms] [LOG] 2025-07-19T18:11:34.403Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2549] [+255105ms] [LOG] 2025-07-19T18:11:34.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:35

[2550] [+255105ms] [LOG] 2025-07-19T18:11:34.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:35

[2551] [+256206ms] [LOG] 2025-07-19T18:11:35.504Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2552] [+256206ms] [LOG] 2025-07-19T18:11:35.504Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2553] [+256206ms] [LOG] 2025-07-19T18:11:35.504Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 101ms

[2554] [+256206ms] [LOG] 2025-07-19T18:11:35.504Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 101ms

[2555] [+256206ms] [LOG] 2025-07-19T18:11:35.504Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2556] [+256206ms] [LOG] 2025-07-19T18:11:35.504Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2557] [+256206ms] [LOG] 2025-07-19T18:11:35.504Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:36

[2558] [+256206ms] [LOG] 2025-07-19T18:11:35.504Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:36

[2559] [+257306ms] [LOG] 2025-07-19T18:11:36.604Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2560] [+257306ms] [LOG] 2025-07-19T18:11:36.604Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2561] [+257306ms] [LOG] 2025-07-19T18:11:36.604Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2562] [+257306ms] [LOG] 2025-07-19T18:11:36.604Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2563] [+257306ms] [LOG] 2025-07-19T18:11:36.604Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2564] [+257306ms] [LOG] 2025-07-19T18:11:36.604Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2565] [+257306ms] [LOG] 2025-07-19T18:11:36.604Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:37

[2566] [+257306ms] [LOG] 2025-07-19T18:11:36.604Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:37

[2567] [+258405ms] [LOG] 2025-07-19T18:11:37.703Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2568] [+258405ms] [LOG] 2025-07-19T18:11:37.703Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2569] [+258405ms] [LOG] 2025-07-19T18:11:37.703Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2570] [+258405ms] [LOG] 2025-07-19T18:11:37.703Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2571] [+258405ms] [LOG] 2025-07-19T18:11:37.703Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2572] [+258405ms] [LOG] 2025-07-19T18:11:37.703Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2573] [+258405ms] [LOG] 2025-07-19T18:11:37.703Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:38

[2574] [+258405ms] [LOG] 2025-07-19T18:11:37.703Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:38

[2575] [+259405ms] [LOG] 2025-07-19T18:11:38.703Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2576] [+259405ms] [LOG] 2025-07-19T18:11:38.703Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2577] [+259405ms] [LOG] 2025-07-19T18:11:38.703Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2578] [+259405ms] [LOG] 2025-07-19T18:11:38.703Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2579] [+259405ms] [LOG] 2025-07-19T18:11:38.703Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2580] [+259405ms] [LOG] 2025-07-19T18:11:38.703Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2581] [+259405ms] [LOG] 2025-07-19T18:11:38.703Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:39

[2582] [+259405ms] [LOG] 2025-07-19T18:11:38.703Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:39

[2583] [+260502ms] [LOG] 2025-07-19T18:11:39.800Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2584] [+260502ms] [LOG] 2025-07-19T18:11:39.800Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2585] [+260502ms] [LOG] 2025-07-19T18:11:39.800Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[2586] [+260502ms] [LOG] 2025-07-19T18:11:39.800Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[2587] [+260502ms] [LOG] 2025-07-19T18:11:39.800Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2588] [+260502ms] [LOG] 2025-07-19T18:11:39.800Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2589] [+260502ms] [LOG] 2025-07-19T18:11:39.800Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:40

[2590] [+260502ms] [LOG] 2025-07-19T18:11:39.800Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:40

[2591] [+261504ms] [LOG] 2025-07-19T18:11:40.802Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2592] [+261504ms] [LOG] 2025-07-19T18:11:40.802Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2593] [+261505ms] [LOG] 2025-07-19T18:11:40.803Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2594] [+261505ms] [LOG] 2025-07-19T18:11:40.803Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2595] [+261505ms] [LOG] 2025-07-19T18:11:40.803Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2596] [+261505ms] [LOG] 2025-07-19T18:11:40.803Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2597] [+261505ms] [LOG] 2025-07-19T18:11:40.803Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:41

[2598] [+261505ms] [LOG] 2025-07-19T18:11:40.803Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:41

[2599] [+262504ms] [LOG] 2025-07-19T18:11:41.802Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2600] [+262504ms] [LOG] 2025-07-19T18:11:41.802Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2601] [+262504ms] [LOG] 2025-07-19T18:11:41.802Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2602] [+262504ms] [LOG] 2025-07-19T18:11:41.802Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2603] [+262505ms] [LOG] 2025-07-19T18:11:41.803Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2604] [+262505ms] [LOG] 2025-07-19T18:11:41.803Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2605] [+262505ms] [LOG] 2025-07-19T18:11:41.803Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:42

[2606] [+262505ms] [LOG] 2025-07-19T18:11:41.803Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:42

[2607] [+263603ms] [LOG] 2025-07-19T18:11:42.901Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2608] [+263603ms] [LOG] 2025-07-19T18:11:42.901Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2609] [+263603ms] [LOG] 2025-07-19T18:11:42.901Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2610] [+263603ms] [LOG] 2025-07-19T18:11:42.901Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2611] [+263603ms] [LOG] 2025-07-19T18:11:42.901Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2612] [+263603ms] [LOG] 2025-07-19T18:11:42.901Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2613] [+263603ms] [LOG] 2025-07-19T18:11:42.901Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:43

[2614] [+263603ms] [LOG] 2025-07-19T18:11:42.901Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:43

[2615] [+264603ms] [LOG] 2025-07-19T18:11:43.901Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2616] [+264603ms] [LOG] 2025-07-19T18:11:43.901Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2617] [+264603ms] [LOG] 2025-07-19T18:11:43.901Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2618] [+264603ms] [LOG] 2025-07-19T18:11:43.901Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2619] [+264603ms] [LOG] 2025-07-19T18:11:43.901Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2620] [+264603ms] [LOG] 2025-07-19T18:11:43.901Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2621] [+264603ms] [LOG] 2025-07-19T18:11:43.901Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:44

[2622] [+264603ms] [LOG] 2025-07-19T18:11:43.901Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:44

[2623] [+265702ms] [LOG] 2025-07-19T18:11:45.000Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2624] [+265702ms] [LOG] 2025-07-19T18:11:45.000Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2625] [+265702ms] [LOG] 2025-07-19T18:11:45.000Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2626] [+265702ms] [LOG] 2025-07-19T18:11:45.000Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2627] [+265702ms] [LOG] 2025-07-19T18:11:45.000Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2628] [+265702ms] [LOG] 2025-07-19T18:11:45.000Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2629] [+265702ms] [LOG] 2025-07-19T18:11:45.000Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:46

[2630] [+265702ms] [LOG] 2025-07-19T18:11:45.000Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:46

[2631] [+266802ms] [LOG] 2025-07-19T18:11:46.100Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2632] [+266802ms] [LOG] 2025-07-19T18:11:46.100Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2633] [+266802ms] [LOG] 2025-07-19T18:11:46.100Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2634] [+266802ms] [LOG] 2025-07-19T18:11:46.100Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2635] [+266802ms] [LOG] 2025-07-19T18:11:46.100Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2636] [+266802ms] [LOG] 2025-07-19T18:11:46.100Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2637] [+266802ms] [LOG] 2025-07-19T18:11:46.100Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:47

[2638] [+266802ms] [LOG] 2025-07-19T18:11:46.100Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:47

[2639] [+267801ms] [LOG] 2025-07-19T18:11:47.099Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2640] [+267801ms] [LOG] 2025-07-19T18:11:47.099Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2641] [+267801ms] [LOG] 2025-07-19T18:11:47.099Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2642] [+267801ms] [LOG] 2025-07-19T18:11:47.099Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2643] [+267801ms] [LOG] 2025-07-19T18:11:47.099Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2644] [+267802ms] [LOG] 2025-07-19T18:11:47.100Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2645] [+267802ms] [LOG] 2025-07-19T18:11:47.100Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:48

[2646] [+267802ms] [LOG] 2025-07-19T18:11:47.100Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:48

[2647] [+268801ms] [LOG] 2025-07-19T18:11:48.099Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2648] [+268801ms] [LOG] 2025-07-19T18:11:48.099Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2649] [+268801ms] [LOG] 2025-07-19T18:11:48.099Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2650] [+268801ms] [LOG] 2025-07-19T18:11:48.099Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2651] [+268802ms] [LOG] 2025-07-19T18:11:48.100Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2652] [+268802ms] [LOG] 2025-07-19T18:11:48.100Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2653] [+268802ms] [LOG] 2025-07-19T18:11:48.100Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:49

[2654] [+268802ms] [LOG] 2025-07-19T18:11:48.100Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:49

[2655] [+269806ms] [LOG] 2025-07-19T18:11:49.104Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2656] [+269806ms] [LOG] 2025-07-19T18:11:49.104Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2657] [+269806ms] [LOG] 2025-07-19T18:11:49.104Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 5ms

[2658] [+269806ms] [LOG] 2025-07-19T18:11:49.104Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 5ms

[2659] [+269807ms] [LOG] 2025-07-19T18:11:49.105Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2660] [+269807ms] [LOG] 2025-07-19T18:11:49.105Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2661] [+269807ms] [LOG] 2025-07-19T18:11:49.105Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:50

[2662] [+269807ms] [LOG] 2025-07-19T18:11:49.105Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:50

[2663] [+270911ms] [LOG] 2025-07-19T18:11:50.209Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2664] [+270911ms] [LOG] 2025-07-19T18:11:50.209Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2665] [+270912ms] [LOG] 2025-07-19T18:11:50.210Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[2666] [+270912ms] [LOG] 2025-07-19T18:11:50.210Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 105ms

[2667] [+270912ms] [LOG] 2025-07-19T18:11:50.210Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2668] [+270912ms] [LOG] 2025-07-19T18:11:50.210Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2669] [+270912ms] [LOG] 2025-07-19T18:11:50.210Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:51

[2670] [+270912ms] [LOG] 2025-07-19T18:11:50.210Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:51

[2671] [+271912ms] [LOG] 2025-07-19T18:11:51.210Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2672] [+271912ms] [LOG] 2025-07-19T18:11:51.210Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2673] [+271912ms] [LOG] 2025-07-19T18:11:51.210Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[2674] [+271912ms] [LOG] 2025-07-19T18:11:51.210Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[2675] [+271912ms] [LOG] 2025-07-19T18:11:51.210Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2676] [+271912ms] [LOG] 2025-07-19T18:11:51.210Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2677] [+271913ms] [LOG] 2025-07-19T18:11:51.211Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:52

[2678] [+271913ms] [LOG] 2025-07-19T18:11:51.211Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:52

[2679] [+273003ms] [LOG] 2025-07-19T18:11:52.301Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2680] [+273003ms] [LOG] 2025-07-19T18:11:52.301Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2681] [+273003ms] [LOG] 2025-07-19T18:11:52.301Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[2682] [+273003ms] [LOG] 2025-07-19T18:11:52.301Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[2683] [+273003ms] [LOG] 2025-07-19T18:11:52.301Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2684] [+273003ms] [LOG] 2025-07-19T18:11:52.301Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2685] [+273003ms] [LOG] 2025-07-19T18:11:52.301Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:53

[2686] [+273003ms] [LOG] 2025-07-19T18:11:52.301Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:53

[2687] [+274112ms] [LOG] 2025-07-19T18:11:53.410Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2688] [+274112ms] [LOG] 2025-07-19T18:11:53.410Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2689] [+274112ms] [LOG] 2025-07-19T18:11:53.410Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 109ms

[2690] [+274112ms] [LOG] 2025-07-19T18:11:53.410Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 109ms

[2691] [+274112ms] [LOG] 2025-07-19T18:11:53.410Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2692] [+274112ms] [LOG] 2025-07-19T18:11:53.410Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2693] [+274113ms] [LOG] 2025-07-19T18:11:53.411Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:54

[2694] [+274113ms] [LOG] 2025-07-19T18:11:53.411Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:54

[2695] [+275204ms] [LOG] 2025-07-19T18:11:54.502Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2696] [+275204ms] [LOG] 2025-07-19T18:11:54.502Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2697] [+275205ms] [LOG] 2025-07-19T18:11:54.503Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[2698] [+275205ms] [LOG] 2025-07-19T18:11:54.503Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[2699] [+275205ms] [LOG] 2025-07-19T18:11:54.503Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2700] [+275205ms] [LOG] 2025-07-19T18:11:54.503Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2701] [+275205ms] [LOG] 2025-07-19T18:11:54.503Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:55

[2702] [+275205ms] [LOG] 2025-07-19T18:11:54.503Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:55

[2703] [+276208ms] [LOG] 2025-07-19T18:11:55.506Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2704] [+276208ms] [LOG] 2025-07-19T18:11:55.506Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2705] [+276208ms] [LOG] 2025-07-19T18:11:55.506Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[2706] [+276209ms] [LOG] 2025-07-19T18:11:55.507Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[2707] [+276209ms] [LOG] 2025-07-19T18:11:55.507Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2708] [+276209ms] [LOG] 2025-07-19T18:11:55.507Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2709] [+276209ms] [LOG] 2025-07-19T18:11:55.507Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:56

[2710] [+276209ms] [LOG] 2025-07-19T18:11:55.507Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:56

[2711] [+277302ms] [LOG] 2025-07-19T18:11:56.600Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2712] [+277302ms] [LOG] 2025-07-19T18:11:56.600Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2713] [+277302ms] [LOG] 2025-07-19T18:11:56.600Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[2714] [+277302ms] [LOG] 2025-07-19T18:11:56.600Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[2715] [+277302ms] [LOG] 2025-07-19T18:11:56.600Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2716] [+277302ms] [LOG] 2025-07-19T18:11:56.600Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2717] [+277302ms] [LOG] 2025-07-19T18:11:56.600Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:57

[2718] [+277302ms] [LOG] 2025-07-19T18:11:56.600Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:57

[2719] [+278305ms] [LOG] 2025-07-19T18:11:57.603Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2720] [+278305ms] [LOG] 2025-07-19T18:11:57.603Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2721] [+278305ms] [LOG] 2025-07-19T18:11:57.603Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[2722] [+278305ms] [LOG] 2025-07-19T18:11:57.603Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[2723] [+278305ms] [LOG] 2025-07-19T18:11:57.603Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2724] [+278305ms] [LOG] 2025-07-19T18:11:57.603Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2725] [+278305ms] [LOG] 2025-07-19T18:11:57.603Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:58

[2726] [+278305ms] [LOG] 2025-07-19T18:11:57.603Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:58

[2727] [+279413ms] [LOG] 2025-07-19T18:11:58.711Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2728] [+279413ms] [LOG] 2025-07-19T18:11:58.711Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2729] [+279413ms] [LOG] 2025-07-19T18:11:58.711Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 108ms

[2730] [+279413ms] [LOG] 2025-07-19T18:11:58.711Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 108ms

[2731] [+279413ms] [LOG] 2025-07-19T18:11:58.711Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2732] [+279414ms] [LOG] 2025-07-19T18:11:58.712Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2733] [+279414ms] [LOG] 2025-07-19T18:11:58.712Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:59

[2734] [+279414ms] [LOG] 2025-07-19T18:11:58.712Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:11:59

[2735] [+280413ms] [LOG] 2025-07-19T18:11:59.711Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2736] [+280413ms] [LOG] 2025-07-19T18:11:59.711Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2737] [+280413ms] [LOG] 2025-07-19T18:11:59.711Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2738] [+280413ms] [LOG] 2025-07-19T18:11:59.711Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2739] [+280413ms] [LOG] 2025-07-19T18:11:59.711Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2740] [+280413ms] [LOG] 2025-07-19T18:11:59.711Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2741] [+280413ms] [LOG] 2025-07-19T18:11:59.711Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:00

[2742] [+280413ms] [LOG] 2025-07-19T18:11:59.711Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:00

[2743] [+281510ms] [LOG] 2025-07-19T18:12:00.808Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2744] [+281510ms] [LOG] 2025-07-19T18:12:00.808Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2745] [+281510ms] [LOG] 2025-07-19T18:12:00.808Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 96ms

[2746] [+281510ms] [LOG] 2025-07-19T18:12:00.808Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 96ms

[2747] [+281510ms] [LOG] 2025-07-19T18:12:00.808Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2748] [+281510ms] [LOG] 2025-07-19T18:12:00.808Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2749] [+281511ms] [LOG] 2025-07-19T18:12:00.809Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:01

[2750] [+281511ms] [LOG] 2025-07-19T18:12:00.809Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:01

[2751] [+282509ms] [LOG] 2025-07-19T18:12:01.807Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2752] [+282509ms] [LOG] 2025-07-19T18:12:01.807Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2753] [+282509ms] [LOG] 2025-07-19T18:12:01.807Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2754] [+282509ms] [LOG] 2025-07-19T18:12:01.807Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2755] [+282510ms] [LOG] 2025-07-19T18:12:01.808Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2756] [+282510ms] [LOG] 2025-07-19T18:12:01.808Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2757] [+282510ms] [LOG] 2025-07-19T18:12:01.808Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:02

[2758] [+282510ms] [LOG] 2025-07-19T18:12:01.808Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:02

[2759] [+283509ms] [LOG] 2025-07-19T18:12:02.807Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2760] [+283509ms] [LOG] 2025-07-19T18:12:02.807Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2761] [+283509ms] [LOG] 2025-07-19T18:12:02.807Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2762] [+283509ms] [LOG] 2025-07-19T18:12:02.807Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2763] [+283510ms] [LOG] 2025-07-19T18:12:02.808Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2764] [+283510ms] [LOG] 2025-07-19T18:12:02.808Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2765] [+283510ms] [LOG] 2025-07-19T18:12:02.808Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:03

[2766] [+283510ms] [LOG] 2025-07-19T18:12:02.808Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:03

[2767] [+284511ms] [LOG] 2025-07-19T18:12:03.809Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2768] [+284511ms] [LOG] 2025-07-19T18:12:03.809Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2769] [+284511ms] [LOG] 2025-07-19T18:12:03.809Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2770] [+284511ms] [LOG] 2025-07-19T18:12:03.809Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2771] [+284511ms] [LOG] 2025-07-19T18:12:03.809Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2772] [+284511ms] [LOG] 2025-07-19T18:12:03.809Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2773] [+284511ms] [LOG] 2025-07-19T18:12:03.809Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:04

[2774] [+284512ms] [LOG] 2025-07-19T18:12:03.810Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:04

[2775] [+285604ms] [LOG] 2025-07-19T18:12:04.902Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2776] [+285604ms] [LOG] 2025-07-19T18:12:04.902Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2777] [+285604ms] [LOG] 2025-07-19T18:12:04.902Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[2778] [+285604ms] [LOG] 2025-07-19T18:12:04.902Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[2779] [+285604ms] [LOG] 2025-07-19T18:12:04.902Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2780] [+285604ms] [LOG] 2025-07-19T18:12:04.902Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2781] [+285604ms] [LOG] 2025-07-19T18:12:04.902Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:05

[2782] [+285604ms] [LOG] 2025-07-19T18:12:04.902Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:05

[2783] [+286605ms] [LOG] 2025-07-19T18:12:05.903Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2784] [+286605ms] [LOG] 2025-07-19T18:12:05.903Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2785] [+286605ms] [LOG] 2025-07-19T18:12:05.903Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[2786] [+286605ms] [LOG] 2025-07-19T18:12:05.903Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[2787] [+286605ms] [LOG] 2025-07-19T18:12:05.903Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2788] [+286605ms] [LOG] 2025-07-19T18:12:05.903Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2789] [+286605ms] [LOG] 2025-07-19T18:12:05.903Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:06

[2790] [+286605ms] [LOG] 2025-07-19T18:12:05.903Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:06

[2791] [+287709ms] [LOG] 2025-07-19T18:12:07.007Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2792] [+287709ms] [LOG] 2025-07-19T18:12:07.007Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2793] [+287710ms] [LOG] 2025-07-19T18:12:07.008Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 104ms

[2794] [+287710ms] [LOG] 2025-07-19T18:12:07.008Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 104ms

[2795] [+287710ms] [LOG] 2025-07-19T18:12:07.008Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2796] [+287710ms] [LOG] 2025-07-19T18:12:07.008Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2797] [+287710ms] [LOG] 2025-07-19T18:12:07.008Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:08

[2798] [+287710ms] [LOG] 2025-07-19T18:12:07.008Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:08

[2799] [+288802ms] [LOG] 2025-07-19T18:12:08.100Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2800] [+288802ms] [LOG] 2025-07-19T18:12:08.100Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2801] [+288802ms] [LOG] 2025-07-19T18:12:08.100Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[2802] [+288802ms] [LOG] 2025-07-19T18:12:08.100Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[2803] [+288802ms] [LOG] 2025-07-19T18:12:08.100Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2804] [+288802ms] [LOG] 2025-07-19T18:12:08.100Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2805] [+288802ms] [LOG] 2025-07-19T18:12:08.100Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:09

[2806] [+288802ms] [LOG] 2025-07-19T18:12:08.100Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:09

[2807] [+289806ms] [LOG] 2025-07-19T18:12:09.104Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2808] [+289806ms] [LOG] 2025-07-19T18:12:09.104Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2809] [+289806ms] [LOG] 2025-07-19T18:12:09.104Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[2810] [+289806ms] [LOG] 2025-07-19T18:12:09.104Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 4ms

[2811] [+289806ms] [LOG] 2025-07-19T18:12:09.104Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2812] [+289806ms] [LOG] 2025-07-19T18:12:09.104Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2813] [+289807ms] [LOG] 2025-07-19T18:12:09.105Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:10

[2814] [+289807ms] [LOG] 2025-07-19T18:12:09.105Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:10

[2815] [+290808ms] [LOG] 2025-07-19T18:12:10.106Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2816] [+290808ms] [LOG] 2025-07-19T18:12:10.106Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2817] [+290809ms] [LOG] 2025-07-19T18:12:10.107Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2818] [+290809ms] [LOG] 2025-07-19T18:12:10.107Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2819] [+290809ms] [LOG] 2025-07-19T18:12:10.107Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2820] [+290809ms] [LOG] 2025-07-19T18:12:10.107Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2821] [+290809ms] [LOG] 2025-07-19T18:12:10.107Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:11

[2822] [+290809ms] [LOG] 2025-07-19T18:12:10.107Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:11

[2823] [+291908ms] [LOG] 2025-07-19T18:12:11.206Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2824] [+291908ms] [LOG] 2025-07-19T18:12:11.206Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2825] [+291908ms] [LOG] 2025-07-19T18:12:11.206Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2826] [+291908ms] [LOG] 2025-07-19T18:12:11.206Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2827] [+291908ms] [LOG] 2025-07-19T18:12:11.206Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2828] [+291908ms] [LOG] 2025-07-19T18:12:11.206Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2829] [+291909ms] [LOG] 2025-07-19T18:12:11.207Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:12

[2830] [+291909ms] [LOG] 2025-07-19T18:12:11.207Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:12

[2831] [+293004ms] [LOG] 2025-07-19T18:12:12.302Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2832] [+293004ms] [LOG] 2025-07-19T18:12:12.302Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2833] [+293004ms] [LOG] 2025-07-19T18:12:12.302Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 96ms

[2834] [+293004ms] [LOG] 2025-07-19T18:12:12.302Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 96ms

[2835] [+293005ms] [LOG] 2025-07-19T18:12:12.303Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2836] [+293005ms] [LOG] 2025-07-19T18:12:12.303Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2837] [+293005ms] [LOG] 2025-07-19T18:12:12.303Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:13

[2838] [+293005ms] [LOG] 2025-07-19T18:12:12.303Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:13

[2839] [+294006ms] [LOG] 2025-07-19T18:12:13.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2840] [+294006ms] [LOG] 2025-07-19T18:12:13.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2841] [+294006ms] [LOG] 2025-07-19T18:12:13.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2842] [+294007ms] [LOG] 2025-07-19T18:12:13.305Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2843] [+294007ms] [LOG] 2025-07-19T18:12:13.305Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2844] [+294007ms] [LOG] 2025-07-19T18:12:13.305Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2845] [+294007ms] [LOG] 2025-07-19T18:12:13.305Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:14

[2846] [+294007ms] [LOG] 2025-07-19T18:12:13.305Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:14

[2847] [+295105ms] [LOG] 2025-07-19T18:12:14.403Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2848] [+295105ms] [LOG] 2025-07-19T18:12:14.403Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2849] [+295106ms] [LOG] 2025-07-19T18:12:14.404Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2850] [+295106ms] [LOG] 2025-07-19T18:12:14.404Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2851] [+295106ms] [LOG] 2025-07-19T18:12:14.404Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2852] [+295106ms] [LOG] 2025-07-19T18:12:14.404Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2853] [+295106ms] [LOG] 2025-07-19T18:12:14.404Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:15

[2854] [+295106ms] [LOG] 2025-07-19T18:12:14.404Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:15

[2855] [+296105ms] [LOG] 2025-07-19T18:12:15.403Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2856] [+296105ms] [LOG] 2025-07-19T18:12:15.403Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2857] [+296105ms] [LOG] 2025-07-19T18:12:15.403Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2858] [+296105ms] [LOG] 2025-07-19T18:12:15.403Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2859] [+296105ms] [LOG] 2025-07-19T18:12:15.403Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2860] [+296105ms] [LOG] 2025-07-19T18:12:15.403Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2861] [+296105ms] [LOG] 2025-07-19T18:12:15.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:16

[2862] [+296105ms] [LOG] 2025-07-19T18:12:15.403Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:16

[2863] [+297108ms] [LOG] 2025-07-19T18:12:16.406Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2864] [+297108ms] [LOG] 2025-07-19T18:12:16.406Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2865] [+297108ms] [LOG] 2025-07-19T18:12:16.406Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[2866] [+297108ms] [LOG] 2025-07-19T18:12:16.406Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[2867] [+297108ms] [LOG] 2025-07-19T18:12:16.406Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2868] [+297108ms] [LOG] 2025-07-19T18:12:16.406Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2869] [+297108ms] [LOG] 2025-07-19T18:12:16.406Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:17

[2870] [+297108ms] [LOG] 2025-07-19T18:12:16.406Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:17

[2871] [+298205ms] [LOG] 2025-07-19T18:12:17.503Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2872] [+298205ms] [LOG] 2025-07-19T18:12:17.503Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2873] [+298205ms] [LOG] 2025-07-19T18:12:17.503Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[2874] [+298205ms] [LOG] 2025-07-19T18:12:17.503Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 97ms

[2875] [+298206ms] [LOG] 2025-07-19T18:12:17.504Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2876] [+298206ms] [LOG] 2025-07-19T18:12:17.504Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2877] [+298206ms] [LOG] 2025-07-19T18:12:17.504Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:18

[2878] [+298206ms] [LOG] 2025-07-19T18:12:17.504Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:18

[2879] [+299304ms] [LOG] 2025-07-19T18:12:18.602Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2880] [+299304ms] [LOG] 2025-07-19T18:12:18.602Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2881] [+299305ms] [LOG] 2025-07-19T18:12:18.603Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2882] [+299305ms] [LOG] 2025-07-19T18:12:18.603Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2883] [+299305ms] [LOG] 2025-07-19T18:12:18.603Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2884] [+299305ms] [LOG] 2025-07-19T18:12:18.603Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2885] [+299305ms] [LOG] 2025-07-19T18:12:18.603Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:19

[2886] [+299305ms] [LOG] 2025-07-19T18:12:18.603Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:19

[2887] [+300404ms] [LOG] 2025-07-19T18:12:19.702Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2888] [+300405ms] [LOG] 2025-07-19T18:12:19.703Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2889] [+300405ms] [LOG] 2025-07-19T18:12:19.703Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2890] [+300405ms] [LOG] 2025-07-19T18:12:19.703Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[2891] [+300405ms] [LOG] 2025-07-19T18:12:19.703Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2892] [+300405ms] [LOG] 2025-07-19T18:12:19.703Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2893] [+300405ms] [LOG] 2025-07-19T18:12:19.703Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:20

[2894] [+300405ms] [LOG] 2025-07-19T18:12:19.703Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:20

[2895] [+301080ms] [LOG] 2025-07-19T18:12:20.378Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 423512
}
  参数2: {
  "type": "ping",
  "timestamp": 423512
}

[2896] [+301080ms] [LOG] 2025-07-19T18:12:20.378Z
📨 WebSocket消息: {
  "type": "ping",
  "timestamp": 423512
}
  参数2: {
  "type": "ping",
  "timestamp": 423512
}

[2897] [+301080ms] [LOG] 2025-07-19T18:12:20.378Z
🏓 回复pong消息

[2898] [+301080ms] [LOG] 2025-07-19T18:12:20.378Z
🏓 回复pong消息

[2899] [+301503ms] [LOG] 2025-07-19T18:12:20.801Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2900] [+301504ms] [LOG] 2025-07-19T18:12:20.802Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2901] [+301504ms] [LOG] 2025-07-19T18:12:20.802Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2902] [+301504ms] [LOG] 2025-07-19T18:12:20.802Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2903] [+301504ms] [LOG] 2025-07-19T18:12:20.802Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2904] [+301504ms] [LOG] 2025-07-19T18:12:20.802Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2905] [+301504ms] [LOG] 2025-07-19T18:12:20.802Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:21

[2906] [+301505ms] [LOG] 2025-07-19T18:12:20.803Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:21

[2907] [+302602ms] [LOG] 2025-07-19T18:12:21.900Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2908] [+302602ms] [LOG] 2025-07-19T18:12:21.900Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2909] [+302602ms] [LOG] 2025-07-19T18:12:21.900Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2910] [+302602ms] [LOG] 2025-07-19T18:12:21.900Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2911] [+302602ms] [LOG] 2025-07-19T18:12:21.900Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2912] [+302602ms] [LOG] 2025-07-19T18:12:21.900Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2913] [+302603ms] [LOG] 2025-07-19T18:12:21.901Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:22

[2914] [+302603ms] [LOG] 2025-07-19T18:12:21.901Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:22

[2915] [+303602ms] [LOG] 2025-07-19T18:12:22.900Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2916] [+303602ms] [LOG] 2025-07-19T18:12:22.900Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2917] [+303602ms] [LOG] 2025-07-19T18:12:22.900Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2918] [+303602ms] [LOG] 2025-07-19T18:12:22.900Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2919] [+303602ms] [LOG] 2025-07-19T18:12:22.900Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2920] [+303602ms] [LOG] 2025-07-19T18:12:22.900Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2921] [+303602ms] [LOG] 2025-07-19T18:12:22.900Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:23

[2922] [+303602ms] [LOG] 2025-07-19T18:12:22.900Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:23

[2923] [+304701ms] [LOG] 2025-07-19T18:12:23.999Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2924] [+304701ms] [LOG] 2025-07-19T18:12:23.999Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2925] [+304701ms] [LOG] 2025-07-19T18:12:23.999Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2926] [+304701ms] [LOG] 2025-07-19T18:12:23.999Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[2927] [+304701ms] [LOG] 2025-07-19T18:12:23.999Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2928] [+304701ms] [LOG] 2025-07-19T18:12:23.999Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2929] [+304701ms] [LOG] 2025-07-19T18:12:23.999Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:24

[2930] [+304701ms] [LOG] 2025-07-19T18:12:23.999Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:24

[2931] [+305702ms] [LOG] 2025-07-19T18:12:25.000Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2932] [+305702ms] [LOG] 2025-07-19T18:12:25.000Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2933] [+305702ms] [LOG] 2025-07-19T18:12:25.000Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[2934] [+305702ms] [LOG] 2025-07-19T18:12:25.000Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[2935] [+305702ms] [LOG] 2025-07-19T18:12:25.000Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2936] [+305702ms] [LOG] 2025-07-19T18:12:25.000Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2937] [+305702ms] [LOG] 2025-07-19T18:12:25.000Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:26

[2938] [+305702ms] [LOG] 2025-07-19T18:12:25.000Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:26

[2939] [+306702ms] [LOG] 2025-07-19T18:12:26.000Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2940] [+306703ms] [LOG] 2025-07-19T18:12:26.001Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2941] [+306703ms] [LOG] 2025-07-19T18:12:26.001Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2942] [+306703ms] [LOG] 2025-07-19T18:12:26.001Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2943] [+306703ms] [LOG] 2025-07-19T18:12:26.001Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2944] [+306703ms] [LOG] 2025-07-19T18:12:26.001Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2945] [+306703ms] [LOG] 2025-07-19T18:12:26.001Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:27

[2946] [+306703ms] [LOG] 2025-07-19T18:12:26.001Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:27

[2947] [+307704ms] [LOG] 2025-07-19T18:12:27.002Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2948] [+307704ms] [LOG] 2025-07-19T18:12:27.002Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2949] [+307704ms] [LOG] 2025-07-19T18:12:27.002Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2950] [+307704ms] [LOG] 2025-07-19T18:12:27.002Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2951] [+307705ms] [LOG] 2025-07-19T18:12:27.003Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2952] [+307705ms] [LOG] 2025-07-19T18:12:27.003Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2953] [+307705ms] [LOG] 2025-07-19T18:12:27.003Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:28

[2954] [+307705ms] [LOG] 2025-07-19T18:12:27.003Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:28

[2955] [+308704ms] [LOG] 2025-07-19T18:12:28.002Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2956] [+308704ms] [LOG] 2025-07-19T18:12:28.002Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2957] [+308704ms] [LOG] 2025-07-19T18:12:28.002Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2958] [+308704ms] [LOG] 2025-07-19T18:12:28.002Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[2959] [+308704ms] [LOG] 2025-07-19T18:12:28.002Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2960] [+308704ms] [LOG] 2025-07-19T18:12:28.002Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2961] [+308704ms] [LOG] 2025-07-19T18:12:28.002Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:29

[2962] [+308704ms] [LOG] 2025-07-19T18:12:28.002Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:29

[2963] [+309706ms] [LOG] 2025-07-19T18:12:29.004Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2964] [+309706ms] [LOG] 2025-07-19T18:12:29.004Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2965] [+309706ms] [LOG] 2025-07-19T18:12:29.004Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2966] [+309707ms] [LOG] 2025-07-19T18:12:29.005Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 2ms

[2967] [+309707ms] [LOG] 2025-07-19T18:12:29.005Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2968] [+309707ms] [LOG] 2025-07-19T18:12:29.005Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2969] [+309707ms] [LOG] 2025-07-19T18:12:29.005Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:30

[2970] [+309707ms] [LOG] 2025-07-19T18:12:29.005Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:30

[2971] [+310815ms] [LOG] 2025-07-19T18:12:30.113Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2972] [+310815ms] [LOG] 2025-07-19T18:12:30.113Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2973] [+310815ms] [LOG] 2025-07-19T18:12:30.113Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 108ms

[2974] [+310815ms] [LOG] 2025-07-19T18:12:30.113Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 108ms

[2975] [+310816ms] [LOG] 2025-07-19T18:12:30.114Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2976] [+310816ms] [LOG] 2025-07-19T18:12:30.114Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2977] [+310816ms] [LOG] 2025-07-19T18:12:30.114Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:31

[2978] [+310816ms] [LOG] 2025-07-19T18:12:30.114Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:31

[2979] [+311906ms] [LOG] 2025-07-19T18:12:31.204Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2980] [+311906ms] [LOG] 2025-07-19T18:12:31.204Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2981] [+311906ms] [LOG] 2025-07-19T18:12:31.204Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[2982] [+311906ms] [LOG] 2025-07-19T18:12:31.204Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 92ms

[2983] [+311907ms] [LOG] 2025-07-19T18:12:31.205Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2984] [+311907ms] [LOG] 2025-07-19T18:12:31.205Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2985] [+311907ms] [LOG] 2025-07-19T18:12:31.205Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:32

[2986] [+311907ms] [LOG] 2025-07-19T18:12:31.205Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:32

[2987] [+312909ms] [LOG] 2025-07-19T18:12:32.207Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2988] [+312909ms] [LOG] 2025-07-19T18:12:32.207Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2989] [+312909ms] [LOG] 2025-07-19T18:12:32.207Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[2990] [+312909ms] [LOG] 2025-07-19T18:12:32.207Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[2991] [+312909ms] [LOG] 2025-07-19T18:12:32.207Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2992] [+312909ms] [LOG] 2025-07-19T18:12:32.207Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[2993] [+312909ms] [LOG] 2025-07-19T18:12:32.207Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:33

[2994] [+312909ms] [LOG] 2025-07-19T18:12:32.207Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:33

[2995] [+313914ms] [LOG] 2025-07-19T18:12:33.212Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2996] [+313914ms] [LOG] 2025-07-19T18:12:33.212Z
⏰ UnifiedTimerManager: 1 个定时器到期

[2997] [+313914ms] [LOG] 2025-07-19T18:12:33.212Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 5ms

[2998] [+313914ms] [LOG] 2025-07-19T18:12:33.212Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 5ms

[2999] [+313914ms] [LOG] 2025-07-19T18:12:33.212Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3000] [+313914ms] [LOG] 2025-07-19T18:12:33.212Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3001] [+313914ms] [LOG] 2025-07-19T18:12:33.212Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:34

[3002] [+313914ms] [LOG] 2025-07-19T18:12:33.212Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:34

[3003] [+315005ms] [LOG] 2025-07-19T18:12:34.303Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3004] [+315005ms] [LOG] 2025-07-19T18:12:34.303Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3005] [+315006ms] [LOG] 2025-07-19T18:12:34.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[3006] [+315006ms] [LOG] 2025-07-19T18:12:34.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 91ms

[3007] [+315006ms] [LOG] 2025-07-19T18:12:34.304Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3008] [+315006ms] [LOG] 2025-07-19T18:12:34.304Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3009] [+315006ms] [LOG] 2025-07-19T18:12:34.304Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:35

[3010] [+315006ms] [LOG] 2025-07-19T18:12:34.304Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:35

[3011] [+316006ms] [LOG] 2025-07-19T18:12:35.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3012] [+316006ms] [LOG] 2025-07-19T18:12:35.304Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3013] [+316006ms] [LOG] 2025-07-19T18:12:35.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[3014] [+316006ms] [LOG] 2025-07-19T18:12:35.304Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[3015] [+316006ms] [LOG] 2025-07-19T18:12:35.304Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3016] [+316007ms] [LOG] 2025-07-19T18:12:35.305Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3017] [+316007ms] [LOG] 2025-07-19T18:12:35.305Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:36

[3018] [+316007ms] [LOG] 2025-07-19T18:12:35.305Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:36

[3019] [+317007ms] [LOG] 2025-07-19T18:12:36.305Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3020] [+317007ms] [LOG] 2025-07-19T18:12:36.305Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3021] [+317007ms] [LOG] 2025-07-19T18:12:36.305Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[3022] [+317007ms] [LOG] 2025-07-19T18:12:36.305Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 1ms

[3023] [+317007ms] [LOG] 2025-07-19T18:12:36.305Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3024] [+317007ms] [LOG] 2025-07-19T18:12:36.305Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3025] [+317007ms] [LOG] 2025-07-19T18:12:36.305Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:37

[3026] [+317007ms] [LOG] 2025-07-19T18:12:36.305Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:37

[3027] [+318112ms] [LOG] 2025-07-19T18:12:37.410Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3028] [+318112ms] [LOG] 2025-07-19T18:12:37.410Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3029] [+318112ms] [LOG] 2025-07-19T18:12:37.410Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 104ms

[3030] [+318112ms] [LOG] 2025-07-19T18:12:37.410Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 104ms

[3031] [+318113ms] [LOG] 2025-07-19T18:12:37.411Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3032] [+318113ms] [LOG] 2025-07-19T18:12:37.411Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3033] [+318113ms] [LOG] 2025-07-19T18:12:37.411Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:38

[3034] [+318113ms] [LOG] 2025-07-19T18:12:37.411Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:38

[3035] [+319204ms] [LOG] 2025-07-19T18:12:38.502Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3036] [+319204ms] [LOG] 2025-07-19T18:12:38.502Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3037] [+319204ms] [LOG] 2025-07-19T18:12:38.502Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[3038] [+319204ms] [LOG] 2025-07-19T18:12:38.502Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 93ms

[3039] [+319205ms] [LOG] 2025-07-19T18:12:38.503Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3040] [+319205ms] [LOG] 2025-07-19T18:12:38.503Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3041] [+319205ms] [LOG] 2025-07-19T18:12:38.503Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:39

[3042] [+319205ms] [LOG] 2025-07-19T18:12:38.503Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:39

[3043] [+320204ms] [LOG] 2025-07-19T18:12:39.502Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3044] [+320204ms] [LOG] 2025-07-19T18:12:39.502Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3045] [+320205ms] [LOG] 2025-07-19T18:12:39.503Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[3046] [+320205ms] [LOG] 2025-07-19T18:12:39.503Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[3047] [+320205ms] [LOG] 2025-07-19T18:12:39.503Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3048] [+320205ms] [LOG] 2025-07-19T18:12:39.503Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3049] [+320205ms] [LOG] 2025-07-19T18:12:39.503Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:40

[3050] [+320205ms] [LOG] 2025-07-19T18:12:39.503Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:40

[3051] [+321207ms] [LOG] 2025-07-19T18:12:40.505Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3052] [+321207ms] [LOG] 2025-07-19T18:12:40.505Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3053] [+321207ms] [LOG] 2025-07-19T18:12:40.505Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[3054] [+321207ms] [LOG] 2025-07-19T18:12:40.505Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 3ms

[3055] [+321208ms] [LOG] 2025-07-19T18:12:40.506Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3056] [+321208ms] [LOG] 2025-07-19T18:12:40.506Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3057] [+321208ms] [LOG] 2025-07-19T18:12:40.506Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:41

[3058] [+321208ms] [LOG] 2025-07-19T18:12:40.506Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:41

[3059] [+322310ms] [LOG] 2025-07-19T18:12:41.608Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3060] [+322310ms] [LOG] 2025-07-19T18:12:41.608Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3061] [+322311ms] [LOG] 2025-07-19T18:12:41.609Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 103ms

[3062] [+322311ms] [LOG] 2025-07-19T18:12:41.609Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 103ms

[3063] [+322311ms] [LOG] 2025-07-19T18:12:41.609Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3064] [+322311ms] [LOG] 2025-07-19T18:12:41.609Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3065] [+322311ms] [LOG] 2025-07-19T18:12:41.609Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:42

[3066] [+322311ms] [LOG] 2025-07-19T18:12:41.609Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:42

[3067] [+323310ms] [LOG] 2025-07-19T18:12:42.608Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3068] [+323311ms] [LOG] 2025-07-19T18:12:42.609Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3069] [+323311ms] [LOG] 2025-07-19T18:12:42.609Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[3070] [+323311ms] [LOG] 2025-07-19T18:12:42.609Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[3071] [+323311ms] [LOG] 2025-07-19T18:12:42.609Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3072] [+323311ms] [LOG] 2025-07-19T18:12:42.609Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3073] [+323311ms] [LOG] 2025-07-19T18:12:42.609Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:43

[3074] [+323311ms] [LOG] 2025-07-19T18:12:42.609Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:43

[3075] [+324311ms] [LOG] 2025-07-19T18:12:43.609Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3076] [+324311ms] [LOG] 2025-07-19T18:12:43.609Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3077] [+324311ms] [LOG] 2025-07-19T18:12:43.609Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[3078] [+324311ms] [LOG] 2025-07-19T18:12:43.609Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 0ms

[3079] [+324311ms] [LOG] 2025-07-19T18:12:43.609Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3080] [+324311ms] [LOG] 2025-07-19T18:12:43.609Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3081] [+324311ms] [LOG] 2025-07-19T18:12:43.609Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:44

[3082] [+324311ms] [LOG] 2025-07-19T18:12:43.609Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:44

[3083] [+325409ms] [LOG] 2025-07-19T18:12:44.707Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3084] [+325409ms] [LOG] 2025-07-19T18:12:44.707Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3085] [+325409ms] [LOG] 2025-07-19T18:12:44.707Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[3086] [+325409ms] [LOG] 2025-07-19T18:12:44.707Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 99ms

[3087] [+325409ms] [LOG] 2025-07-19T18:12:44.707Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3088] [+325409ms] [LOG] 2025-07-19T18:12:44.707Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3089] [+325409ms] [LOG] 2025-07-19T18:12:44.707Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:45

[3090] [+325409ms] [LOG] 2025-07-19T18:12:44.707Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:45

[3091] [+326509ms] [LOG] 2025-07-19T18:12:45.807Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3092] [+326509ms] [LOG] 2025-07-19T18:12:45.807Z
⏰ UnifiedTimerManager: 1 个定时器到期

[3093] [+326509ms] [LOG] 2025-07-19T18:12:45.807Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[3094] [+326509ms] [LOG] 2025-07-19T18:12:45.807Z
🔥 UnifiedTimerManager: 执行定时器 [system_time_update], 延迟: 100ms

[3095] [+326509ms] [LOG] 2025-07-19T18:12:45.807Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3096] [+326509ms] [LOG] 2025-07-19T18:12:45.807Z
✅ UnifiedTimerManager: 定时器 [system_time_update] 执行成功

[3097] [+326509ms] [LOG] 2025-07-19T18:12:45.807Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:46

[3098] [+326509ms] [LOG] 2025-07-19T18:12:45.807Z
🔄 UnifiedTimerManager: 重复定时器 [system_time_update] 下次执行: 2025/7/20 02:12:46

