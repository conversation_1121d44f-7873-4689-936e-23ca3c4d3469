#ifndef MEMORY_MANAGER_H
#define MEMORY_MANAGER_H

/**
 * @file memory_manager.h
 * @brief 内存管理器 - ESP32-S3内存优化管理
 * @details 专门针对8MB SRAM的内存分配和监控
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/semphr.h>
#include <vector>
#include <map>

#include "config/config.h"
#include "config/constants.h"

/**
 * @brief 内存管理器 - 单例模式
 * @details 管理ESP32-S3的8MB SRAM，优化内存分配和使用
 */
class MemoryManager {
private:
    static MemoryManager* instance;
    
    // 内存池类型
    enum MemoryPoolType {
        SYSTEM_POOL = 0,        // 系统核心内存池
        NETWORK_POOL = 1,       // 网络缓存内存池
        IR_POOL = 2,            // 红外处理内存池
        STORAGE_POOL = 3,       // 存储缓存内存池
        TEMP_POOL = 4           // 临时内存池
    };
    
    // 内存块结构
    struct MemoryBlock {
        void* ptr;              // 内存指针
        size_t size;            // 块大小
        MemoryPoolType pool;    // 所属内存池
        String tag;             // 标识标签
        uint32_t allocTime;     // 分配时间
        bool inUse;             // 是否在使用
        
        MemoryBlock() : ptr(nullptr), size(0), pool(SYSTEM_POOL), 
                       allocTime(0), inUse(false) {}
    };
    
    // 内存池信息
    struct MemoryPool {
        String name;            // 池名称
        size_t totalSize;       // 总大小
        size_t usedSize;        // 已使用大小
        size_t maxBlockSize;    // 最大块大小
        uint32_t allocCount;    // 分配次数
        uint32_t freeCount;     // 释放次数
        std::vector<MemoryBlock*> blocks;  // 内存块列表
        
        MemoryPool() : totalSize(0), usedSize(0), maxBlockSize(0), 
                      allocCount(0), freeCount(0) {}
    };
    
    // 内存池映射
    std::map<MemoryPoolType, MemoryPool> memoryPools;
    
    // 内存统计
    struct MemoryStats {
        size_t totalHeap;       // 总堆内存
        size_t freeHeap;        // 可用堆内存
        size_t minFreeHeap;     // 最小可用堆内存
        size_t maxAllocHeap;    // 最大可分配堆内存
        size_t totalPSRAM;      // 总PSRAM (ESP32-S3中为0)
        size_t freePSRAM;       // 可用PSRAM (ESP32-S3中为0)
        uint32_t allocCount;    // 总分配次数
        uint32_t freeCount;     // 总释放次数
        uint32_t fragmentCount; // 碎片数量
        
        MemoryStats() : totalHeap(0), freeHeap(0), minFreeHeap(0), 
                       maxAllocHeap(0), totalPSRAM(0), freePSRAM(0),
                       allocCount(0), freeCount(0), fragmentCount(0) {}
    };
    
    MemoryStats currentStats;
    
    // 同步原语
    SemaphoreHandle_t memoryMutex;
    
    // 监控配置
    bool monitoringEnabled;
    uint32_t lastCheckTime;
    uint32_t checkInterval;
    
    // 警告阈值
    size_t lowMemoryThreshold;
    size_t criticalMemoryThreshold;
    
    // 私有构造函数
    MemoryManager();
    
    // 禁用拷贝构造和赋值
    MemoryManager(const MemoryManager&) = delete;
    MemoryManager& operator=(const MemoryManager&) = delete;

public:
    /**
     * @brief 获取内存管理器实例
     * @return MemoryManager& 内存管理器引用
     */
    static MemoryManager& getInstance();
    
    /**
     * @brief 析构函数
     */
    ~MemoryManager();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * @brief 初始化内存管理器
     * @return bool 初始化是否成功
     */
    bool begin();
    
    /**
     * @brief 内存管理器主循环
     */
    void loop();
    
    /**
     * @brief 停止内存管理器
     */
    void stop();
    
    // ==================== 内存分配管理 ====================
    
    /**
     * @brief 分配内存
     * @param size 内存大小
     * @param pool 内存池类型
     * @param tag 标识标签
     * @return void* 内存指针，失败返回nullptr
     */
    void* allocate(size_t size, MemoryPoolType pool = SYSTEM_POOL, const String& tag = "");
    
    /**
     * @brief 释放内存
     * @param ptr 内存指针
     * @return bool 释放是否成功
     */
    bool deallocate(void* ptr);
    
    /**
     * @brief 重新分配内存
     * @param ptr 原内存指针
     * @param newSize 新大小
     * @return void* 新内存指针
     */
    void* reallocate(void* ptr, size_t newSize);
    
    /**
     * @brief 分配对齐内存
     * @param size 内存大小
     * @param alignment 对齐字节数
     * @param pool 内存池类型
     * @return void* 内存指针
     */
    void* allocateAligned(size_t size, size_t alignment, MemoryPoolType pool = SYSTEM_POOL);
    
    // ==================== 专用内存分配 ====================
    
    /**
     * @brief 分配网络缓冲区
     * @param size 缓冲区大小
     * @param tag 标识标签
     * @return void* 缓冲区指针
     */
    void* allocateNetworkBuffer(size_t size, const String& tag = "NetworkBuffer");
    
    /**
     * @brief 分配红外数据缓冲区
     * @param size 缓冲区大小
     * @param tag 标识标签
     * @return void* 缓冲区指针
     */
    void* allocateIRBuffer(size_t size, const String& tag = "IRBuffer");
    
    /**
     * @brief 分配JSON文档内存
     * @param size 文档大小
     * @param tag 标识标签
     * @return void* 内存指针
     */
    void* allocateJsonBuffer(size_t size, const String& tag = "JsonBuffer");
    
    /**
     * @brief 分配临时内存
     * @param size 内存大小
     * @param tag 标识标签
     * @return void* 内存指针
     */
    void* allocateTemp(size_t size, const String& tag = "TempBuffer");
    
    // ==================== 内存池管理 ====================
    
    /**
     * @brief 初始化内存池
     * @param pool 内存池类型
     * @param totalSize 总大小
     * @param maxBlockSize 最大块大小
     * @return bool 初始化是否成功
     */
    bool initializePool(MemoryPoolType pool, size_t totalSize, size_t maxBlockSize);
    
    /**
     * @brief 清理内存池
     * @param pool 内存池类型
     * @return bool 清理是否成功
     */
    bool cleanupPool(MemoryPoolType pool);
    
    /**
     * @brief 获取内存池信息
     * @param pool 内存池类型
     * @return MemoryPool 内存池信息
     */
    MemoryPool getPoolInfo(MemoryPoolType pool) const;
    
    /**
     * @brief 压缩内存池
     * @param pool 内存池类型
     * @return size_t 回收的内存大小
     */
    size_t compactPool(MemoryPoolType pool);
    
    // ==================== 内存监控 ====================
    
    /**
     * @brief 获取内存统计信息
     * @return MemoryStats 内存统计
     */
    MemoryStats getMemoryStats() const;
    
    /**
     * @brief 获取堆内存信息
     * @return JsonDocument 堆内存信息
     */
    JsonDocument getHeapInfo() const;
    
    /**
     * @brief 检查内存健康状态
     * @return bool 内存状态是否健康
     */
    bool checkMemoryHealth() const;
    
    /**
     * @brief 获取内存使用率
     * @return float 内存使用率(%)
     */
    float getMemoryUsage() const;
    
    /**
     * @brief 获取最大可分配内存
     * @return size_t 最大可分配内存大小
     */
    size_t getMaxAllocatableSize() const;
    
    // ==================== 内存优化 ====================
    
    /**
     * @brief 执行垃圾回收
     * @return size_t 回收的内存大小
     */
    size_t garbageCollect();
    
    /**
     * @brief 内存碎片整理
     * @return size_t 整理后的可用内存
     */
    size_t defragment();
    
    /**
     * @brief 清理临时内存
     * @param maxAge 最大存活时间(ms)
     * @return size_t 清理的内存大小
     */
    size_t cleanupTempMemory(uint32_t maxAge = 60000);
    
    /**
     * @brief 紧急内存清理
     * @return size_t 清理的内存大小
     */
    size_t emergencyCleanup();
    
    // ==================== 配置管理 ====================
    
    /**
     * @brief 启用内存监控
     * @param interval 检查间隔(ms)
     */
    void enableMonitoring(uint32_t interval = 5000);
    
    /**
     * @brief 禁用内存监控
     */
    void disableMonitoring();
    
    /**
     * @brief 设置内存警告阈值
     * @param lowThreshold 低内存阈值
     * @param criticalThreshold 关键内存阈值
     */
    void setMemoryThresholds(size_t lowThreshold, size_t criticalThreshold);
    
    // ==================== 调试和诊断 ====================
    
    /**
     * @brief 打印内存使用报告
     */
    void printMemoryReport() const;
    
    /**
     * @brief 获取内存泄漏报告
     * @return JsonDocument 内存泄漏信息
     */
    JsonDocument getMemoryLeakReport() const;
    
    /**
     * @brief 验证内存完整性
     * @return bool 内存完整性是否正常
     */
    bool validateMemoryIntegrity() const;

private:
    // ==================== 私有方法 ====================
    
    /**
     * @brief 更新内存统计
     */
    void updateMemoryStats();
    
    /**
     * @brief 检查内存警告
     */
    void checkMemoryWarnings();
    
    /**
     * @brief 查找内存块
     * @param ptr 内存指针
     * @return MemoryBlock* 内存块指针
     */
    MemoryBlock* findMemoryBlock(void* ptr);
    
    /**
     * @brief 获取内存池名称
     * @param pool 内存池类型
     * @return String 内存池名称
     */
    String getPoolName(MemoryPoolType pool) const;
    
    /**
     * @brief 内存对齐
     * @param size 原大小
     * @param alignment 对齐字节数
     * @return size_t 对齐后的大小
     */
    size_t alignSize(size_t size, size_t alignment) const;
};

#endif // MEMORY_MANAGER_H
