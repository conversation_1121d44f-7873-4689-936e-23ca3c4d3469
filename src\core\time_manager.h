#pragma once

#include <Arduino.h>
#include <WiFi.h>
#include <time.h>
#include <sys/time.h>
#include "config/config.h"

/**
 * @brief 时间管理器 - 处理NTP同步和时间戳生成
 */
class TimeManager {
private:
    static TimeManager* instance;
    
    bool isInitialized;
    bool isNTPSynced;
    uint32_t lastSyncAttempt;
    uint32_t syncInterval;
    String timezone;
    String ntpServer1;
    String ntpServer2;
    String ntpServer3;
    
    // 时间同步状态
    struct tm timeinfo;
    time_t lastSyncTime;
    uint32_t bootTimeOffset;  // 启动时间偏移量
    
public:
    TimeManager();
    ~TimeManager();
    
    // 单例模式
    static TimeManager* getInstance();
    
    // 初始化和配置
    bool begin();
    void setTimezone(const String& tz);
    void setNTPServers(const String& server1, const String& server2 = "", const String& server3 = "");
    
    // NTP同步
    bool syncWithNTP();
    void loop();  // 定期检查和同步
    
    // 时间获取
    uint64_t getUnixTimestamp();      // 获取Unix时间戳（秒）
    uint64_t getUnixTimestampMs();    // 获取Unix时间戳（毫秒）
    String getFormattedTime(const String& format = "%Y-%m-%d %H:%M:%S");
    String getISO8601Time();
    
    // 状态查询
    bool isTimeSynced() const { return isNTPSynced; }
    time_t getLastSyncTime() const { return lastSyncTime; }
    uint32_t getTimeSinceSync() const;
    
    // 时间转换工具
    static uint64_t millisToUnixMs(uint32_t millis_time);
    static String formatUnixTime(uint64_t unix_ms, const String& format = "%Y-%m-%d %H:%M:%S");
    
private:
    void updateBootTimeOffset();
    bool waitForTimeSync(uint32_t timeout_ms = 10000);
};

// 全局时间管理器实例
extern TimeManager* timeManager;

// 便捷函数
uint64_t getCurrentUnixMs();
String getCurrentTimeString();
