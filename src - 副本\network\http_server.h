#ifndef HTTP_SERVER_H
#define HTTP_SERVER_H

/**
 * @file http_server.h
 * @brief HTTP API服务器 - 与前端完全兼容的REST API
 * @details 实现所有前端期望的API接口，确保100%兼容
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include <LittleFS.h>

#include "config/config.h"
#include "config/constants.h"
#include "storage/data_structures.h"

// 前向声明
class SystemManager;
class IRSignalManager;
class SignalStorage;
class TaskScheduler;
class TimerManagerBackend;

/**
 * @brief HTTP API服务器类
 * @details 提供与前端完全兼容的REST API接口
 */
class HTTPAPIServer {
private:
    AsyncWebServer server;
    
    // 组件引用
    SystemManager* systemManager;
    IRSignalManager* irManager;
    SignalStorage* signalStorage;
    TaskScheduler* taskScheduler;
    
    // 服务器状态
    bool isRunning;
    uint32_t requestCount;
    uint32_t errorCount;
    uint32_t startTime;
    
    // 速率限制
    struct RateLimitInfo {
        uint32_t requestCount;
        uint32_t lastResetTime;
        IPAddress clientIP;
    };
    std::vector<RateLimitInfo> rateLimitMap;

public:
    /**
     * @brief 构造函数
     */
    HTTPAPIServer();
    
    /**
     * @brief 析构函数
     */
    ~HTTPAPIServer();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * @brief 初始化HTTP服务器
     * @return bool 初始化是否成功
     */
    bool begin();
    
    /**
     * @brief HTTP服务器主循环
     */
    void loop();
    
    /**
     * @brief 停止HTTP服务器
     */
    void stop();
    
    // ==================== 服务器状态 ====================
    
    /**
     * @brief 检查服务器是否运行
     * @return bool 是否运行
     */
    bool isServerRunning() const { return isRunning; }
    
    /**
     * @brief 获取服务器统计信息
     * @return JsonDocument 统计信息
     */
    JsonDocument getServerStats() const;

private:
    // ==================== 路由设置 ====================
    
    /**
     * @brief 设置所有API路由
     */
    void setupRoutes();
    
    /**
     * @brief 设置CORS头
     */
    void setupCORS();
    
    /**
     * @brief 设置静态文件服务
     */
    void setupStaticFiles();
    
    // ==================== API处理器 - 系统管理 ====================
    
    /**
     * @brief 处理系统状态查询 - GET /api/status
     * @param request HTTP请求
     */
    static void handleGetStatus(AsyncWebServerRequest *request);

    /**
     * @brief 处理系统统计信息查询 - GET /api/system/stats
     * @param request HTTP请求
     */
    static void handleGetSystemStats(AsyncWebServerRequest *request);

    /**
     * @brief 处理内存统计信息查询 - GET /api/system/memory
     * @param request HTTP请求
     */
    static void handleGetMemoryStats(AsyncWebServerRequest *request);

    /**
     * @brief 处理性能统计信息查询 - GET /api/system/performance
     * @param request HTTP请求
     */
    static void handleGetPerformanceStats(AsyncWebServerRequest *request);

    /**
     * @brief 处理磁盘使用率查询 - GET /api/system/disk
     * @param request HTTP请求
     */
    static void handleGetDiskStats(AsyncWebServerRequest *request);

    /**
     * @brief 处理任务状态查询 - GET /api/system/tasks
     * @param request HTTP请求
     */
    static void handleGetTaskStatus(AsyncWebServerRequest *request);

    // ==================== API处理器 - 信号分组管理 ====================

    /**
     * @brief 处理获取信号分组列表 - GET /api/groups
     * @param request HTTP请求
     */
    static void handleGetSignalGroups(AsyncWebServerRequest *request);

    /**
     * @brief 处理创建信号分组 - POST /api/groups
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleCreateSignalGroup(AsyncWebServerRequest *request, uint8_t *data,
                                       size_t len, size_t index, size_t total);

    /**
     * @brief 处理更新信号分组 - PUT /api/groups/{id}
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleUpdateSignalGroup(AsyncWebServerRequest *request, uint8_t *data,
                                       size_t len, size_t index, size_t total);

    /**
     * @brief 处理删除信号分组 - DELETE /api/groups/{id}
     * @param request HTTP请求
     */
    static void handleDeleteSignalGroup(AsyncWebServerRequest *request);

    // ==================== API处理器 - 用户配置管理 ====================

    /**
     * @brief 处理获取用户配置 - GET /api/config
     * @param request HTTP请求
     */
    static void handleGetUserConfig(AsyncWebServerRequest *request);

    /**
     * @brief 处理更新用户配置 - PUT /api/config
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleUpdateUserConfig(AsyncWebServerRequest *request, uint8_t *data,
                                      size_t len, size_t index, size_t total);

    /**
     * @brief 处理重置用户配置 - POST /api/config/reset
     * @param request HTTP请求
     */
    static void handleResetUserConfig(AsyncWebServerRequest *request);

    // ==================== API处理器 - 系统备份恢复 ====================

    /**
     * @brief 处理导出系统备份 - GET /api/backup
     * @param request HTTP请求
     */
    static void handleExportBackup(AsyncWebServerRequest *request);

    /**
     * @brief 处理导入系统备份 - POST /api/restore
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleImportBackup(AsyncWebServerRequest *request, uint8_t *data,
                                  size_t len, size_t index, size_t total);

    // ==================== API处理器 - 固件更新 ====================

    /**
     * @brief 处理获取固件信息 - GET /api/firmware/info
     * @param request HTTP请求
     */
    static void handleGetFirmwareInfo(AsyncWebServerRequest *request);

    /**
     * @brief 处理固件上传 - POST /api/firmware/update
     * @param request HTTP请求
     * @param filename 文件名
     * @param index 文件索引
     * @param data 文件数据
     * @param len 数据长度
     * @param final 是否为最后一块
     */
    static void handleFirmwareUpload(AsyncWebServerRequest *request, const String& filename,
                                    size_t index, uint8_t *data, size_t len, bool final);

    /**
     * @brief 处理检查固件更新 - POST /api/firmware/check
     * @param request HTTP请求
     */
    static void handleCheckFirmwareUpdate(AsyncWebServerRequest *request);

    /**
     * @brief 处理系统重启 - POST /api/system/restart
     * @param request HTTP请求
     */
    static void handleSystemRestart(AsyncWebServerRequest *request);

    // ==================== API处理器 - 错误日志管理 ====================

    /**
     * @brief 处理获取系统日志 - GET /api/logs
     * @param request HTTP请求
     */
    static void handleGetSystemLogs(AsyncWebServerRequest *request);

    /**
     * @brief 处理清空系统日志 - DELETE /api/logs
     * @param request HTTP请求
     */
    static void handleClearSystemLogs(AsyncWebServerRequest *request);

    /**
     * @brief 处理下载日志文件 - GET /api/logs/download
     * @param request HTTP请求
     */
    static void handleDownloadLogs(AsyncWebServerRequest *request);

    // ==================== API处理器 - 信号管理 ====================
    
    /**
     * @brief 处理获取信号列表 - GET /api/signals
     * @param request HTTP请求
     */
    static void handleGetSignals(AsyncWebServerRequest *request);
    
    /**
     * @brief 处理创建信号 - POST /api/signals
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleCreateSignal(AsyncWebServerRequest *request, uint8_t *data, 
                                  size_t len, size_t index, size_t total);
    
    /**
     * @brief 处理更新信号 - PUT /api/signals/{id}
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleUpdateSignal(AsyncWebServerRequest *request, uint8_t *data, 
                                  size_t len, size_t index, size_t total);
    
    /**
     * @brief 处理删除信号 - DELETE /api/signals/{id}
     * @param request HTTP请求
     */
    static void handleDeleteSignal(AsyncWebServerRequest *request);
    
    /**
     * @brief 处理清空信号 - POST /api/signals/clear
     * @param request HTTP请求
     */
    static void handleClearSignals(AsyncWebServerRequest *request);
    
    // ==================== API处理器 - 信号学习 ====================
    
    /**
     * @brief 处理信号学习控制 - POST /api/learning
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleLearningControl(AsyncWebServerRequest *request, uint8_t *data,
                                     size_t len, size_t index, size_t total);

    /**
     * @brief 处理信号检测 - POST /api/learning/detect
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleLearningDetect(AsyncWebServerRequest *request, uint8_t *data,
                                    size_t len, size_t index, size_t total);

    // ==================== API处理器 - 定时器管理 ====================

    /**
     * @brief 处理获取定时任务列表 - GET /api/timer/tasks
     * @param request HTTP请求
     */
    static void handleGetTimerTasks(AsyncWebServerRequest *request);

    /**
     * @brief 处理创建定时任务 - POST /api/timer/tasks
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleCreateTimerTask(AsyncWebServerRequest *request, uint8_t *data,
                                     size_t len, size_t index, size_t total);

    /**
     * @brief 处理更新定时任务 - PUT /api/timer/tasks/{id}
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleUpdateTimerTask(AsyncWebServerRequest *request, uint8_t *data,
                                     size_t len, size_t index, size_t total);

    /**
     * @brief 处理删除定时任务 - DELETE /api/timer/tasks/{id}
     * @param request HTTP请求
     */
    static void handleDeleteTimerTask(AsyncWebServerRequest *request);

    /**
     * @brief 处理定时器系统启用/禁用 - POST /api/timer/enable
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleTimerEnable(AsyncWebServerRequest *request, uint8_t *data,
                                 size_t len, size_t index, size_t total);

    /**
     * @brief 处理获取定时器状态 - GET /api/timer/status
     * @param request HTTP请求
     */
    static void handleGetTimerStatus(AsyncWebServerRequest *request);

    /**
     * @brief 处理执行定时任务 - POST /api/timer/tasks/{id}/execute
     * @param request HTTP请求
     */
    static void handleExecuteTimerTask(AsyncWebServerRequest *request);

    /**
     * @brief 处理任务启用/禁用 - POST /api/timer/tasks/{id}/enable
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleTimerTaskEnable(AsyncWebServerRequest *request, uint8_t *data,
                                     size_t len, size_t index, size_t total);

    // ==================== API处理器 - 信号发射 ====================
    
    /**
     * @brief 处理信号发射 - POST /api/emit/signal
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleEmitSignal(AsyncWebServerRequest *request, uint8_t *data, 
                                size_t len, size_t index, size_t total);
    
    // ==================== API处理器 - 批量操作 ====================
    
    /**
     * @brief 处理批量操作 - POST /api/batch
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleBatchOperation(AsyncWebServerRequest *request, uint8_t *data, 
                                    size_t len, size_t index, size_t total);
    
    // ==================== 工具方法 ====================
    
    /**
     * @brief 发送成功响应
     * @param request HTTP请求
     * @param data 响应数据
     * @param message 消息
     */
    static void sendSuccessResponse(AsyncWebServerRequest *request,
                                   const JsonDocument& data,
                                   const String& message = "",
                                   uint32_t requestStartTime = millis());
    
    /**
     * @brief 发送错误响应
     * @param request HTTP请求
     * @param error 错误信息
     * @param code HTTP状态码
     * @param message 消息
     */
    static void sendErrorResponse(AsyncWebServerRequest *request, 
                                 const String& error, 
                                 int code = 400,
                                 const String& message = "");
    
    /**
     * @brief 解析JSON请求体
     * @param data 请求数据
     * @param len 数据长度
     * @param doc JSON文档
     * @return bool 解析是否成功
     */
    static bool parseJsonBody(uint8_t *data, size_t len, JsonDocument& doc);
    
    /**
     * @brief 验证请求参数
     * @param request HTTP请求
     * @param requiredParams 必需参数列表
     * @return bool 验证是否通过
     */
    static bool validateRequest(AsyncWebServerRequest *request, 
                               const std::vector<String>& requiredParams);
    
    /**
     * @brief 检查速率限制
     * @param request HTTP请求
     * @return bool 是否超出限制
     */
    bool checkRateLimit(AsyncWebServerRequest *request);
    
    /**
     * @brief 记录请求日志
     * @param request HTTP请求
     * @param responseCode 响应代码
     * @param processingTime 处理时间
     */
    static void logRequest(AsyncWebServerRequest *request, int responseCode, uint32_t processingTime);
    
    /**
     * @brief 获取客户端IP
     * @param request HTTP请求
     * @return IPAddress 客户端IP
     */
    static IPAddress getClientIP(AsyncWebServerRequest *request);
    
    /**
     * @brief 处理OPTIONS请求 (CORS预检)
     * @param request HTTP请求
     */
    static void handleOptions(AsyncWebServerRequest *request);
    
    /**
     * @brief 处理404错误
     * @param request HTTP请求
     */
    static void handleNotFound(AsyncWebServerRequest *request);
    
    /**
     * @brief 中间件：请求预处理
     * @param request HTTP请求
     * @return bool 是否继续处理
     */
    static bool requestMiddleware(AsyncWebServerRequest *request);
    
    /**
     * @brief 获取系统管理器实例
     * @return SystemManager* 系统管理器指针
     */
    static SystemManager* getSystemManager();
    
    /**
     * @brief 获取红外管理器实例
     * @return IRSignalManager* 红外管理器指针
     */
    static IRSignalManager* getIRManager();
    
    /**
     * @brief 获取信号存储实例
     * @return SignalStorage* 信号存储指针
     */
    static SignalStorage* getSignalStorage();
    
    /**
     * @brief 获取任务调度器实例
     * @return TaskScheduler* 任务调度器指针
     */
    static TaskScheduler* getTaskScheduler();

    /**
     * @brief 获取定时器管理器实例
     * @return TimerManagerBackend* 定时器管理器指针
     */
    static TimerManagerBackend* getTimerManagerBackend();
};

#endif // HTTP_SERVER_H
