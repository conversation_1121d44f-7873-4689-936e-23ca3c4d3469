#!/bin/bash

echo "========================================"
echo "ESP32前端文件上传脚本"
echo "========================================"
echo

echo "📁 检查data文件夹..."
if [ ! -d "data" ]; then
    echo "❌ 错误：data文件夹不存在"
    exit 1
fi

echo "✅ data文件夹存在"

echo
echo "📋 文件列表："
find data -type f -name "*.*" | sort

echo
echo "🚀 开始上传前端文件到ESP32..."
echo

pio run --target uploadfs

if [ $? -eq 0 ]; then
    echo
    echo "✅ 前端文件上传成功！"
    echo
    echo "📡 访问地址："
    echo "   主页面: http://192.168.4.1:8000/"
    echo "   测试页面: http://192.168.4.1:8000/test.html"
    echo
    echo "📝 使用说明："
    echo "   1. 连接WiFi: ESP32-IR-Control"
    echo "   2. 密码: 12345678"
    echo "   3. 打开浏览器访问上述地址"
    echo
else
    echo
    echo "❌ 上传失败！"
    echo
    echo "🔧 故障排除："
    echo "   1. 检查ESP32是否连接"
    echo "   2. 检查PlatformIO是否正确安装"
    echo "   3. 尝试重新连接ESP32"
    echo
fi

read -p "按Enter键继续..."
