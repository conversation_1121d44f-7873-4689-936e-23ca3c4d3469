#ifndef STRING_UTILS_H
#define STRING_UTILS_H

/**
 * @file string_utils.h
 * @brief 字符串工具函数
 * @details 提供常用的字符串处理功能
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <vector>

namespace StringUtils {
    
    /**
     * @brief 分割字符串
     * @param str 要分割的字符串
     * @param delimiter 分隔符
     * @return 分割后的字符串数组
     */
    std::vector<String> split(const String& str, char delimiter);
    
    /**
     * @brief 去除字符串首尾空白字符
     * @param str 要处理的字符串
     * @return 处理后的字符串
     */
    String trim(const String& str);
    
    /**
     * @brief 转换为小写
     * @param str 要转换的字符串
     * @return 小写字符串
     */
    String toLowerCase(const String& str);
    
    /**
     * @brief 转换为大写
     * @param str 要转换的字符串
     * @return 大写字符串
     */
    String toUpperCase(const String& str);
    
    /**
     * @brief 检查字符串是否为空或只包含空白字符
     * @param str 要检查的字符串
     * @return 如果为空或只包含空白字符返回true
     */
    bool isBlank(const String& str);
    
    /**
     * @brief 替换字符串中的所有匹配项
     * @param str 原字符串
     * @param from 要替换的子字符串
     * @param to 替换为的字符串
     * @return 替换后的字符串
     */
    String replace(const String& str, const String& from, const String& to);
    
    /**
     * @brief 格式化字符串
     * @param format 格式字符串
     * @param ... 参数
     * @return 格式化后的字符串
     */
    String format(const char* format, ...);
    
    /**
     * @brief 生成随机字符串
     * @param length 字符串长度
     * @param includeNumbers 是否包含数字
     * @param includeSymbols 是否包含符号
     * @return 随机字符串
     */
    String generateRandom(int length, bool includeNumbers = true, bool includeSymbols = false);
    
    /**
     * @brief 计算字符串的哈希值
     * @param str 要计算哈希的字符串
     * @return 哈希值
     */
    uint32_t hash(const String& str);
    
    /**
     * @brief 检查字符串是否为有效的JSON
     * @param str 要检查的字符串
     * @return 如果是有效JSON返回true
     */
    bool isValidJson(const String& str);
    
    /**
     * @brief 转义JSON字符串
     * @param str 要转义的字符串
     * @return 转义后的字符串
     */
    String escapeJson(const String& str);
    
    /**
     * @brief 反转义JSON字符串
     * @param str 要反转义的字符串
     * @return 反转义后的字符串
     */
    String unescapeJson(const String& str);
}

#endif // STRING_UTILS_H
