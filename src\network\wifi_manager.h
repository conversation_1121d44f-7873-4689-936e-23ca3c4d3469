#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

/**
 * @file wifi_manager.h
 * @brief WiFi连接管理器
 * @details 管理WiFi连接、AP模式、网络配置等
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <WiFi.h>
#include <WiFiMulti.h>
#include <WiFiAP.h>
#include <ArduinoJson.h>
#include <LittleFS.h>

#include "config/config.h"
#include "config/constants.h"
#include "storage/data_structures.h"

/**
 * @brief WiFi管理器类
 * @details 处理WiFi连接、配置管理、网络状态监控
 */
class WiFiManager {
private:
    // WiFi状态枚举
    enum WiFiState {
        DISCONNECTED = 0,
        CONNECTING = 1,
        CONNECTED = 2,
        AP_MODE = 3,
        ERROR = 4
    };
    
    // WiFi模式枚举
    enum WiFiMode {
        STA_MODE = 0,           // 客户端模式
        AP_MODE_ONLY = 1,       // 仅AP模式
        AP_STA_MODE = 2         // AP+STA模式
    };
    
    // WiFi网络信息结构
    struct WiFiNetwork {
        String ssid;
        String password;
        int32_t rssi;
        uint8_t encryptionType;
        bool isConfigured;
        uint32_t lastConnected;
        uint32_t connectAttempts;
        
        WiFiNetwork() : rssi(0), encryptionType(0), isConfigured(false), 
                       lastConnected(0), connectAttempts(0) {}
    };
    
    WiFiMulti wifiMulti;
    WiFiState currentState;
    WiFiMode currentMode;
    
    // 网络配置
    std::vector<WiFiNetwork> configuredNetworks;
    WiFiNetwork currentNetwork;
    
    // AP模式配置
    String apSSID;
    String apPassword;
    IPAddress apIP;
    IPAddress apGateway;
    IPAddress apSubnet;
    uint8_t apChannel;
    uint8_t maxClients;
    
    // 连接管理
    uint32_t lastConnectionAttempt;
    uint32_t connectionTimeout;
    uint32_t reconnectInterval;
    uint8_t maxReconnectAttempts;
    uint8_t currentReconnectAttempts;
    
    // 状态监控
    uint32_t lastStatusCheck;
    uint32_t statusCheckInterval;
    int32_t lastRSSI;
    bool autoReconnect;
    
    // 事件回调
    std::function<void(WiFiState)> stateChangeCallback;
    std::function<void(const String&)> errorCallback;

public:
    /**
     * @brief 构造函数
     */
    WiFiManager();
    
    /**
     * @brief 析构函数
     */
    ~WiFiManager();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * @brief 初始化WiFi管理器
     * @return bool 初始化是否成功
     */
    bool begin();
    
    /**
     * @brief WiFi管理器主循环
     */
    void loop();
    
    /**
     * @brief 停止WiFi管理器
     */
    void stop();
    
    // ==================== 连接管理 ====================
    
    /**
     * @brief 连接到WiFi网络
     * @param ssid 网络名称
     * @param password 密码
     * @param timeout 超时时间(ms)
     * @return bool 连接是否成功
     */
    bool connect(const String& ssid, const String& password, uint32_t timeout = WIFI_CONNECT_TIMEOUT);
    
    /**
     * @brief 断开WiFi连接
     */
    void disconnect();
    
    /**
     * @brief 重新连接WiFi
     * @return bool 重连是否成功
     */
    bool reconnect();
    
    /**
     * @brief 启动AP模式
     * @param ssid AP名称
     * @param password AP密码
     * @param channel 信道
     * @return bool 启动是否成功
     */
    bool startAP(const String& ssid = WIFI_SSID, const String& password = WIFI_PASSWORD, 
                 uint8_t channel = WIFI_CHANNEL);
    
    /**
     * @brief 停止AP模式
     */
    void stopAP();
    
    /**
     * @brief 启动AP+STA模式
     * @return bool 启动是否成功
     */
    bool startAPSTA();
    
    // ==================== 网络配置管理 ====================
    
    /**
     * @brief 添加WiFi网络配置
     * @param ssid 网络名称
     * @param password 密码
     * @return bool 添加是否成功
     */
    bool addNetwork(const String& ssid, const String& password);
    
    /**
     * @brief 移除WiFi网络配置
     * @param ssid 网络名称
     * @return bool 移除是否成功
     */
    bool removeNetwork(const String& ssid);
    
    /**
     * @brief 清空所有网络配置
     */
    void clearNetworks();
    
    /**
     * @brief 保存网络配置到文件
     * @return bool 保存是否成功
     */
    bool saveNetworkConfig();
    
    /**
     * @brief 从文件加载网络配置
     * @return bool 加载是否成功
     */
    bool loadNetworkConfig();
    
    /**
     * @brief 扫描可用WiFi网络
     * @return std::vector<WiFiNetwork> 网络列表
     */
    std::vector<WiFiNetwork> scanNetworks();
    
    // ==================== 状态查询 ====================
    
    /**
     * @brief 检查是否已连接
     * @return bool 是否已连接
     */
    bool isConnected() const { return currentState == CONNECTED; }
    
    /**
     * @brief 检查是否在AP模式
     * @return bool 是否在AP模式
     */
    bool isAPMode() const { return currentMode == AP_MODE_ONLY || currentMode == AP_STA_MODE; }
    
    /**
     * @brief 获取当前WiFi状态
     * @return WiFiState 当前状态
     */
    WiFiState getState() const { return currentState; }
    
    /**
     * @brief 获取当前WiFi模式
     * @return WiFiMode 当前模式
     */
    WiFiMode getMode() const { return currentMode; }
    
    /**
     * @brief 获取本地IP地址
     * @return IPAddress IP地址
     */
    IPAddress getLocalIP() const;
    
    /**
     * @brief 获取AP IP地址
     * @return IPAddress AP IP地址
     */
    IPAddress getAPIP() const { return apIP; }
    
    /**
     * @brief 获取MAC地址
     * @return String MAC地址
     */
    String getMACAddress() const;
    
    /**
     * @brief 获取当前SSID
     * @return String 当前连接的SSID
     */
    String getCurrentSSID() const;
    
    /**
     * @brief 获取信号强度
     * @return int32_t RSSI值
     */
    int32_t getRSSI() const;
    
    /**
     * @brief 获取连接的客户端数量 (AP模式)
     * @return uint8_t 客户端数量
     */
    uint8_t getConnectedClients() const;
    
    // ==================== 网络信息 ====================
    
    /**
     * @brief 获取网络状态信息
     * @return JsonDocument 网络状态
     */
    JsonDocument getNetworkStatus() const;
    
    /**
     * @brief 获取网络配置信息
     * @return JsonDocument 网络配置
     */
    JsonDocument getNetworkConfig() const;
    
    /**
     * @brief 获取网络统计信息
     * @return JsonDocument 网络统计
     */
    JsonDocument getNetworkStats() const;
    
    // ==================== 事件处理 ====================
    
    /**
     * @brief 设置状态变化回调
     * @param callback 回调函数
     */
    void setStateChangeCallback(std::function<void(WiFiState)> callback) {
        stateChangeCallback = callback;
    }
    
    /**
     * @brief 设置错误回调
     * @param callback 回调函数
     */
    void setErrorCallback(std::function<void(const String&)> callback) {
        errorCallback = callback;
    }
    
    // ==================== 配置管理 ====================
    
    /**
     * @brief 设置自动重连
     * @param enable 是否启用
     * @param interval 重连间隔(ms)
     * @param maxAttempts 最大重连次数
     */
    void setAutoReconnect(bool enable, uint32_t interval = 30000, uint8_t maxAttempts = 5);
    
    /**
     * @brief 设置连接超时
     * @param timeout 超时时间(ms)
     */
    void setConnectionTimeout(uint32_t timeout) { connectionTimeout = timeout; }
    
    /**
     * @brief 设置状态检查间隔
     * @param interval 检查间隔(ms)
     */
    void setStatusCheckInterval(uint32_t interval) { statusCheckInterval = interval; }

private:
    // ==================== 私有方法 ====================
    
    /**
     * @brief 状态变化处理
     * @param newState 新状态
     */
    void handleStateChange(WiFiState newState);
    
    /**
     * @brief 连接状态检查
     */
    void checkConnectionStatus();
    
    /**
     * @brief 自动重连处理
     */
    void handleAutoReconnect();
    
    /**
     * @brief 查找网络配置
     * @param ssid 网络名称
     * @return WiFiNetwork* 网络配置指针
     */
    WiFiNetwork* findNetwork(const String& ssid);
    
    /**
     * @brief 获取WiFi状态字符串
     * @param state 状态
     * @return String 状态字符串
     */
    String getStateString(WiFiState state) const;
    
    /**
     * @brief 获取WiFi模式字符串
     * @param mode 模式
     * @return String 模式字符串
     */
    String getModeString(WiFiMode mode) const;
    
    /**
     * @brief 获取加密类型字符串
     * @param encType 加密类型
     * @return String 加密类型字符串
     */
    String getEncryptionTypeString(uint8_t encType) const;
    
    /**
     * @brief WiFi事件处理
     * @param event WiFi事件
     */
    static void onWiFiEvent(WiFiEvent_t event);
};

#endif // WIFI_MANAGER_H
