#include "timer_data.h"
#include "../core/time_manager.h"
#include <time.h>

void TimerTask::calculateNextExecutionTime() {
    if (!isEnabled) {
        nextExecutionTime = 0;
        return;
    }

    // 获取当前时间
    uint64_t currentTime = getCurrentUnixMs();
    time_t currentTimestamp = currentTime / 1000;
    struct tm* currentTm = localtime(&currentTimestamp);
    
    // 解析开始时间
    int startHour = 0, startMinute = 0;
    if (sscanf(startTime.c_str(), "%d:%d", &startHour, &startMinute) != 2) {
        Serial.printf("❌ TimerTask: 无效的开始时间格式: %s\n", startTime.c_str());
        nextExecutionTime = 0;
        return;
    }

    // 解析结束时间
    int endHour = 0, endMinute = 0;
    if (sscanf(endTime.c_str(), "%d:%d", &endHour, &endMinute) != 2) {
        Serial.printf("❌ TimerTask: 无效的结束时间格式: %s\n", endTime.c_str());
        nextExecutionTime = 0;
        return;
    }

    // 创建今天的开始时间
    struct tm nextTm = *currentTm;
    nextTm.tm_hour = startHour;
    nextTm.tm_min = startMinute;
    nextTm.tm_sec = 0;
    
    time_t nextTimestamp = mktime(&nextTm);
    uint64_t nextTimeMs = (uint64_t)nextTimestamp * 1000;

    // 如果是第一次执行或者时间已过
    if (lastExecutionTime == 0 || nextTimeMs <= currentTime) {
        if (isDaily) {
            // 每日重复：设置为明天的开始时间
            nextTm.tm_mday += 1;
            nextTimestamp = mktime(&nextTm);
            nextTimeMs = (uint64_t)nextTimestamp * 1000;
        } else {
            // 不重复：基于上次执行时间计算
            if (lastExecutionTime > 0) {
                nextTimeMs = lastExecutionTime + (intervalMinutes * 60 * 1000);
            } else {
                // 第一次执行：如果当前时间在执行窗口内，立即执行
                int currentHour = currentTm->tm_hour;
                int currentMinute = currentTm->tm_min;
                int currentTotalMinutes = currentHour * 60 + currentMinute;
                int startTotalMinutes = startHour * 60 + startMinute;
                int endTotalMinutes = endHour * 60 + endMinute;
                
                if (currentTotalMinutes >= startTotalMinutes && currentTotalMinutes <= endTotalMinutes) {
                    nextTimeMs = currentTime + 60000; // 1分钟后执行
                }
            }
        }
    }

    // 检查是否在执行时间窗口内
    struct tm* nextCheckTm = localtime(&nextTimestamp);
    int nextHour = nextCheckTm->tm_hour;
    int nextMinute = nextCheckTm->tm_min;
    int nextTotalMinutes = nextHour * 60 + nextMinute;
    int startTotalMinutes = startHour * 60 + startMinute;
    int endTotalMinutes = endHour * 60 + endMinute;

    // 如果超出执行窗口，调整到下一个执行周期
    if (nextTotalMinutes > endTotalMinutes) {
        if (isDaily) {
            // 每日重复：设置为明天的开始时间
            nextTm.tm_mday += 1;
            nextTm.tm_hour = startHour;
            nextTm.tm_min = startMinute;
            nextTimestamp = mktime(&nextTm);
            nextTimeMs = (uint64_t)nextTimestamp * 1000;
        } else {
            // 不重复：任务结束
            nextTimeMs = 0;
        }
    }

    nextExecutionTime = nextTimeMs;
    
    Serial.printf("🕐 TimerTask: 任务 %s 下次执行时间: %s\n", 
                  name.c_str(), 
                  nextTimeMs > 0 ? ctime(&nextTimestamp) : "已结束");
}

bool TimerTask::isDue() const {
    if (!isEnabled || nextExecutionTime == 0) {
        return false;
    }
    
    uint64_t currentTime = getCurrentUnixMs();
    return currentTime >= nextExecutionTime;
}

void TimerTask::updateExecutionStats() {
    lastExecutionTime = getCurrentUnixMs();
    executionCount++;
    updatedTime = lastExecutionTime;
    
    // 重新计算下次执行时间
    calculateNextExecutionTime();
    
    Serial.printf("📊 TimerTask: 任务 %s 执行统计更新 - 执行次数: %d, 下次执行: %llu\n", 
                  name.c_str(), executionCount, nextExecutionTime);
}
