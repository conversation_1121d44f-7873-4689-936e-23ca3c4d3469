#ifndef SIGNAL_STORAGE_TEMP_H
#define SIGNAL_STORAGE_TEMP_H

/**
 * @file signal_storage_temp.h
 * @brief 临时信号存储类 - 用于基础测试
 * @details 简化版本，仅用于编译测试
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <vector>
#include "storage/data_structures.h"

/**
 * @brief 临时信号存储类
 * @details 简化版本，仅用于基础测试
 */
class SignalStorage {
private:
    std::vector<SignalData> signals;

public:
    SignalStorage() {}
    ~SignalStorage() {}
    
    bool begin() {
        Serial.println("📦 临时信号存储初始化");
        return true;
    }
    
    bool saveSignal(const SignalData& signal) {
        Serial.printf("💾 [存储] 开始保存信号: %s\n", signal.name.c_str());

        // 检查内存是否足够
        size_t freeHeap = ESP.getFreeHeap();
        Serial.printf("🧠 [存储] 当前可用内存: %d bytes\n", freeHeap);

        if (freeHeap < 15000) {  // 需要至少15KB可用内存
            Serial.println("❌ [存储] 内存不足，无法保存信号");
            return false;
        }

        try {
            // 预先分配空间避免动态扩容
            if (signals.capacity() <= signals.size()) {
                signals.reserve(signals.size() + 5);  // 预留5个信号的空间
            }

            // 使用emplace_back避免额外的拷贝
            signals.emplace_back(signal);
            Serial.printf("✅ [存储] 信号保存成功: %s (总数: %d)\n", signal.name.c_str(), signals.size());
            return true;
        } catch (const std::exception& e) {
            Serial.printf("❌ [存储] 信号保存失败: %s\n", e.what());
            return false;
        } catch (...) {
            Serial.println("❌ [存储] 信号保存失败: 未知异常");
            return false;
        }
    }
    
    SignalData getSignal(const String& id) {
        Serial.printf("🔍 [存储] 查找信号: %s (总数: %d)\n", id.c_str(), signals.size());

        // 检查输入参数
        if (id.isEmpty()) {
            Serial.println("❌ [存储] 信号ID为空");
            return SignalData();
        }

        try {
            for (size_t i = 0; i < signals.size(); i++) {
                // 安全的字符串比较
                if (signals[i].id.length() > 0 && signals[i].id.equals(id)) {
                    Serial.printf("✅ [存储] 找到信号: %s\n", id.c_str());
                    return signals[i];
                }
            }
        } catch (const std::exception& e) {
            Serial.printf("❌ [存储] 查找信号异常: %s\n", e.what());
            return SignalData();
        } catch (...) {
            Serial.println("❌ [存储] 查找信号未知异常");
            return SignalData();
        }

        Serial.printf("⚠️ [存储] 未找到信号: %s\n", id.c_str());
        return SignalData();
    }
    
    std::vector<SignalData> getAllSignals() {
        return signals;
    }
    
    bool updateSignal(const SignalData& signal) {
        for (auto& existing : signals) {
            if (existing.id == signal.id) {
                existing = signal;
                return true;
            }
        }
        return false;
    }
    
    bool deleteSignal(const String& id) {
        auto it = std::find_if(signals.begin(), signals.end(),
                              [&id](const SignalData& signal) {
                                  return signal.id == id;
                              });
        if (it != signals.end()) {
            signals.erase(it);
            return true;
        }
        return false;
    }
    
    bool clearAllSignals() {
        signals.clear();
        return true;
    }
    
    uint32_t getSignalCount() const {
        return signals.size();
    }
    
    void loop() {
        // 临时实现，什么都不做
    }
};

#endif // SIGNAL_STORAGE_TEMP_H
