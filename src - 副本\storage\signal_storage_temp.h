#ifndef SIGNAL_STORAGE_TEMP_H
#define SIGNAL_STORAGE_TEMP_H

/**
 * @file signal_storage_temp.h
 * @brief 临时信号存储类 - 用于基础测试
 * @details 简化版本，仅用于编译测试
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <vector>
#include "storage/data_structures.h"

/**
 * @brief 临时信号存储类
 * @details 简化版本，仅用于基础测试
 */
class SignalStorage {
private:
    std::vector<SignalData> signals;

public:
    SignalStorage() {}
    ~SignalStorage() {}
    
    bool begin() {
        Serial.println("📦 临时信号存储初始化");
        return true;
    }
    
    bool saveSignal(const SignalData& signal) {
        signals.push_back(signal);
        Serial.printf("💾 保存信号: %s\n", signal.name.c_str());
        return true;
    }
    
    SignalData getSignal(const String& id) {
        for (const auto& signal : signals) {
            if (signal.id == id) {
                return signal;
            }
        }
        return SignalData();
    }
    
    std::vector<SignalData> getAllSignals() {
        return signals;
    }
    
    bool updateSignal(const SignalData& signal) {
        for (auto& existing : signals) {
            if (existing.id == signal.id) {
                existing = signal;
                return true;
            }
        }
        return false;
    }
    
    bool deleteSignal(const String& id) {
        auto it = std::find_if(signals.begin(), signals.end(),
                              [&id](const SignalData& signal) {
                                  return signal.id == id;
                              });
        if (it != signals.end()) {
            signals.erase(it);
            return true;
        }
        return false;
    }
    
    bool clearAllSignals() {
        signals.clear();
        return true;
    }
    
    uint32_t getSignalCount() const {
        return signals.size();
    }
    
    void loop() {
        // 临时实现，什么都不做
    }
};

#endif // SIGNAL_STORAGE_TEMP_H
