#!/usr/bin/env node
/**
 * 前端文件gzip压缩脚本 (Node.js版本)
 * 为ESP32 LittleFS生成压缩文件，优化传输性能
 */

const fs = require('fs');
const path = require('path');
const zlib = require('zlib');

function compressFile(sourcePath, targetPath) {
    return new Promise((resolve, reject) => {
        try {
            const sourceData = fs.readFileSync(sourcePath);
            const compressedData = zlib.gzipSync(sourceData, { level: 9 });
            
            fs.writeFileSync(targetPath, compressedData);
            
            const originalSize = sourceData.length;
            const compressedSize = compressedData.length;
            const ratio = ((1 - compressedSize / originalSize) * 100).toFixed(1);
            
            console.log(`✅ ${path.basename(sourcePath)}: ${originalSize.toLocaleString()} → ${compressedSize.toLocaleString()} bytes (${ratio}% 减少)`);
            resolve(true);
        } catch (error) {
            console.log(`❌ 压缩失败 ${sourcePath}: ${error.message}`);
            resolve(false);
        }
    });
}

function shouldCompress(filePath) {
    // 需要压缩的文件类型
    const compressExtensions = ['.html', '.css', '.js', '.json', '.svg', '.txt', '.md'];
    
    // 排除的文件
    const excludeFiles = ['api-test.html', 'button-test.html'];
    
    const fileName = path.basename(filePath);
    const fileExt = path.extname(filePath).toLowerCase();
    
    if (excludeFiles.includes(fileName)) {
        return false;
    }
    
    if (compressExtensions.includes(fileExt)) {
        try {
            const stats = fs.statSync(filePath);
            // 只压缩大于1KB的文件
            return stats.size > 1024;
        } catch {
            return false;
        }
    }
    
    return false;
}

function getAllFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
            getAllFiles(filePath, fileList);
        } else {
            fileList.push(filePath);
        }
    });
    
    return fileList;
}

async function compressFrontend() {
    const dataDir = 'data';
    
    if (!fs.existsSync(dataDir)) {
        console.log('❌ data目录不存在');
        return false;
    }
    
    console.log('🗜️  开始压缩前端文件...');
    
    let compressedCount = 0;
    let totalOriginal = 0;
    let totalCompressed = 0;
    
    const allFiles = getAllFiles(dataDir);
    
    for (const filePath of allFiles) {
        if (shouldCompress(filePath)) {
            const gzPath = filePath + '.gz';
            
            // 如果.gz文件已存在且比原文件新，跳过
            if (fs.existsSync(gzPath)) {
                const originalStat = fs.statSync(filePath);
                const gzStat = fs.statSync(gzPath);
                if (gzStat.mtime > originalStat.mtime) {
                    continue;
                }
            }
            
            const success = await compressFile(filePath, gzPath);
            if (success) {
                compressedCount++;
                totalOriginal += fs.statSync(filePath).size;
                totalCompressed += fs.statSync(gzPath).size;
            }
        }
    }
    
    if (compressedCount > 0) {
        const totalRatio = ((1 - totalCompressed / totalOriginal) * 100).toFixed(1);
        console.log('\n🎉 压缩完成!');
        console.log(`📊 压缩了 ${compressedCount} 个文件`);
        console.log(`📈 总大小: ${totalOriginal.toLocaleString()} → ${totalCompressed.toLocaleString()} bytes`);
        console.log(`💾 节省空间: ${totalRatio}%`);
    } else {
        console.log('ℹ️  没有需要压缩的文件');
    }
    
    return true;
}

// 运行压缩
compressFrontend().catch(console.error);
