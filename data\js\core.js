/**
 * R1系统 - 核心系统
 * 提供事件总线、通信管理、状态管理等核心功能
 */

/**
 * 事件总线 - 高效的事件发布订阅系统
 */
class EventBus {
  constructor() {
    this.events = new Map();
    this.onceEvents = new Map();
    this.performance = {
      totalEvents: 0,
      totalTime: 0,
      avgTime: 0
    };

    // 批处理优化 - 不影响信号发射时序
    this.eventQueue = [];
    this.processing = false;
    this.batchSize = 20;
    this.highPriorityEvents = new Set([
      'control.emit.progress',
      'control.signal.emitting',
      'control.signal.emitted',
      'signal.learning.status.changed',
      'system.error',
      'timer.task.due',
      'control.emit.completed',
      'timer.task.execution.request'
    ]);
    this.maxQueueSize = 1000;
    this.mergableEvents = new Set([
      'system.monitor.update',
      'status.display.update',
      'signal.list.refresh'
    ]);
  }

  /**
   * 监听事件
   * @param {string} event - 事件名
   * @param {Function} handler - 处理函数
   * @param {object} options - 选项
   */
  on(event, handler, options = {}) {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    
    const listener = {
      handler,
      priority: options.priority || 0,
      once: options.once || false
    };
    
    this.events.get(event).push(listener);
    
    // 按优先级排序
    this.events.get(event).sort((a, b) => b.priority - a.priority);
  }

  /**
   * 监听一次性事件
   * @param {string} event - 事件名
   * @param {Function} handler - 处理函数
   */
  once(event, handler) {
    this.on(event, handler, { once: true });
  }

  /**
   * 取消监听
   * @param {string} event - 事件名
   * @param {Function} handler - 处理函数
   */
  off(event, handler) {
    if (!this.events.has(event)) return;
    
    const listeners = this.events.get(event);
    const index = listeners.findIndex(listener => listener.handler === handler);
    
    if (index > -1) {
      listeners.splice(index, 1);
    }
    
    if (listeners.length === 0) {
      this.events.delete(event);
    }
  }

  /**
   * 发布事件 - 优化版本（批处理 + 高优先级立即处理）
   * @param {string} event - 事件名
   * @param {any} data - 事件数据
   * @returns {Promise} 处理结果
   */
  async emit(event, data = null) {
    // 高优先级事件立即处理（信号发射相关）
    if (this.highPriorityEvents.has(event)) {
      return this.processEventImmediate(event, data);
    }

    // 普通事件加入批处理队列
    return this.addToBatch(event, data);
  }

  /**
   * 立即处理高优先级事件
   */
  async processEventImmediate(event, data) {
    const startTime = performance.now();

    if (!this.events.has(event)) {
      return [];
    }

    const listeners = this.events.get(event);
    const results = [];
    const toRemove = [];

    for (let i = 0; i < listeners.length; i++) {
      const listener = listeners[i];

      try {
        const result = await listener.handler(data);
        results.push(result);

        if (listener.once) {
          toRemove.push(i);
        }
      } catch (error) {
        console.error(`Event handler error for ${event}:`, error);
        results.push({ error });
      }
    }

    // 移除一次性监听器
    toRemove.reverse().forEach(index => {
      listeners.splice(index, 1);
    });

    // 性能统计
    const endTime = performance.now();
    const duration = endTime - startTime;
    this.performance.totalEvents++;
    this.performance.totalTime += duration;
    this.performance.avgTime = this.performance.totalTime / this.performance.totalEvents;

    return results;
  }

  /**
   * 添加到批处理队列
   */
  addToBatch(event, data) {
    return new Promise((resolve) => {
      // 检查队列大小
      if (this.eventQueue.length >= this.maxQueueSize) {
        console.warn('EventBus: 事件队列已满，丢弃旧事件');
        this.eventQueue.shift();
      }

      // 对于可合并事件，替换队列中的旧事件
      if (this.mergableEvents.has(event)) {
        const existingIndex = this.eventQueue.findIndex(item => item.event === event);
        if (existingIndex !== -1) {
          this.eventQueue[existingIndex] = { event, data, resolve, timestamp: performance.now() };
          return;
        }
      }

      // 添加到队列
      this.eventQueue.push({ event, data, resolve, timestamp: performance.now() });

      // 调度批处理
      this.scheduleBatch();
    });
  }

  /**
   * 调度批处理
   */
  scheduleBatch() {
    if (this.processing) return;

    this.processing = true;

    // 使用requestIdleCallback优化性能
    if (window.requestIdleCallback) {
      requestIdleCallback(() => {
        this.processBatch();
        this.processing = false;
      }, { timeout: 16 });
    } else {
      requestAnimationFrame(() => {
        this.processBatch();
        this.processing = false;
      });
    }
  }

  /**
   * 批处理事件
   */
  async processBatch() {
    const startTime = performance.now();
    const timeSlice = 8; // 8ms时间片

    while (this.eventQueue.length > 0 && (performance.now() - startTime) < timeSlice) {
      const batch = this.eventQueue.splice(0, this.batchSize);

      for (const item of batch) {
        try {
          const result = await this.processEventImmediate(item.event, item.data);
          item.resolve(result);
        } catch (error) {
          console.error(`Batch event error for ${item.event}:`, error);
          item.resolve([{ error }]);
        }
      }
    }

    // 如果还有事件，继续处理
    if (this.eventQueue.length > 0) {
      this.scheduleBatch();
    }
  }

  /**
   * 同步发布事件 - 用于需要立即执行的场景（如UI事件注册）
   * @param {string} event - 事件名
   * @param {any} data - 事件数据
   * @returns {Array} 处理结果
   */
  emitSync(event, data = null) {
    const startTime = performance.now();

    if (!this.events.has(event)) {
      return [];
    }

    const listeners = this.events.get(event);
    const results = [];
    const toRemove = [];

    for (let i = 0; i < listeners.length; i++) {
      const listener = listeners[i];

      try {
        // 同步执行处理器
        const result = listener.handler(data);
        results.push(result);

        if (listener.once) {
          toRemove.push(i);
        }
      } catch (error) {
        console.error(`Event handler error for ${event}:`, error);
        results.push({ error });
      }
    }

    // 移除一次性监听器
    toRemove.reverse().forEach(index => {
      listeners.splice(index, 1);
    });

    // 性能统计
    const endTime = performance.now();
    const duration = endTime - startTime;
    this.performance.totalEvents++;
    this.performance.totalTime += duration;
    this.performance.avgTime = this.performance.totalTime / this.performance.totalEvents;

    return results;
  }

  /**
   * 获取性能统计
   * @returns {object} 性能数据
   */
  getPerformance() {
    return { ...this.performance };
  }

  /**
   * 清空所有事件监听器
   */
  clear() {
    this.events.clear();
    this.onceEvents.clear();
    this.eventQueue.length = 0;
    this.processing = false;
  }

  /**
   * 获取批处理统计
   */
  getBatchStats() {
    return {
      queueLength: this.eventQueue.length,
      processing: this.processing,
      highPriorityEvents: Array.from(this.highPriorityEvents),
      mergableEvents: Array.from(this.mergableEvents)
    };
  }
}

/**
 * ESP32通信管理器
 */
class ESP32Communicator {
  constructor(eventBus) {
    this.eventBus = eventBus;
    // ESP32 AP模式地址配置 - 直接硬编码避免方法调用问题
    this.baseURL = 'http://192.168.4.1:8000';
    this.wsURL = 'ws://192.168.4.1:8001/ws';
    console.error('🚨 构造函数中直接设置URL - HTTP:', this.baseURL, 'WebSocket:', this.wsURL);
    this.ws = null;
    this.connected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.requestQueue = [];
    this.batchTimer = null;
    this.batchDelay = 50; // 50ms批量延迟
    
    this.performance = {
      requestCount: 0,
      successCount: 0,
      errorCount: 0,
      avgResponseTime: 0,
      totalResponseTime: 0
    };
  }

  /**
   * 检测HTTP API基础URL
   */
  detectBaseURL() {
    // 简单直接的修复 - 强制返回正确的HTTP URL
    console.error('🚨 HTTP URL强制修复生效！');
    return 'http://192.168.4.1:8000';
  }

  /**
   * 检测WebSocket URL
   */
  detectWebSocketURL() {
    // 简单直接的修复 - 强制返回正确的WebSocket URL
    console.error('🚨 WebSocket URL强制修复生效！');
    return 'ws://192.168.4.1:8001/ws';
  }

  /**
   * 初始化通信 - 开发模式下不阻止系统启动
   */
  async init() {
    try {
      console.log('🚀 初始化ESP32管理器...');
      console.log('📡 HTTP API: ' + this.baseURL);
      console.log('🔌 WebSocket: ' + this.wsURL);

      // 测试HTTP连接
      await this.testConnection();

      // 建立WebSocket连接
      await this.connectWebSocket();

      this.connected = true;
      this.eventBus.emit('esp32.connected', {
        timestamp: Date.now(),
        message: 'ESP32连接成功'
      });
      console.log('✅ ESP32管理器初始化完成 - 在线模式');

    } catch (error) {
      console.log('❌ ESP32连接失败:', error.message);
      this.connected = false;
      this.eventBus.emit('esp32.error', {
        error: error.message,
        timestamp: Date.now()
      });

      // 允许系统继续启动
      console.log('⚠️ 系统将在离线模式下运行');
      this.eventBus.emit('esp32.offline', {
        reason: error.message,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 测试HTTP连接
   */
  async testConnection() {
    console.log('🔍 测试后端连接...');
    console.log(`📡 请求URL: ${this.baseURL}/api/status`);

    try {
      // 直接使用fetch进行测试，不通过request方法
      const url = `${this.baseURL}/api/status`;
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        timeout: 5000
      });

      console.log(`📊 HTTP状态: ${response.status} ${response.statusText}`);
      console.log('📋 响应头:', {
        'content-type': response.headers.get('content-type'),
        'content-length': response.headers.get('content-length')
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('📦 响应数据:', data);

      if (data.success) {
        console.log('✅ 后端连接正常');
        return true;
      } else {
        throw new Error('后端返回错误: ' + (data.error || '未知错误'));
      }

    } catch (error) {
      console.error('❌ 后端连接失败:', error);
      throw error;
    }
  }

  /**
   * 建立WebSocket连接
   */
  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.wsURL);
        
        this.ws.onopen = () => {
          console.log('✅ WebSocket连接成功');
          this.reconnectAttempts = 0;
          this.eventBus.emit('esp32.ws.connected', {
            timestamp: Date.now()
          });
          resolve();
        };
        
        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
          } catch (error) {
            console.error('WebSocket消息解析错误:', error);
          }
        };
        
        this.ws.onclose = (event) => {
          this.connected = false;
          console.log('WebSocket连接关闭:', event.code, event.reason);
          
          this.eventBus.emit('esp32.disconnected', {
            code: event.code,
            reason: event.reason,
            timestamp: Date.now()
          });
          
          // 自动重连
          this.scheduleReconnect();
        };
        
        this.ws.onerror = (error) => {
          console.error('WebSocket错误:', error);
          reject(new Error('WebSocket连接失败'));
        };
        
        // 连接超时
        setTimeout(() => {
          if (this.ws.readyState !== WebSocket.OPEN) {
            this.ws.close();
            reject(new Error('WebSocket连接超时'));
          }
        }, 5000);
        
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 处理WebSocket消息
   * @param {object} data - 消息数据
   */
  handleWebSocketMessage(data) {
    console.log('📨 WebSocket消息:', data);

    const { type, payload } = data;

    // 处理特殊消息类型
    if (type === 'ping') {
      console.log('🏓 回复pong消息');
      this.sendWebSocketMessage('pong', { timestamp: Date.now() });
      return;
    }

    // 发布对应的事件
    this.eventBus.emit(`esp32.${type}`, payload);

    // 发布通用WebSocket消息事件
    this.eventBus.emit('esp32.message', data);
  }

  /**
   * 计划重连
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('达到最大重连次数，停止重连');
      this.eventBus.emit('esp32.reconnect.failed', {
        attempts: this.reconnectAttempts,
        timestamp: Date.now()
      });
      return;
    }
    
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指数退避
    
    console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连`);
    
    setTimeout(() => {
      this.connectWebSocket().catch(error => {
        console.error('重连失败:', error);
        this.scheduleReconnect();
      });
    }, delay);
  }

  /**
   * 发送HTTP请求
   * @param {string} endpoint - 端点
   * @param {object} options - 请求选项
   * @returns {Promise} 响应数据
   */
  async request(endpoint, options = {}) {
    const startTime = performance.now();
    this.performance.requestCount++;

    const fullUrl = `${this.baseURL}${endpoint}`;
    const requestOptions = {
      timeout: 10000,
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    // 详细请求调试信息
    console.log('🔍 ESP32请求调试信息:', {
      url: fullUrl,
      method: requestOptions.method || 'GET',
      headers: requestOptions.headers,
      bodyLength: requestOptions.body ? requestOptions.body.length : 0,
      bodyPreview: requestOptions.body ? requestOptions.body.substring(0, 200) : null
    });

    try {
      const response = await fetch(fullUrl, requestOptions);
      
      if (!response.ok) {
        // 尝试读取错误响应体
        let errorBody = '';
        try {
          errorBody = await response.text();
        } catch (e) {
          errorBody = '无法读取错误响应体';
        }

        console.error('🔍 HTTP错误详情:', {
          status: response.status,
          statusText: response.statusText,
          url: fullUrl,
          method: requestOptions.method || 'GET',
          responseHeaders: Object.fromEntries(response.headers.entries()),
          errorBody: errorBody
        });

        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorBody}`);
      }
      
      const data = await response.json();

      // 验证API响应格式
      if (window.R1DataValidator && !window.R1DataValidator.validateAPIResponse(data)) {
        console.warn('API响应格式验证失败:', window.R1DataValidator.getErrors());
      }

      // 性能统计
      const responseTime = performance.now() - startTime;
      this.performance.successCount++;
      this.performance.totalResponseTime += responseTime;
      this.performance.avgResponseTime =
        this.performance.totalResponseTime / this.performance.successCount;
      
      // 发布请求成功事件
      this.eventBus.emit('esp32.request.success', {
        endpoint,
        responseTime,
        data
      });
      
      return data;
      
    } catch (error) {
      this.performance.errorCount++;
      
      // 发布请求失败事件
      this.eventBus.emit('esp32.request.error', {
        endpoint,
        error: error.message,
        timestamp: Date.now()
      });
      
      throw error;
    }
  }

  /**
   * 批量请求
   * @param {string} endpoint - 端点
   * @param {object} data - 请求数据
   * @returns {Promise} 响应数据
   */
  batchRequest(endpoint, data) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ endpoint, data, resolve, reject });
      
      if (!this.batchTimer) {
        this.batchTimer = setTimeout(() => {
          this.flushBatchRequests();
        }, this.batchDelay);
      }
    });
  }

  /**
   * 刷新批量请求
   */
  async flushBatchRequests() {
    if (this.requestQueue.length === 0) return;
    
    const batch = this.requestQueue.splice(0);
    this.batchTimer = null;
    
    try {
      const response = await this.request('/api/batch', {
        method: 'POST',
        body: JSON.stringify({
          requests: batch.map(({ endpoint, data }) => ({ endpoint, data }))
        })
      });
      
      // 处理批量响应
      if (response.results && Array.isArray(response.results)) {
        response.results.forEach((result, index) => {
          if (result.success) {
            batch[index].resolve(result.data);
          } else {
            batch[index].reject(new Error(result.error || '批量请求失败'));
          }
        });
      } else {
        throw new Error('批量响应格式错误');
      }
      
    } catch (error) {
      // 批量失败，逐个重试
      console.warn('批量请求失败，逐个重试:', error);
      
      batch.forEach(async ({ endpoint, data, resolve, reject }) => {
        try {
          const result = await this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
          });
          resolve(result);
        } catch (retryError) {
          reject(retryError);
        }
      });
    }
  }

  /**
   * 发送WebSocket消息
   * @param {string} type - 消息类型
   * @param {any} data - 消息数据
   * @returns {boolean} 是否发送成功
   */
  sendWebSocketMessage(type, data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = {
        type,
        data,
        timestamp: Date.now()
      };
      
      this.ws.send(JSON.stringify(message));
      return true;
    }
    
    console.warn('WebSocket未连接，无法发送消息');
    return false;
  }

  /**
   * 获取性能统计
   * @returns {object} 性能数据
   */
  getPerformance() {
    return { ...this.performance };
  }

  /**
   * 关闭连接
   */
  close() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.connected = false;
  }
}

/**
 * 通知系统
 */
class NotificationSystem {
  constructor() {
    this.container = null;
    this.notifications = new Map();
    this.defaultDuration = 5000;
    this.maxNotifications = 5;
  }

  /**
   * 初始化通知系统
   */
  init() {
    this.container = $('#notificationContainer');
    if (!this.container) {
      console.error('通知容器未找到');
    }
  }

  /**
   * 显示通知
   * @param {string} message - 消息内容
   * @param {string} type - 通知类型 (success, error, warning, info)
   * @param {number} duration - 显示时长
   * @returns {string} 通知ID
   */
  show(message, type = 'info', duration = this.defaultDuration) {
    if (!this.container) {
      console.warn('通知系统未初始化');
      return null;
    }

    const id = R1Utils.generateId('notification');
    
    // 限制通知数量
    if (this.notifications.size >= this.maxNotifications) {
      const oldestId = this.notifications.keys().next().value;
      this.hide(oldestId);
    }

    const notification = R1Utils.dom.create('div', {
      className: `notification ${type}`,
      dataset: { id }
    }, `
      <div class="notification-content">
        <div class="notification-message">${message}</div>
        <button class="notification-close" onclick="window.R1System.notification.hide('${id}')">&times;</button>
      </div>
    `);

    this.container.appendChild(notification);
    this.notifications.set(id, {
      element: notification,
      timer: null,
      type,
      message,
      timestamp: Date.now()
    });

    // 自动隐藏
    if (duration > 0) {
      window.UnifiedTimerManager.addTimer(
        `notification_${id}`,
        () => {
          this.hide(id);
        },
        duration,
        false
      );
    }

    return id;
  }

  /**
   * 隐藏通知
   * @param {string} id - 通知ID
   */
  hide(id) {
    const notification = this.notifications.get(id);
    if (!notification) return;

    // 清除定时器
    if (window.UnifiedTimerManager.hasTimer(`notification_${id}`)) {
      window.UnifiedTimerManager.removeTimer(`notification_${id}`);
    }

    // 移除元素
    notification.element.style.animation = 'slideOut 0.3s ease-in forwards';
    window.UnifiedTimerManager.addTimer(
      `notification_remove_${id}`,
      () => {
        if (notification.element.parentNode) {
          notification.element.parentNode.removeChild(notification.element);
        }
        this.notifications.delete(id);
      },
      300,
      false
    );
  }

  /**
   * 清空所有通知
   */
  clear() {
    this.notifications.forEach((notification, id) => {
      this.hide(id);
    });
  }
}

// 添加slideOut动画样式
if (!document.getElementById('notification-styles')) {
  const style = document.createElement('style');
  style.id = 'notification-styles';
  style.textContent = `
    @keyframes slideOut {
      from {
        transform: translateX(0);
        opacity: 1;
      }
      to {
        transform: translateX(100%);
        opacity: 0;
      }
    }

    .notification-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-md);
    }

    .notification-message {
      flex: 1;
      font-size: 0.875rem;
      line-height: 1.4;
    }

    .notification-close {
      background: none;
      border: none;
      font-size: 1.25rem;
      cursor: pointer;
      color: var(--text-secondary);
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--radius-sm);
      transition: background-color var(--transition-fast);
    }

    .notification-close:hover {
      background: var(--bg-secondary);
    }
  `;
  document.head.appendChild(style);
}

/**
 * 统一模块基类 - 所有模块必须继承此基类
 */
class BaseModule {
  constructor(eventBus, esp32, moduleName) {
    this.eventBus = eventBus;
    this.esp32 = esp32;
    this.moduleName = moduleName;
    this.isInitialized = false;
    this.isActive = false;

    // 统一的模块状态
    this.state = {
      loading: false,
      error: null,
      data: new Map()
    };

    // 统一的性能监控
    this.performance = {
      initTime: 0,
      operationCount: 0,
      errorCount: 0,
      lastOperation: null
    };

    // BaseModule不自动初始化，由调用者控制 - 符合控制反转原则
  }

  /**
   * 统一的初始化流程 - 所有模块必须遵循
   * 简单直接的初始化，由调用者控制时机
   */
  async init() {
    // 简单的重复初始化检查
    if (this.isInitialized) {
      console.log(`⚠️ ${this.moduleName} 模块已经初始化，跳过重复初始化`);
      return;
    }

    const startTime = performance.now();
    try {
      console.log(`🔧 开始初始化 ${this.moduleName} 模块...`);

      // 1. 初始化事件监听器
      try {
        await this.initEventListeners();
        console.log(`📡 ${this.moduleName} 事件监听器初始化完成`);
      } catch (error) {
        console.error(`❌ ${this.moduleName} 事件监听器初始化失败:`, error);
        throw error;
      }

      // 2. 初始化UI组件
      try {
        await this.initUI();
        console.log(`🎨 ${this.moduleName} UI初始化完成`);
      } catch (error) {
        console.error(`❌ ${this.moduleName} UI初始化失败:`, error);
        throw error;
      }

      // 3. 加载模块数据
      try {
        await this.loadData();
        console.log(`📊 ${this.moduleName} 数据加载完成`);
      } catch (error) {
        console.error(`❌ ${this.moduleName} 数据加载失败:`, error);
        // 数据加载失败不应该阻止模块初始化
        console.warn(`⚠️ ${this.moduleName} 数据加载失败，但模块将继续初始化`);
      }

      // 4. 标记初始化完成
      this.isInitialized = true;
      this.isActive = true;
      this.performance.initTime = performance.now() - startTime;

      // 5. 发布模块就绪事件
      this.emitEvent('module.ready', {
        moduleName: this.moduleName,
        initTime: this.performance.initTime
      });

      console.log(`✅ ${this.moduleName} 模块初始化完成，耗时: ${this.performance.initTime.toFixed(2)}ms`);

    } catch (error) {
      this.performance.initTime = performance.now() - startTime;
      console.error(`❌ ${this.moduleName} 模块初始化失败:`, error);
      this.handleError(error, '模块初始化');
      throw error;
    }
  }

  /**
   * 统一的事件监听器初始化 - 子类必须实现
   */
  async initEventListeners() {
    // 监听系统级事件
    this.eventBus.on('system.refresh', () => {
      this.refresh();
    });

    // 子类实现具体的事件监听
    if (this.setupEventListeners) {
      await this.setupEventListeners();
    }
  }

  /**
   * 统一的UI初始化 - 子类必须实现
   */
  async initUI() {
    // 子类实现具体的UI初始化
    if (this.setupUI) {
      await this.setupUI();
    }
  }

  /**
   * 统一的数据加载 - 子类可选实现
   */
  async loadData() {
    // 子类实现具体的数据加载
    if (this.loadModuleData) {
      await this.loadModuleData();
    }
  }

  /**
   * 统一的事件发布方法
   * ✅ 根源修复：直接发送原始数据，不进行包装
   * 这样保持与直接使用 eventBus.emit() 的一致性
   */
  emitEvent(eventType, data = null) {
    // 如果数据中没有 source，自动添加
    if (data && typeof data === 'object' && !data.source) {
      data.source = this.moduleName;
    }

    // 直接发送原始数据，不包装
    this.eventBus.emit(eventType, data);
    this.performance.lastOperation = eventType;
    this.performance.operationCount++;
  }

  /**
   * 统一的错误处理方法
   */
  handleError(error, operation = '操作') {
    this.performance.errorCount++;
    this.state.error = {
      message: error.message,
      operation: operation,
      timestamp: Date.now()
    };

    console.error(`❌ ${this.moduleName} ${operation}失败:`, error);

    // 发布错误事件
    this.emitEvent('module.error', {
      moduleName: this.moduleName,
      operation: operation,
      error: error.message
    });

    // 显示用户通知
    if (window.R1System && window.R1System.notification) {
      window.R1System.notification.show(
        `${this.moduleName} ${operation}失败: ${error.message}`,
        'error'
      );
    }
  }

  /**
   * 统一的成功处理方法
   */
  handleSuccess(message, operation = '操作', data = null) {
    this.state.error = null;

    console.log(`✅ ${this.moduleName} ${operation}成功:`, message);

    // 发布成功事件
    this.emitEvent('module.success', {
      moduleName: this.moduleName,
      operation: operation,
      message: message,
      data: data
    });

    // 显示用户通知
    if (window.R1System && window.R1System.notification) {
      window.R1System.notification.show(
        `${message}`,
        'success'
      );
    }
  }

  /**
   * 统一的ESP32请求方法
   */
  async requestESP32(endpoint, options = {}) {
    try {
      this.state.loading = true;
      const response = await this.esp32.request(endpoint, options);
      this.state.loading = false;
      return response;
    } catch (error) {
      this.state.loading = false;
      throw error;
    }
  }



  /**
   * 统一的刷新方法
   */
  async refresh() {
    try {
      this.state.loading = true;

      // 子类实现具体的刷新逻辑
      if (this.onRefresh) {
        await this.onRefresh();
      }

      this.state.loading = false;
      this.handleSuccess('刷新完成', '数据刷新');

    } catch (error) {
      this.state.loading = false;
      this.handleError(error, '数据刷新');
    }
  }

  /**
   * 获取模块状态
   */
  getStatus() {
    return {
      moduleName: this.moduleName,
      isInitialized: this.isInitialized,
      isActive: this.isActive,
      state: { ...this.state },
      performance: { ...this.performance }
    };
  }

  /**
   * 销毁模块
   */
  destroy() {
    this.isActive = false;
    this.isInitialized = false;

    // 清理事件监听器
    this.eventBus.off('system.refresh');

    // 子类实现具体的清理逻辑
    if (this.cleanup) {
      this.cleanup();
    }

    console.log(`🗑️ ${this.moduleName} 模块已销毁`);
  }
}

// 导出核心类
window.BaseModule = BaseModule;
window.EventBus = EventBus;
window.ESP32Communicator = ESP32Communicator;
window.NotificationSystem = NotificationSystem;
