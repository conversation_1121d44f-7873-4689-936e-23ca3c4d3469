#ifndef API_HANDLERS_H
#define API_HANDLERS_H

/**
 * @file api_handlers.h
 * @brief API处理器工具类 - HTTP服务器的辅助方法
 * @details 包含HTTP服务器的工具方法和剩余的API处理器
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>

#include "config/config.h"
#include "config/constants.h"
#include "storage/data_structures.h"

// 前向声明
class SystemManager;
class IRSignalManager;
class SignalStorage;
class TaskScheduler;

/**
 * @brief API处理器工具类
 * @details 提供HTTP服务器的工具方法和剩余的API处理器
 */
class APIHandlers {
public:
    // ==================== 信号管理API ====================

    /**
     * @brief 处理创建信号 - POST /api/signals
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleCreateSignal(AsyncWebServerRequest *request, uint8_t *data,
                                  size_t len, size_t index, size_t total);

    /**
     * @brief 处理更新信号 - PUT /api/signals/{id}
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleUpdateSignal(AsyncWebServerRequest *request, uint8_t *data,
                                  size_t len, size_t index, size_t total);

    /**
     * @brief 处理删除信号 - DELETE /api/signals/{id}
     * @param request HTTP请求
     */
    static void handleDeleteSignal(AsyncWebServerRequest *request);

    /**
     * @brief 处理信号导出 - GET /api/signals/export
     * @param request HTTP请求
     */
    static void handleExportSignals(AsyncWebServerRequest *request);

    /**
     * @brief 处理信号导入 - POST /api/signals/import
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleImportSignals(AsyncWebServerRequest *request, uint8_t *data,
                                   size_t len, size_t index, size_t total);

    // ==================== 信号分组管理API ====================

    /**
     * @brief 处理获取信号分组列表 - GET /api/groups
     * @param request HTTP请求
     */
    static void handleGetSignalGroups(AsyncWebServerRequest *request);

    /**
     * @brief 处理创建信号分组 - POST /api/groups
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleCreateSignalGroup(AsyncWebServerRequest *request, uint8_t *data,
                                       size_t len, size_t index, size_t total);

    /**
     * @brief 处理更新信号分组 - PUT /api/groups/{id}
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleUpdateSignalGroup(AsyncWebServerRequest *request, uint8_t *data,
                                       size_t len, size_t index, size_t total);

    /**
     * @brief 处理删除信号分组 - DELETE /api/groups/{id}
     * @param request HTTP请求
     */
    static void handleDeleteSignalGroup(AsyncWebServerRequest *request);

    // ==================== 用户配置管理API ====================

    /**
     * @brief 处理获取用户配置 - GET /api/config
     * @param request HTTP请求
     */
    static void handleGetUserConfig(AsyncWebServerRequest *request);

    /**
     * @brief 处理更新用户配置 - PUT /api/config
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleUpdateUserConfig(AsyncWebServerRequest *request, uint8_t *data,
                                      size_t len, size_t index, size_t total);

    /**
     * @brief 处理重置用户配置 - POST /api/config/reset
     * @param request HTTP请求
     */
    static void handleResetUserConfig(AsyncWebServerRequest *request);

    // ==================== 系统备份恢复API ====================

    /**
     * @brief 处理系统备份导出 - GET /api/backup
     * @param request HTTP请求
     */
    static void handleExportBackup(AsyncWebServerRequest *request);

    /**
     * @brief 处理系统备份导入 - POST /api/restore
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleImportBackup(AsyncWebServerRequest *request, uint8_t *data,
                                  size_t len, size_t index, size_t total);

    // ==================== 日志管理API ====================

    /**
     * @brief 处理获取系统日志 - GET /api/logs
     * @param request HTTP请求
     */
    static void handleGetSystemLogs(AsyncWebServerRequest *request);

    /**
     * @brief 处理清空系统日志 - DELETE /api/logs
     * @param request HTTP请求
     */
    static void handleClearSystemLogs(AsyncWebServerRequest *request);

    /**
     * @brief 处理下载日志文件 - GET /api/logs/download
     * @param request HTTP请求
     */
    static void handleDownloadLogs(AsyncWebServerRequest *request);

    // ==================== 红外功能API ====================

    /**
     * @brief 处理学习控制 - POST /api/learning
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleLearningControl(AsyncWebServerRequest *request, uint8_t *data,
                                     size_t len, size_t index, size_t total);

    /**
     * @brief 处理信号检测 - POST /api/learning/detect
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleLearningDetect(AsyncWebServerRequest *request, uint8_t *data,
                                    size_t len, size_t index, size_t total);

    // ==================== 定时器管理API ====================

    /**
     * @brief 处理创建定时任务 - POST /api/timer/tasks
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleCreateTimerTask(AsyncWebServerRequest *request, uint8_t *data,
                                     size_t len, size_t index, size_t total);

    /**
     * @brief 处理更新定时任务 - PUT /api/timer/tasks/{id}
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleUpdateTimerTask(AsyncWebServerRequest *request, uint8_t *data,
                                     size_t len, size_t index, size_t total);

    /**
     * @brief 处理删除定时任务 - DELETE /api/timer/tasks/{id}
     * @param request HTTP请求
     */
    static void handleDeleteTimerTask(AsyncWebServerRequest *request);

    /**
     * @brief 处理定时器启用/禁用 - POST /api/timer/enable
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleTimerEnable(AsyncWebServerRequest *request, uint8_t *data,
                                 size_t len, size_t index, size_t total);

    // ==================== 信号发射API ====================
    
    /**
     * @brief 处理信号发射 - POST /api/emit/signal
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleEmitSignal(AsyncWebServerRequest *request, uint8_t *data,
                                size_t len, size_t index, size_t total);

    // ==================== 批量操作API ====================
    
    /**
     * @brief 处理批量操作 - POST /api/batch
     * @param request HTTP请求
     * @param data 请求数据
     * @param len 数据长度
     * @param index 数据索引
     * @param total 总长度
     */
    static void handleBatchOperation(AsyncWebServerRequest *request, uint8_t *data, 
                                    size_t len, size_t index, size_t total);
    
    // ==================== 工具方法 ====================
    
    /**
     * @brief 发送成功响应
     * @param request HTTP请求
     * @param data 响应数据
     * @param message 消息
     */
    static void sendSuccessResponse(AsyncWebServerRequest *request,
                                   const JsonDocument& data,
                                   const String& message = "",
                                   uint32_t requestStartTime = millis());
    
    /**
     * @brief 发送错误响应
     * @param request HTTP请求
     * @param error 错误信息
     * @param code HTTP状态码
     * @param message 消息
     */
    static void sendErrorResponse(AsyncWebServerRequest *request, 
                                 const String& error, 
                                 int code = 400,
                                 const String& message = "");
    
    /**
     * @brief 解析JSON请求体
     * @param data 请求数据
     * @param len 数据长度
     * @param doc JSON文档
     * @return bool 解析是否成功
     */
    static bool parseJsonBody(uint8_t *data, size_t len, JsonDocument& doc);
    
    /**
     * @brief 验证请求参数
     * @param request HTTP请求
     * @param requiredParams 必需参数列表
     * @return bool 验证是否通过
     */
    static bool validateRequest(AsyncWebServerRequest *request, 
                               const std::vector<String>& requiredParams);
    
    /**
     * @brief 记录请求日志
     * @param request HTTP请求
     * @param responseCode 响应代码
     * @param processingTime 处理时间
     */
    static void logRequest(AsyncWebServerRequest *request, int responseCode, uint32_t processingTime);
    
    /**
     * @brief 获取客户端IP
     * @param request HTTP请求
     * @return IPAddress 客户端IP
     */
    static IPAddress getClientIP(AsyncWebServerRequest *request);
    
    /**
     * @brief 处理OPTIONS请求 (CORS预检)
     * @param request HTTP请求
     */
    static void handleOptions(AsyncWebServerRequest *request);
    
    /**
     * @brief 处理404错误
     * @param request HTTP请求
     */
    static void handleNotFound(AsyncWebServerRequest *request);
    
    /**
     * @brief 中间件：请求预处理
     * @param request HTTP请求
     * @return bool 是否继续处理
     */
    static bool requestMiddleware(AsyncWebServerRequest *request);
    
    /**
     * @brief 获取系统管理器实例
     * @return SystemManager* 系统管理器指针
     */
    static SystemManager* getSystemManager();
    
    /**
     * @brief 获取红外管理器实例
     * @return IRSignalManager* 红外管理器指针
     */
    static IRSignalManager* getIRManager();
    
    /**
     * @brief 获取信号存储实例
     * @return SignalStorage* 信号存储指针
     */
    static SignalStorage* getSignalStorage();
    
    /**
     * @brief 获取任务调度器实例
     * @return TaskScheduler* 任务调度器指针
     */
    static TaskScheduler* getTaskScheduler();

    /**
     * @brief 验证信号数据格式
     * @param signal 信号JSON对象
     * @return bool 数据是否有效
     */
    static bool validateSignalData(const JsonObject& signal);

    /**
     * @brief 处理单个批量请求
     * @param request 请求对象
     * @param response 响应对象
     */
    static void processBatchRequest(const JsonObject& request, JsonObject& response);

private:
    // 私有构造函数，防止实例化
    APIHandlers() = delete;
    ~APIHandlers() = delete;
    APIHandlers(const APIHandlers&) = delete;
    APIHandlers& operator=(const APIHandlers&) = delete;
};

#endif // API_HANDLERS_H
