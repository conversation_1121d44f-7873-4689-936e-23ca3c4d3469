#include "task_scheduler.h"
#include "system_manager.h"
#include "ir/ir_manager.h"
#include "storage/signal_storage.h"
#include "utils/string_utils.h"

// 静态成员初始化
TaskScheduler* TaskScheduler::instance = nullptr;

TaskScheduler::TaskScheduler() 
    : taskQueue(taskPriorityCompare)
    , schedulerMutex(nullptr)
    , taskEventQueue(nullptr)
    , isRunning(false)
    , maxConcurrentTasks(5)
    , totalTasksExecuted(0)
    , totalTasksFailed(0)
    , schedulerTaskHandle(nullptr) {
}

TaskScheduler& TaskScheduler::getInstance() {
    if (!instance) {
        instance = new TaskScheduler();
    }
    return *instance;
}

TaskScheduler::~TaskScheduler() {
    stop();
    
    // 清理所有任务
    while (!taskQueue.empty()) {
        delete taskQueue.top();
        taskQueue.pop();
    }
    
    for (auto* task : runningTasks) {
        delete task;
    }
    runningTasks.clear();
    
    for (auto* task : taskHistory) {
        delete task;
    }
    taskHistory.clear();
    
    for (auto* task : timerTasks) {
        delete task;
    }
    timerTasks.clear();
    
    // 清理同步原语
    if (schedulerMutex) {
        vSemaphoreDelete(schedulerMutex);
    }
    if (taskEventQueue) {
        vQueueDelete(taskEventQueue);
    }
}

bool TaskScheduler::begin() {
    Serial.println("📅 初始化任务调度器...");
    
    // 创建同步原语
    schedulerMutex = xSemaphoreCreateMutex();
    taskEventQueue = xQueueCreate(50, sizeof(String*));
    
    if (!schedulerMutex || !taskEventQueue) {
        Serial.println("❌ 任务调度器同步原语创建失败");
        return false;
    }
    
    // 创建调度器任务
    BaseType_t result = xTaskCreatePinnedToCore(
        schedulerTask,
        "TaskScheduler",
        8192,
        this,
        2,  // 中等优先级
        &schedulerTaskHandle,
        0   // Core 0
    );
    
    if (result != pdPASS) {
        Serial.println("❌ 任务调度器任务创建失败");
        return false;
    }
    
    isRunning = true;
    Serial.println("✅ 任务调度器初始化完成");
    return true;
}

void TaskScheduler::stop() {
    isRunning = false;
    
    if (schedulerTaskHandle) {
        vTaskDelete(schedulerTaskHandle);
        schedulerTaskHandle = nullptr;
    }
    
    Serial.println("🛑 任务调度器已停止");
}

void TaskScheduler::loop() {
    if (!isRunning) {
        return;
    }
    
    if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(10)) == pdTRUE) {
        // 检查定时任务
        checkTimerTasks();
        
        // 执行下一个任务
        executeNextTask();
        
        // 处理任务超时
        handleTaskTimeouts();
        
        // 重试失败的任务
        retryFailedTasks();
        
        // 清理已完成的任务
        cleanupCompletedTasks();
        
        xSemaphoreGive(schedulerMutex);
    }
}

String TaskScheduler::addTask(const String& name, TaskType type, uint8_t priority,
                             std::function<bool()> executor, 
                             std::function<void(bool)> callback,
                             uint32_t timeout) {
    if (!isRunning) {
        return "";
    }
    
    ScheduledTask* task = new ScheduledTask();
    task->id = generateTaskId();
    task->name = name;
    task->type = type;
    task->priority = priority;
    task->status = PENDING;
    task->createTime = millis();
    task->scheduleTime = millis();  // 立即执行
    task->timeout = timeout;
    task->executor = executor;
    task->callback = callback;
    
    if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        taskQueue.push(task);
        xSemaphoreGive(schedulerMutex);
        
        Serial.printf("📝 添加任务: %s (ID: %s, 优先级: %d)\n", 
                      name.c_str(), task->id.c_str(), priority);
        
        // 发送任务创建事件
        JsonDocument eventData;
        eventData["name"] = name;
        eventData["type"] = type;
        eventData["priority"] = priority;
        sendTaskEvent("task_created", task->id, eventData);
        
        return task->id;
    }
    
    delete task;
    return "";
}

String TaskScheduler::addTimerTask(const String& name, TaskType type, uint8_t priority,
                                  uint64_t scheduleTime, std::function<bool()> executor,
                                  std::function<void(bool)> callback) {
    if (!isRunning) {
        return "";
    }
    
    ScheduledTask* task = new ScheduledTask();
    task->id = generateTaskId();
    task->name = name;
    task->type = type;
    task->priority = priority;
    task->status = PENDING;
    task->createTime = millis();
    task->scheduleTime = scheduleTime;
    task->executor = executor;
    task->callback = callback;
    
    if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        timerTasks.push_back(task);
        xSemaphoreGive(schedulerMutex);
        
        Serial.printf("⏰ 添加定时任务: %s (ID: %s, 计划时间: %llu)\n", 
                      name.c_str(), task->id.c_str(), scheduleTime);
        
        return task->id;
    }
    
    delete task;
    return "";
}

String TaskScheduler::addSignalEmitTask(const std::vector<String>& signalIds, 
                                       uint8_t priority,
                                       bool isLoopMode,
                                       uint32_t emitRate,
                                       uint32_t intervalRate) {
    if (signalIds.empty()) {
        return "";
    }
    
    String taskName = "信号发射任务 (" + String(signalIds.size()) + "个信号)";
    
    // 创建任务执行函数
    auto executor = [signalIds, isLoopMode, emitRate, intervalRate]() -> bool {
        SystemManager& systemManager = SystemManager::getInstance();
        IRSignalManager* irManager = systemManager.getIRManager();
        
        if (!irManager) {
            return false;
        }
        
        bool allSuccess = true;
        
        do {
            for (const String& signalId : signalIds) {
                // 发射信号
                bool success = irManager->emitSignal(signalId);
                if (!success) {
                    allSuccess = false;
                }
                
                // 等待间隔
                if (intervalRate > 0) {
                    vTaskDelay(pdMS_TO_TICKS(intervalRate));
                }
            }
            
            // 循环间隔
            if (isLoopMode && emitRate > 0) {
                vTaskDelay(pdMS_TO_TICKS(emitRate));
            }
            
        } while (isLoopMode);
        
        return allSuccess;
    };
    
    // 创建完成回调
    auto callback = [signalIds](bool success) {
        SystemManager& systemManager = SystemManager::getInstance();
        
        JsonDocument eventData;
        eventData["success"] = success;
        eventData["signal_count"] = signalIds.size();
        
        JsonArray signalsArray = eventData["signals"].to<JsonArray>();
        for (const String& signalId : signalIds) {
            signalsArray.add(signalId);
        }
        
        systemManager.sendSystemEvent("signal_emit_completed", eventData);
    };
    
    return addTask(taskName, SIGNAL_EMIT, priority, executor, callback);
}

String TaskScheduler::addBatchTask(const JsonArray& operations, uint8_t priority) {
    if (operations.size() == 0) {
        return "";
    }
    
    String taskName = "批量操作任务 (" + String(operations.size()) + "个操作)";
    
    // 创建任务执行函数
    auto executor = [operations]() -> bool {
        SystemManager& systemManager = SystemManager::getInstance();
        bool allSuccess = true;
        
        for (JsonVariant operation : operations) {
            String type = operation["type"].as<String>();
            
            if (type == "emit") {
                String signalId = operation["signalId"].as<String>();
                IRSignalManager* irManager = systemManager.getIRManager();
                if (irManager) {
                    bool success = irManager->emitSignal(signalId);
                    if (!success) {
                        allSuccess = false;
                    }
                }
            } else if (type == "delay") {
                uint32_t delayMs = operation["delay"].as<uint32_t>();
                vTaskDelay(pdMS_TO_TICKS(delayMs));
            }
            // 可以添加更多操作类型
        }
        
        return allSuccess;
    };
    
    return addTask(taskName, BATCH_OPERATION, priority, executor);
}

bool TaskScheduler::cancelTask(const String& taskId) {
    if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        ScheduledTask* task = findTask(taskId);
        if (task && task->status != COMPLETED && task->status != FAILED) {
            task->status = CANCELLED;
            task->endTime = millis();
            
            Serial.printf("❌ 取消任务: %s\n", taskId.c_str());
            
            // 发送任务取消事件
            sendTaskEvent("task_cancelled", taskId);
            
            xSemaphoreGive(schedulerMutex);
            return true;
        }
        xSemaphoreGive(schedulerMutex);
    }
    
    return false;
}

bool TaskScheduler::pauseTask(const String& taskId) {
    if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        ScheduledTask* task = findTask(taskId);
        if (task && task->status == RUNNING) {
            task->status = PAUSED;
            
            Serial.printf("⏸️ 暂停任务: %s\n", taskId.c_str());
            
            // 发送任务暂停事件
            sendTaskEvent("task_paused", taskId);
            
            xSemaphoreGive(schedulerMutex);
            return true;
        }
        xSemaphoreGive(schedulerMutex);
    }
    
    return false;
}

bool TaskScheduler::resumeTask(const String& taskId) {
    if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        ScheduledTask* task = findTask(taskId);
        if (task && task->status == PAUSED) {
            task->status = PENDING;
            
            Serial.printf("▶️ 恢复任务: %s\n", taskId.c_str());
            
            // 发送任务恢复事件
            sendTaskEvent("task_resumed", taskId);
            
            xSemaphoreGive(schedulerMutex);
            return true;
        }
        xSemaphoreGive(schedulerMutex);
    }

    return false;
}

TaskData TaskScheduler::getTaskStatus(const String& taskId) const {
    TaskData taskData;

    if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        ScheduledTask* task = const_cast<TaskScheduler*>(this)->findTask(taskId);
        if (task) {
            taskData = convertToTaskData(task);
        }
        xSemaphoreGive(schedulerMutex);
    }

    return taskData;
}

std::vector<TaskData> TaskScheduler::getAllTasks() const {
    std::vector<TaskData> tasks;

    if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        // 添加队列中的任务
        std::priority_queue<ScheduledTask*, std::vector<ScheduledTask*>,
                           std::function<bool(ScheduledTask*, ScheduledTask*)>> tempQueue = taskQueue;
        while (!tempQueue.empty()) {
            tasks.push_back(convertToTaskData(tempQueue.top()));
            tempQueue.pop();
        }

        // 添加运行中的任务
        for (const auto* task : runningTasks) {
            tasks.push_back(convertToTaskData(task));
        }

        // 添加定时任务
        for (const auto* task : timerTasks) {
            tasks.push_back(convertToTaskData(task));
        }

        xSemaphoreGive(schedulerMutex);
    }

    return tasks;
}

std::vector<TaskData> TaskScheduler::getRunningTasks() const {
    std::vector<TaskData> tasks;

    if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        for (const auto* task : runningTasks) {
            tasks.push_back(convertToTaskData(task));
        }
        xSemaphoreGive(schedulerMutex);
    }

    return tasks;
}

void TaskScheduler::clearTaskHistory() {
    if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        for (auto* task : taskHistory) {
            delete task;
        }
        taskHistory.clear();
        xSemaphoreGive(schedulerMutex);

        Serial.println("🗑️ 任务历史已清空");
    }
}

std::vector<String> TaskScheduler::pauseLowPriorityTasks(uint8_t minPriority) {
    std::vector<String> pausedTaskIds;

    if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        for (auto* task : runningTasks) {
            if (task->priority < minPriority && task->status == RUNNING) {
                task->status = PAUSED;
                pausedTaskIds.push_back(task->id);

                Serial.printf("⏸️ 暂停低优先级任务: %s (优先级: %d)\n",
                              task->id.c_str(), task->priority);
            }
        }
        xSemaphoreGive(schedulerMutex);
    }

    return pausedTaskIds;
}

void TaskScheduler::resumePausedTasks(const std::vector<String>& taskIds) {
    if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        for (const String& taskId : taskIds) {
            ScheduledTask* task = findTask(taskId);
            if (task && task->status == PAUSED) {
                task->status = PENDING;

                Serial.printf("▶️ 恢复暂停的任务: %s\n", taskId.c_str());
            }
        }
        xSemaphoreGive(schedulerMutex);
    }
}

bool TaskScheduler::setTaskPriority(const String& taskId, uint8_t priority) {
    if (priority < 1 || priority > 4) {
        return false;
    }

    if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        ScheduledTask* task = findTask(taskId);
        if (task) {
            task->priority = priority;

            Serial.printf("🔄 更新任务优先级: %s -> %d\n", taskId.c_str(), priority);

            xSemaphoreGive(schedulerMutex);
            return true;
        }
        xSemaphoreGive(schedulerMutex);
    }

    return false;
}

JsonDocument TaskScheduler::getSchedulerStats() const {
    JsonDocument stats;

    if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        stats["is_running"] = isRunning;
        stats["queue_size"] = taskQueue.size();
        stats["running_tasks"] = runningTasks.size();
        stats["timer_tasks"] = timerTasks.size();
        stats["history_size"] = taskHistory.size();
        stats["total_executed"] = totalTasksExecuted;
        stats["total_failed"] = totalTasksFailed;
        stats["max_concurrent"] = maxConcurrentTasks;

        xSemaphoreGive(schedulerMutex);
    }

    return stats;
}

void TaskScheduler::sendTaskEvent(const String& eventType, const String& taskId,
                                 const JsonDocument& eventData) {
    SystemManager& systemManager = SystemManager::getInstance();

    JsonDocument fullEventData;
    fullEventData["task_id"] = taskId;
    fullEventData["event_type"] = eventType;
    fullEventData["timestamp"] = millis();

    // 合并额外的事件数据
    for (JsonPairConst kv : eventData.as<JsonObjectConst>()) {
        fullEventData[kv.key()] = kv.value();
    }

    systemManager.sendSystemEvent("task_event", fullEventData);
}

// ==================== 私有方法实现 ====================

void TaskScheduler::executeNextTask() {
    if (runningTasks.size() >= maxConcurrentTasks || taskQueue.empty()) {
        return;
    }

    ScheduledTask* task = taskQueue.top();
    taskQueue.pop();

    if (task->status == CANCELLED) {
        delete task;
        return;
    }

    // 检查是否到了执行时间
    if (millis() < task->scheduleTime) {
        taskQueue.push(task);  // 重新放回队列
        return;
    }

    task->status = RUNNING;
    task->startTime = millis();
    runningTasks.push_back(task);

    Serial.printf("🚀 开始执行任务: %s\n", task->name.c_str());

    // 发送任务开始事件
    sendTaskEvent("task_started", task->id);

    // 在新任务中执行
    auto taskWrapper = [this, task]() {
        bool success = false;

        try {
            if (task->executor) {
                success = task->executor();
            }
        } catch (...) {
            success = false;
            Serial.printf("❌ 任务执行异常: %s\n", task->id.c_str());
        }

        // 更新任务状态
        if (xSemaphoreTake(schedulerMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
            task->endTime = millis();
            task->status = success ? COMPLETED : FAILED;

            if (success) {
                totalTasksExecuted++;
                Serial.printf("✅ 任务完成: %s\n", task->name.c_str());
            } else {
                totalTasksFailed++;
                Serial.printf("❌ 任务失败: %s\n", task->name.c_str());
            }

            // 调用完成回调
            if (task->callback) {
                task->callback(success);
            }

            // 发送任务完成事件
            JsonDocument eventData;
            eventData["success"] = success;
            eventData["duration"] = task->endTime - task->startTime;
            sendTaskEvent(success ? "task_completed" : "task_failed", task->id, eventData);

            xSemaphoreGive(schedulerMutex);
        }
    };

    // 创建任务执行线程
    xTaskCreate([](void* param) {
        auto wrapper = static_cast<std::function<void()>*>(param);
        (*wrapper)();
        delete wrapper;
        vTaskDelete(NULL);
    }, "TaskExecution", 4096, new std::function<void()>(taskWrapper), 1, NULL);
}

void TaskScheduler::checkTimerTasks() {
    uint64_t currentTime = millis();

    for (auto it = timerTasks.begin(); it != timerTasks.end();) {
        ScheduledTask* task = *it;

        if (currentTime >= task->scheduleTime && task->status == PENDING) {
            // 将定时任务移到执行队列
            taskQueue.push(task);
            it = timerTasks.erase(it);

            Serial.printf("⏰ 定时任务到期: %s\n", task->name.c_str());
        } else {
            ++it;
        }
    }
}

void TaskScheduler::cleanupCompletedTasks() {
    for (auto it = runningTasks.begin(); it != runningTasks.end();) {
        ScheduledTask* task = *it;

        if (task->status == COMPLETED || task->status == FAILED || task->status == CANCELLED) {
            // 移到历史记录
            taskHistory.push_back(task);
            it = runningTasks.erase(it);

            // 限制历史记录大小
            if (taskHistory.size() > 100) {
                delete taskHistory.front();
                taskHistory.erase(taskHistory.begin());
            }
        } else {
            ++it;
        }
    }
}

void TaskScheduler::handleTaskTimeouts() {
    uint64_t currentTime = millis();

    for (auto* task : runningTasks) {
        if (task->status == RUNNING &&
            (currentTime - task->startTime) > task->timeout) {

            task->status = FAILED;
            task->endTime = currentTime;
            totalTasksFailed++;

            Serial.printf("⏱️ 任务超时: %s (运行时间: %llu ms)\n",
                          task->name.c_str(), currentTime - task->startTime);

            // 发送超时事件
            JsonDocument eventData;
            eventData["reason"] = "timeout";
            eventData["duration"] = currentTime - task->startTime;
            sendTaskEvent("task_failed", task->id, eventData);
        }
    }
}

void TaskScheduler::retryFailedTasks() {
    for (auto* task : runningTasks) {
        if (task->status == FAILED && task->retryCount < task->maxRetries) {
            task->retryCount++;
            task->status = PENDING;
            task->scheduleTime = millis() + (1000 * task->retryCount);  // 指数退避

            Serial.printf("🔄 重试任务: %s (第%d次重试)\n",
                          task->name.c_str(), task->retryCount);

            // 重新加入队列
            taskQueue.push(task);
        }
    }
}

String TaskScheduler::generateTaskId() {
    static uint32_t taskCounter = 0;
    taskCounter++;
    return "task_" + String(millis()) + "_" + String(taskCounter);
}

TaskData TaskScheduler::convertToTaskData(const ScheduledTask* task) const {
    TaskData taskData;

    taskData.id = task->id;
    taskData.name = task->name;

    // 转换任务类型
    switch (task->type) {
        case SIGNAL_EMIT:
            taskData.type = "signal_emit";
            break;
        case SIGNAL_LEARNING:
            taskData.type = "signal_learning";
            break;
        case BATCH_OPERATION:
            taskData.type = "batch_operation";
            break;
        case TIMER_TASK:
            taskData.type = "timer_task";
            break;
        case SYSTEM_MAINTENANCE:
            taskData.type = "system_maintenance";
            break;
        default:
            taskData.type = "unknown";
            break;
    }

    taskData.priority = task->priority;

    // 转换任务状态
    switch (task->status) {
        case PENDING:
            taskData.status = "pending";
            break;
        case RUNNING:
            taskData.status = "running";
            break;
        case PAUSED:
            taskData.status = "paused";
            break;
        case COMPLETED:
            taskData.status = "completed";
            break;
        case FAILED:
            taskData.status = "failed";
            break;
        case CANCELLED:
            taskData.status = "cancelled";
            break;
        default:
            taskData.status = "unknown";
            break;
    }

    taskData.created = task->createTime;
    taskData.started = task->startTime;
    taskData.completed = task->endTime;
    taskData.config = task->taskData;

    return taskData;
}

TaskScheduler::ScheduledTask* TaskScheduler::findTask(const String& taskId) {
    // 在队列中查找
    std::priority_queue<ScheduledTask*, std::vector<ScheduledTask*>,
                       std::function<bool(ScheduledTask*, ScheduledTask*)>> tempQueue = taskQueue;
    while (!tempQueue.empty()) {
        ScheduledTask* task = tempQueue.top();
        tempQueue.pop();
        if (task->id == taskId) {
            return task;
        }
    }

    // 在运行中的任务中查找
    for (auto* task : runningTasks) {
        if (task->id == taskId) {
            return task;
        }
    }

    // 在定时任务中查找
    for (auto* task : timerTasks) {
        if (task->id == taskId) {
            return task;
        }
    }

    // 在历史记录中查找
    for (auto* task : taskHistory) {
        if (task->id == taskId) {
            return task;
        }
    }

    return nullptr;
}

void TaskScheduler::schedulerTask(void* parameter) {
    TaskScheduler* scheduler = static_cast<TaskScheduler*>(parameter);

    while (scheduler->isRunning) {
        scheduler->loop();
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    vTaskDelete(NULL);
}

bool TaskScheduler::taskPriorityCompare(ScheduledTask* a, ScheduledTask* b) {
    // 优先级高的任务排在前面
    if (a->priority != b->priority) {
        return a->priority < b->priority;
    }

    // 优先级相同时，按计划时间排序
    return a->scheduleTime > b->scheduleTime;
}
