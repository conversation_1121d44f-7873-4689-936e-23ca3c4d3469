<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            margin: 5px;
        }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .tab-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border: none;
            background: #f8f9fa;
            color: #6c757d;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
        }
        .tab-btn:hover {
            background: #e9ecef;
            color: #333;
        }
        .tab-btn.active {
            background: #f5f5f5;
            color: #007bff;
            border-bottom: 2px solid #007bff;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔘 按钮功能测试</h1>
        
        <h2>基础按钮测试</h2>
        <button class="btn btn-primary" onclick="testClick('primary')">主要按钮</button>
        <button class="btn btn-secondary" onclick="testClick('secondary')">次要按钮</button>
        
        <h2>标签页按钮测试</h2>
        <button class="tab-btn active" data-module="test1" onclick="testTab(this, 'test1')">
            <span>📡</span>测试标签1
        </button>
        <button class="tab-btn" data-module="test2" onclick="testTab(this, 'test2')">
            <span>🎮</span>测试标签2
        </button>
        <button class="tab-btn" data-module="test3" onclick="testTab(this, 'test3')">
            <span>⏰</span>测试标签3
        </button>
        
        <h2>事件监听器测试</h2>
        <button class="btn btn-primary" id="event-test-btn">事件监听器按钮</button>
        
        <h2>CSS样式测试</h2>
        <p>请检查按钮是否有以下效果：</p>
        <ul>
            <li>鼠标悬停时有颜色变化</li>
            <li>鼠标悬停时有轻微上移效果</li>
            <li>鼠标悬停时有阴影效果</li>
            <li>标签页按钮点击后变为激活状态</li>
        </ul>
        
        <div id="result" class="result">
            等待测试...
        </div>
    </div>

    <script>
        let testResults = [];
        
        function updateResult(message, type = 'info') {
            const result = document.getElementById('result');
            testResults.push(`[${new Date().toLocaleTimeString()}] ${message}`);
            result.innerHTML = testResults.join('<br>');
            result.className = `result ${type}`;
        }
        
        function testClick(type) {
            updateResult(`✅ ${type}按钮点击成功`, 'success');
        }
        
        function testTab(element, module) {
            // 移除所有active类
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 添加active类到当前按钮
            element.classList.add('active');
            
            updateResult(`✅ 标签页切换成功: ${module}`, 'success');
        }
        
        // 测试事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const eventBtn = document.getElementById('event-test-btn');
            if (eventBtn) {
                eventBtn.addEventListener('click', function() {
                    updateResult('✅ 事件监听器工作正常', 'success');
                });
                updateResult('📋 页面加载完成，事件监听器已绑定');
            } else {
                updateResult('❌ 无法找到事件测试按钮', 'error');
            }
        });
        
        // 测试CSS加载
        window.addEventListener('load', function() {
            const btn = document.querySelector('.btn-primary');
            const computedStyle = window.getComputedStyle(btn);
            const backgroundColor = computedStyle.backgroundColor;
            
            if (backgroundColor === 'rgb(0, 123, 255)' || backgroundColor.includes('123')) {
                updateResult('✅ CSS样式加载正常', 'success');
            } else {
                updateResult(`⚠️ CSS样式可能有问题，背景色: ${backgroundColor}`, 'error');
            }
        });
        
        updateResult('🚀 测试页面初始化完成');
    </script>
</body>
</html>
