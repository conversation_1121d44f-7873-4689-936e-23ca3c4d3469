/**
 * @file main.cpp
 * @brief ESP32-S3红外控制系统主程序
 * @details 系统入口点，初始化所有组件并启动主循环
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <WiFi.h>
#include "config/config.h"
#include "core/system_manager.h"
#include "core/time_manager.h"

// 全局系统管理器引用
SystemManager* systemManager = nullptr;

// 时间管理器在time_manager.cpp中定义
extern TimeManager* timeManager;

/**
 * @brief Arduino setup函数 - 系统初始化
 */
void setup() {
    // 初始化串口通信
    Serial.begin(115200);
    delay(1000);

    // 全局WiFi稳定性配置 - 在系统管理器初始化之前设置
    WiFi.persistent(false);  // 禁用WiFi配置持久化
    WiFi.setAutoConnect(false);  // 禁用自动连接
    WiFi.setAutoReconnect(false);  // 禁用自动重连
    WiFi.setSleep(false);  // 禁用WiFi睡眠模式

    // 设置WiFi发射功率，避免过热导致的不稳定
    WiFi.setTxPower(WIFI_POWER_19_5dBm);

    // 打印启动信息
    String separator = "";
    for(int i = 0; i < 50; i++) separator += "=";
    Serial.println("\n" + separator);
    Serial.println("🚀 ESP32-S3红外控制系统启动");
    Serial.println("版本: " + String(FIRMWARE_VERSION));
    Serial.println("构建日期: " + String(BUILD_DATE) + " " + String(BUILD_TIME));
    Serial.println("芯片型号: " + String(ESP.getChipModel()));
    Serial.println("芯片版本: " + String(ESP.getChipRevision()));
    Serial.println("Flash大小: " + String(ESP.getFlashChipSize() / (1024 * 1024)) + "MB");
    Serial.println("SRAM大小: " + String(ESP.getHeapSize() / 1024) + "KB");
    Serial.println("CPU频率: " + String(ESP.getCpuFreqMHz()) + "MHz");
    Serial.println("MAC地址: " + WiFi.macAddress());
    Serial.println(separator + "\n");
    
    // 初始化时间管理器（不立即同步NTP）
    timeManager = TimeManager::getInstance();
    timeManager->begin();

    // 获取系统管理器实例
    systemManager = &SystemManager::getInstance();

    // 初始化系统
    if (!systemManager->begin()) {
        Serial.println("💥 系统初始化失败！");
        Serial.println("🔄 3秒后重启...");
        delay(3000);
        ESP.restart();
    }

    // 系统初始化完成后，尝试NTP同步
    if (timeManager) {
        timeManager->syncWithNTP();
    }
    
    Serial.println("🎉 系统初始化完成，进入主循环");
}

/**
 * @brief Arduino loop函数 - 主循环
 */
void loop() {
    // 更新时间管理器
    if (timeManager) {
        timeManager->loop();
    }

    // 检查系统管理器是否有效
    if (systemManager && systemManager->isReady()) {
        // 执行系统主循环
        systemManager->loop();
    } else {
        // 系统未就绪，等待或重启
        Serial.println("⚠️ 系统未就绪，等待初始化...");
        delay(1000);
        
        // 如果等待时间过长，重启系统
        static uint32_t waitStartTime = millis();
        if (millis() - waitStartTime > 120000) {  // 增加到120秒超时
            Serial.println("💥 系统初始化超时，重启系统");
            ESP.restart();
        }
    }
    
    // 让出CPU时间给其他任务
    yield();
}
