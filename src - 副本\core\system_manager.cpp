#include "system_manager.h"
#include "network/wifi_manager.h"
#include "network/http_server.h"
#include "network/websocket_manager.h"
#include "ir/ir_manager.h"
#include "storage/signal_storage.h"
#include "monitor/system_monitor.h"
#include "timer_manager_backend.h"
#include "utils/json_utils.h"

// 静态成员初始化
SystemManager* SystemManager::instance = nullptr;

SystemManager::SystemManager() 
    : currentState(UNINITIALIZED)
    , bootTime(0)
    , lastHeartbeat(0)
    , wifiManager(nullptr)
    , httpServer(nullptr)
    , wsManager(nullptr)
    , irManager(nullptr)
    , signalStorage(nullptr)
    , systemMonitor(nullptr)
    , timerManager(nullptr)
    , networkTaskHandle(nullptr)
    , irTaskHandle(nullptr)
    , systemTaskHandle(nullptr)
    , storageTaskHandle(nullptr)
    , systemMutex(nullptr)
    , configMutex(nullptr)
    , eventQueue(nullptr)
    , isConfigLoaded(false)
    , lastErrorTime(0)
    , consecutiveErrors(0) {
}

SystemManager& SystemManager::getInstance() {
    if (!instance) {
        instance = new SystemManager();
    }
    return *instance;
}

SystemManager::~SystemManager() {
    shutdown();
}

bool SystemManager::begin() {
    Serial.println("🚀 ESP32-S3红外控制系统启动中...");
    
    currentState = INITIALIZING;
    bootTime = millis();
    
    // 1. 初始化硬件
    if (!initializeHardware()) {
        logError("硬件初始化失败", "SystemManager::begin");
        currentState = ERROR;
        return false;
    }
    
    // 2. 初始化文件系统
    if (!initializeFileSystem()) {
        logError("文件系统初始化失败", "SystemManager::begin");
        currentState = ERROR;
        return false;
    }
    
    // 3. 加载系统配置
    if (!loadConfig()) {
        logWarning("配置加载失败，使用默认配置", "SystemManager::begin");
    }
    
    // 4. 创建同步原语
    systemMutex = xSemaphoreCreateMutex();
    configMutex = xSemaphoreCreateMutex();
    eventQueue = xQueueCreate(20, sizeof(String*));
    
    if (!systemMutex || !configMutex || !eventQueue) {
        logError("同步原语创建失败", "SystemManager::begin");
        currentState = ERROR;
        return false;
    }
    
    // 5. 初始化组件
    if (!initializeComponents()) {
        logError("组件初始化失败", "SystemManager::begin");
        currentState = ERROR;
        return false;
    }
    
    // 6. 创建系统任务
    if (!createSystemTasks()) {
        logError("系统任务创建失败", "SystemManager::begin");
        currentState = ERROR;
        return false;
    }
    
    // 7. 系统自检
    if (!performSelfTest()) {
        logError("系统自检失败", "SystemManager::begin");
        currentState = ERROR;
        return false;
    }
    
    // 8. 启用看门狗
    enableWatchdog();
    
    currentState = RUNNING;
    lastHeartbeat = millis();
    
    Serial.println("✅ ESP32-S3红外控制系统启动完成");

    // 根据WiFi模式显示正确的IP地址
    IPAddress serverIP;
    String modeStr;
    if (WiFi.getMode() == WIFI_AP || WiFi.getMode() == WIFI_AP_STA) {
        serverIP = WiFi.softAPIP();
        modeStr = "AP模式";
    } else if (WiFi.getMode() == WIFI_STA && WiFi.isConnected()) {
        serverIP = WiFi.localIP();
        modeStr = "STA模式";
    } else {
        serverIP = IPAddress(192, 168, 4, 1);  // 默认AP IP
        modeStr = "默认";
    }

    Serial.printf("🌐 HTTP服务器 (%s): http://%s:%d\n", modeStr.c_str(), serverIP.toString().c_str(), HTTP_PORT);
    Serial.printf("🔌 WebSocket服务器 (%s): ws://%s:%d/ws\n", modeStr.c_str(), serverIP.toString().c_str(), WEBSOCKET_PORT);
    Serial.printf("💾 可用内存: %d bytes\n", getFreeHeap());
    Serial.printf("🔥 芯片温度: %.1f°C\n", getChipTemperature());
    
    return true;
}

void SystemManager::loop() {
    if (currentState != RUNNING) {
        return;
    }
    
    uint32_t currentTime = millis();
    
    // 更新心跳
    lastHeartbeat = currentTime;
    
    // 处理系统事件
    processSystemEvents();
    
    // 喂看门狗
    feedWatchdog("MainLoop");
    
    // 检查内存状态
    uint32_t freeHeap = getFreeHeap();
    if (freeHeap < MIN_FREE_HEAP) {
        logWarning("内存不足: " + String(freeHeap) + " bytes", "SystemManager::loop");
    }
    
    // 移除定期状态更新轮询 - 改为事件驱动
    // 只在状态真正发生变化时才发送更新事件
    
    // 让出CPU时间
    vTaskDelay(pdMS_TO_TICKS(10));
}

void SystemManager::shutdown() {
    Serial.println("🛑 系统关闭中...");
    
    currentState = SHUTDOWN;
    
    // 停止所有任务
    if (networkTaskHandle) {
        vTaskDelete(networkTaskHandle);
        networkTaskHandle = nullptr;
    }
    if (irTaskHandle) {
        vTaskDelete(irTaskHandle);
        irTaskHandle = nullptr;
    }
    if (systemTaskHandle) {
        vTaskDelete(systemTaskHandle);
        systemTaskHandle = nullptr;
    }
    if (storageTaskHandle) {
        vTaskDelete(storageTaskHandle);
        storageTaskHandle = nullptr;
    }
    
    // 关闭组件
    if (httpServer) {
        delete httpServer;
        httpServer = nullptr;
    }
    if (wsManager) {
        delete wsManager;
        wsManager = nullptr;
    }
    if (wifiManager) {
        delete wifiManager;
        wifiManager = nullptr;
    }
    if (irManager) {
        delete irManager;
        irManager = nullptr;
    }
    if (signalStorage) {
        delete signalStorage;
        signalStorage = nullptr;
    }
    if (systemMonitor) {
        delete systemMonitor;
        systemMonitor = nullptr;
    }
    
    // 清理同步原语
    if (systemMutex) {
        vSemaphoreDelete(systemMutex);
        systemMutex = nullptr;
    }
    if (configMutex) {
        vSemaphoreDelete(configMutex);
        configMutex = nullptr;
    }
    if (eventQueue) {
        vQueueDelete(eventQueue);
        eventQueue = nullptr;
    }
    
    Serial.println("✅ 系统关闭完成");
}

void SystemManager::restart(const String& reason) {
    logInfo("系统重启: " + reason, "SystemManager::restart");
    
    // 保存配置
    saveConfig();
    
    // 关闭系统
    shutdown();
    
    // 延迟后重启
    delay(1000);
    ESP.restart();
}

SystemStatus SystemManager::getSystemStatus() const {
    SystemStatus status;
    
    status.uptime = getUptime();
    status.memory_usage = getMemoryUsage();
    status.free_heap = getFreeHeap();
    status.chip_temperature = getChipTemperature();
    
    // WiFi信号强度
    if (WiFi.isConnected()) {
        status.wifi_strength = WiFi.RSSI();
    } else {
        status.wifi_strength = 0;
    }
    
    // 信号数量
    if (signalStorage) {
        status.signal_count = signalStorage->getSignalCount();
    } else {
        status.signal_count = 0;
    }
    
    return status;
}

uint32_t SystemManager::getUptime() const {
    return (millis() - bootTime) / 1000;
}

float SystemManager::getMemoryUsage() const {
    uint32_t totalHeap = ESP.getHeapSize();
    uint32_t freeHeap = ESP.getFreeHeap();
    return ((float)(totalHeap - freeHeap) / totalHeap) * 100.0;
}

float SystemManager::getChipTemperature() const {
    // ESP32-S3温度传感器读取
    return temperatureRead();
}

bool SystemManager::loadConfig() {
    if (!LittleFS.exists(SYSTEM_CONFIG_FILE)) {
        logInfo("配置文件不存在，创建默认配置", "SystemManager::loadConfig");
        return resetConfig();
    }
    
    File configFile = LittleFS.open(SYSTEM_CONFIG_FILE, "r");
    if (!configFile) {
        logError("无法打开配置文件", "SystemManager::loadConfig");
        return false;
    }
    
    DeserializationError error = deserializeJson(systemConfig, configFile);
    configFile.close();
    
    if (error) {
        logError("配置文件解析失败: " + String(error.c_str()), "SystemManager::loadConfig");
        return false;
    }
    
    isConfigLoaded = true;
    logInfo("配置加载成功", "SystemManager::loadConfig");
    return true;
}

bool SystemManager::saveConfig() {
    if (!isConfigLoaded) {
        return false;
    }
    
    File configFile = LittleFS.open(SYSTEM_CONFIG_FILE, "w");
    if (!configFile) {
        logError("无法创建配置文件", "SystemManager::saveConfig");
        return false;
    }
    
    if (serializeJson(systemConfig, configFile) == 0) {
        configFile.close();
        logError("配置文件写入失败", "SystemManager::saveConfig");
        return false;
    }
    
    configFile.close();
    logInfo("配置保存成功", "SystemManager::saveConfig");
    return true;
}

bool SystemManager::resetConfig() {
    systemConfig.clear();
    
    // 设置默认配置
    systemConfig["system"]["name"] = SystemConstants::SYSTEM_NAME;
    systemConfig["system"]["version"] = FIRMWARE_VERSION;
    systemConfig["system"]["debug_level"] = DEBUG_LEVEL;
    
    systemConfig["network"]["http_port"] = HTTP_PORT;
    systemConfig["network"]["websocket_port"] = WEBSOCKET_PORT;
    systemConfig["network"]["wifi_timeout"] = WIFI_CONNECT_TIMEOUT;
    
    systemConfig["ir"]["frequency"] = IR_FREQUENCY;
    systemConfig["ir"]["timeout"] = IR_TIMEOUT;
    systemConfig["ir"]["tolerance"] = IR_TOLERANCE;
    
    isConfigLoaded = true;
    return saveConfig();
}

void SystemManager::logError(const String& error, const String& context, uint16_t errorCode) {
    String logEntry = "[ERROR] " + String(millis()) + " - " + error;
    if (!context.isEmpty()) {
        logEntry += " (" + context + ")";
    }
    if (errorCode != 0) {
        logEntry += " [Code: " + String(errorCode) + "]";
    }
    
    Serial.println(logEntry);
    
    errorLog.push_back(logEntry);
    if (errorLog.size() > 100) {  // 限制日志大小
        errorLog.erase(errorLog.begin());
    }
    
    lastErrorTime = millis();
    consecutiveErrors++;
    
    // 发送错误事件
    JsonDocument errorDoc;
    errorDoc["error"] = error;
    errorDoc["context"] = context;
    errorDoc["code"] = errorCode;
    sendSystemEvent("error", errorDoc);
    
    // 检查是否需要重启
    if (consecutiveErrors > 10) {
        handleSystemError("连续错误过多", true);
    }
}

void SystemManager::logWarning(const String& warning, const String& context) {
    String logEntry = "[WARN] " + String(millis()) + " - " + warning;
    if (!context.isEmpty()) {
        logEntry += " (" + context + ")";
    }
    Serial.println(logEntry);
}

void SystemManager::logInfo(const String& info, const String& context) {
    String logEntry = "[INFO] " + String(millis()) + " - " + info;
    if (!context.isEmpty()) {
        logEntry += " (" + context + ")";
    }
    Serial.println(logEntry);
}

void SystemManager::sendSystemEvent(const String& eventType, const JsonDocument& eventData) {
    if (wsManager) {
        WebSocketMessage message = WebSocketMessage::create(eventType, eventData);
        wsManager->broadcastMessage(message.toJsonString());
    }
}

void SystemManager::processSystemEvents() {
    String* eventData;
    while (xQueueReceive(eventQueue, &eventData, 0) == pdTRUE) {
        // 处理事件
        delete eventData;
    }
}

void SystemManager::feedWatchdog(const String& taskName) {
    // ESP32-S3看门狗喂食
    esp_task_wdt_reset();
}

void SystemManager::enableWatchdog(uint32_t timeout) {
    esp_task_wdt_init(timeout, true);
    esp_task_wdt_add(NULL);
}

void SystemManager::disableWatchdog() {
    esp_task_wdt_delete(NULL);
    esp_task_wdt_deinit();
}

// ==================== 私有方法实现 ====================

bool SystemManager::initializeHardware() {
    Serial.begin(115200);
    delay(1000);

    Serial.println("🔧 初始化硬件...");

    // 初始化引脚
    INIT_IR_PINS();
    INIT_STATUS_LEDS();
    INIT_USER_BUTTONS();

    // 设置CPU频率
    setCpuFrequencyMhz(240);

    Serial.printf("✅ 硬件初始化完成 - CPU: %dMHz, Flash: %dMB\n",
                  getCpuFrequencyMhz(), ESP.getFlashChipSize() / (1024 * 1024));

    return true;
}

bool SystemManager::initializeFileSystem() {
    Serial.println("💾 初始化文件系统...");

    // 使用完整参数挂载LittleFS，确保读写模式
    if (!LittleFS.begin(false, "/littlefs", 10, "spiffs")) {
        Serial.println("⚠️ LittleFS挂载失败，尝试格式化...");
        if (!LittleFS.begin(true, "/littlefs", 10, "spiffs")) {
            Serial.println("❌ LittleFS初始化失败");
            return false;
        }
        Serial.println("✅ LittleFS格式化并挂载成功");
    } else {
        Serial.println("✅ LittleFS挂载成功");
    }

    // 创建必要的目录 - 使用递归创建确保成功
    createDirectoryRecursive(SIGNALS_DIR);
    createDirectoryRecursive(CONFIG_DIR);
    createDirectoryRecursive(LOGS_DIR);
    createDirectoryRecursive("/signals/backups");

    // 测试文件创建权限
    Serial.println("🧪 测试文件创建权限...");
    File testFile = LittleFS.open("/test_write.txt", "w");
    if (testFile) {
        testFile.println("LittleFS write test");
        testFile.close();
        LittleFS.remove("/test_write.txt");
        Serial.println("✅ 文件创建权限测试通过");
    } else {
        Serial.println("❌ 文件创建权限测试失败");
        return false;
    }

    Serial.printf("✅ 文件系统初始化完成 - 总空间: %d bytes, 已用: %d bytes\n",
                  LittleFS.totalBytes(), LittleFS.usedBytes());

    return true;
}

bool SystemManager::createDirectoryRecursive(const String& path) {
    if (path.isEmpty() || path == "/") {
        return true;
    }

    if (LittleFS.exists(path)) {
        return true;
    }

    // 获取父目录路径
    int lastSlash = path.lastIndexOf('/');
    if (lastSlash > 0) {
        String parentPath = path.substring(0, lastSlash);
        if (!createDirectoryRecursive(parentPath)) {
            return false;
        }
    }

    // 创建当前目录
    bool success = LittleFS.mkdir(path);
    if (success) {
        Serial.printf("✅ 创建目录: %s\n", path.c_str());
    } else {
        Serial.printf("❌ 创建目录失败: %s\n", path.c_str());
    }

    return success;
}

bool SystemManager::setupDualCoreTasks() {
    return createSystemTasks();
}

bool SystemManager::createSystemTasks() {
    Serial.println("⚙️ 创建系统任务...");

    // 网络任务 (Core 0)
    BaseType_t result = xTaskCreatePinnedToCore(
        networkTask,
        "NetworkTask",
        NETWORK_TASK_STACK,
        this,
        NETWORK_TASK_PRIORITY,
        &networkTaskHandle,
        NETWORK_TASK_CORE
    );
    if (result != pdPASS) {
        Serial.println("❌ 网络任务创建失败");
        return false;
    }

    // 红外任务 (Core 1)
    result = xTaskCreatePinnedToCore(
        irTask,
        "IRTask",
        IR_TASK_STACK,
        this,
        IR_TASK_PRIORITY,
        &irTaskHandle,
        IR_TASK_CORE
    );
    if (result != pdPASS) {
        Serial.println("❌ 红外任务创建失败");
        return false;
    }

    // 系统任务 (Core 0)
    result = xTaskCreatePinnedToCore(
        systemTask,
        "SystemTask",
        SYSTEM_TASK_STACK,
        this,
        SYSTEM_TASK_PRIORITY,
        &systemTaskHandle,
        SYSTEM_TASK_CORE
    );
    if (result != pdPASS) {
        Serial.println("❌ 系统任务创建失败");
        return false;
    }

    // 存储任务 (Core 1)
    result = xTaskCreatePinnedToCore(
        storageTask,
        "StorageTask",
        STORAGE_TASK_STACK,
        this,
        STORAGE_TASK_PRIORITY,
        &storageTaskHandle,
        STORAGE_TASK_CORE
    );
    if (result != pdPASS) {
        Serial.println("❌ 存储任务创建失败");
        return false;
    }

    Serial.println("✅ 系统任务创建完成");
    return true;
}

void SystemManager::stopDualCoreTasks() {
    Serial.println("🛑 停止双核任务...");

    // 停止网络任务
    if (networkTaskHandle) {
        vTaskDelete(networkTaskHandle);
        networkTaskHandle = nullptr;
        Serial.println("✅ 网络任务已停止");
    }

    // 停止红外任务
    if (irTaskHandle) {
        vTaskDelete(irTaskHandle);
        irTaskHandle = nullptr;
        Serial.println("✅ 红外任务已停止");
    }

    // 停止系统任务
    if (systemTaskHandle) {
        vTaskDelete(systemTaskHandle);
        systemTaskHandle = nullptr;
        Serial.println("✅ 系统任务已停止");
    }

    // 停止存储任务
    if (storageTaskHandle) {
        vTaskDelete(storageTaskHandle);
        storageTaskHandle = nullptr;
        Serial.println("✅ 存储任务已停止");
    }

    Serial.println("✅ 双核任务停止完成");
}

JsonDocument SystemManager::getTaskStatus() const {
    JsonDocument doc;

    // 任务状态信息
    doc["total_tasks"] = 4;
    doc["running_tasks"] = 0;

    // 网络任务状态 (Core 0)
    JsonObject networkTask = doc["tasks"]["network"].to<JsonObject>();
    networkTask["name"] = "NetworkTask";
    networkTask["core"] = NETWORK_TASK_CORE;
    networkTask["priority"] = NETWORK_TASK_PRIORITY;
    networkTask["stack_size"] = NETWORK_TASK_STACK;
    networkTask["running"] = (networkTaskHandle != nullptr);
    if (networkTask["running"]) doc["running_tasks"] = doc["running_tasks"].as<int>() + 1;

    // 红外任务状态 (Core 1)
    JsonObject irTask = doc["tasks"]["ir"].to<JsonObject>();
    irTask["name"] = "IRTask";
    irTask["core"] = IR_TASK_CORE;
    irTask["priority"] = IR_TASK_PRIORITY;
    irTask["stack_size"] = IR_TASK_STACK;
    irTask["running"] = (irTaskHandle != nullptr);
    if (irTask["running"]) doc["running_tasks"] = doc["running_tasks"].as<int>() + 1;

    // 系统任务状态 (Core 0)
    JsonObject systemTask = doc["tasks"]["system"].to<JsonObject>();
    systemTask["name"] = "SystemTask";
    systemTask["core"] = SYSTEM_TASK_CORE;
    systemTask["priority"] = SYSTEM_TASK_PRIORITY;
    systemTask["stack_size"] = SYSTEM_TASK_STACK;
    systemTask["running"] = (systemTaskHandle != nullptr);
    if (systemTask["running"]) doc["running_tasks"] = doc["running_tasks"].as<int>() + 1;

    // 存储任务状态 (Core 1)
    JsonObject storageTask = doc["tasks"]["storage"].to<JsonObject>();
    storageTask["name"] = "StorageTask";
    storageTask["core"] = STORAGE_TASK_CORE;
    storageTask["priority"] = STORAGE_TASK_PRIORITY;
    storageTask["stack_size"] = STORAGE_TASK_STACK;
    storageTask["running"] = (storageTaskHandle != nullptr);
    if (storageTask["running"]) doc["running_tasks"] = doc["running_tasks"].as<int>() + 1;

    // 核心分配统计
    doc["core_allocation"]["core_0_tasks"] = 2;  // Network + System
    doc["core_allocation"]["core_1_tasks"] = 2;  // IR + Storage

    return doc;
}

bool SystemManager::initializeComponents() {
    Serial.println("🔌 初始化组件...");

    // 初始化WiFi管理器
    wifiManager = new WiFiManager();
    if (!wifiManager || !wifiManager->begin()) {
        Serial.println("❌ WiFi管理器初始化失败");
        return false;
    }

    // 初始化信号存储 (临时版本)
    signalStorage = new SignalStorage();
    if (!signalStorage || !signalStorage->begin()) {
        Serial.println("❌ 信号存储初始化失败");
        return false;
    }

    // 初始化WebSocket管理器
    wsManager = new WebSocketManager();
    if (!wsManager || !wsManager->begin()) {
        Serial.println("❌ WebSocket管理器初始化失败");
        return false;
    }

    // 初始化红外管理器
    irManager = new IRSignalManager();
    if (!irManager || !irManager->begin()) {
        Serial.println("❌ 红外管理器初始化失败");
        return false;
    }

    // 暂时注释未创建的组件，用于基础测试
    /*
    */

    // 初始化HTTP服务器
    httpServer = new HTTPAPIServer();
    if (!httpServer || !httpServer->begin()) {
        Serial.println("❌ HTTP服务器初始化失败");
        return false;
    }

    // 初始化系统监控器
    systemMonitor = new SystemMonitor();
    if (!systemMonitor || !systemMonitor->begin()) {
        Serial.println("❌ 系统监控器初始化失败");
        return false;
    }

    // 初始化定时器管理器
    timerManager = TimerManagerBackend::getInstance();
    if (!timerManager || !timerManager->begin(signalStorage, irManager)) {
        Serial.println("❌ 定时器管理器初始化失败");
        return false;
    }

    // 设置定时器执行事件回调
    timerManager->setTaskExecutionCallback([this](const String& taskId, const String& taskName, bool success) {
        if (wsManager) {
            wsManager->sendTimerExecutedEvent(taskId, taskName, success);
        }
    });

    Serial.println("✅ 组件初始化完成");
    return true;
}

bool SystemManager::performSelfTest() {
    Serial.println("🔍 执行系统自检...");

    // 检查内存
    if (getFreeHeap() < MIN_FREE_HEAP) {
        Serial.println("❌ 内存不足");
        return false;
    }

    // 检查文件系统
    if (!LittleFS.exists(CONFIG_DIR)) {
        Serial.println("❌ 配置目录不存在");
        return false;
    }

    // 检查WiFi连接
    if (!WiFi.isConnected()) {
        Serial.println("⚠️ WiFi未连接，但系统可以继续运行");
    }

    Serial.println("✅ 系统自检通过");
    return true;
}

void SystemManager::handleSystemError(const String& error, bool isCritical) {
    logError("系统错误: " + error, "SystemManager::handleSystemError");

    if (isCritical) {
        Serial.println("💥 检测到关键错误，系统将重启");
        restart("Critical error: " + error);
    }
}

// ==================== 静态任务函数 ====================

void SystemManager::networkTask(void* parameter) {
    SystemManager* manager = static_cast<SystemManager*>(parameter);

    while (true) {
        if (manager->currentState == RUNNING) {
            // 网络任务处理
            if (manager->wifiManager) {
                manager->wifiManager->loop();
            }
            if (manager->httpServer) {
                manager->httpServer->loop();
            }
            if (manager->wsManager) {
                manager->wsManager->loop();
            }
        }

        manager->feedWatchdog("NetworkTask");
        vTaskDelay(pdMS_TO_TICKS(50));
    }
}

void SystemManager::irTask(void* parameter) {
    SystemManager* manager = static_cast<SystemManager*>(parameter);

    while (true) {
        if (manager->currentState == RUNNING) {
            // 红外任务处理
            if (manager->irManager) {
                manager->irManager->loop();
            }
        }

        manager->feedWatchdog("IRTask");
        vTaskDelay(pdMS_TO_TICKS(10));
    }
}

void SystemManager::systemTask(void* parameter) {
    SystemManager* manager = static_cast<SystemManager*>(parameter);

    while (true) {
        if (manager->currentState == RUNNING) {
            // 系统监控任务
            if (manager->systemMonitor) {
                manager->systemMonitor->loop();
            }

            // 定时器管理任务
            if (manager->timerManager) {
                manager->timerManager->loop();
            }

            // 处理系统事件
            manager->processSystemEvents();
        }

        manager->feedWatchdog("SystemTask");
        vTaskDelay(pdMS_TO_TICKS(100));
    }
}

void SystemManager::storageTask(void* parameter) {
    SystemManager* manager = static_cast<SystemManager*>(parameter);

    while (true) {
        if (manager->currentState == RUNNING) {
            // 存储任务处理
            if (manager->signalStorage) {
                manager->signalStorage->loop();
            }
        }

        manager->feedWatchdog("StorageTask");
        vTaskDelay(pdMS_TO_TICKS(200));
    }
}

String SystemManager::getConfigValue(const String& key, const String& defaultValue) const {
    if (!isConfigLoaded) {
        return defaultValue;
    }

    // 支持点分隔的键路径，如 "network.http_port"
    JsonVariantConst value = systemConfig.as<JsonVariantConst>();
    String currentKey = key;

    int dotIndex = currentKey.indexOf('.');
    while (dotIndex != -1) {
        String section = currentKey.substring(0, dotIndex);
        currentKey = currentKey.substring(dotIndex + 1);
        value = value[section];
        dotIndex = currentKey.indexOf('.');
    }

    value = value[currentKey];
    return value.isNull() ? defaultValue : value.as<String>();
}

bool SystemManager::setConfigValue(const String& key, const String& value) {
    if (!isConfigLoaded) {
        return false;
    }

    // 支持点分隔的键路径
    JsonVariant current = systemConfig.as<JsonVariant>();
    String currentKey = key;

    int dotIndex = currentKey.indexOf('.');
    while (dotIndex != -1) {
        String section = currentKey.substring(0, dotIndex);
        currentKey = currentKey.substring(dotIndex + 1);
        current = current[section];
        dotIndex = currentKey.indexOf('.');
    }

    current[currentKey] = value;
    return true;
}
