#ifndef SYSTEM_MANAGER_H
#define SYSTEM_MANAGER_H

/**
 * @file system_manager.h
 * @brief ESP32-S3系统管理器 - 核心系统控制
 * @details 负责系统初始化、任务调度、状态监控、错误处理
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <WiFi.h>
#include <LittleFS.h>
#include <ArduinoJson.h>
#include <vector>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/semphr.h>
#include <freertos/queue.h>
#include <esp_task_wdt.h>

#include "config/config.h"
#include "config/pins.h"
#include "config/constants.h"
#include "storage/data_structures.h"

// 前向声明
class WiFiManager;
class HTTPAPIServer;
class WebSocketManager;
class IRSignalManager;
class SignalStorage;
class SystemMonitor;
class TimerManagerBackend;

/**
 * @brief 系统管理器 - 单例模式
 * @details 管理整个ESP32-S3系统的生命周期和核心功能
 */
class SystemManager {
private:
    static SystemManager* instance;
    
    // 系统状态
    enum SystemState {
        UNINITIALIZED = 0,
        INITIALIZING = 1,
        RUNNING = 2,
        ERROR = 3,
        SHUTDOWN = 4
    };

    // 任务间通信队列
    QueueHandle_t networkQueue;
    QueueHandle_t irQueue;
    QueueHandle_t storageQueue;
    
    SystemState currentState;
    uint32_t bootTime;
    uint32_t lastHeartbeat;
    
    // 核心组件指针
    WiFiManager* wifiManager;
    HTTPAPIServer* httpServer;
    WebSocketManager* wsManager;
    IRSignalManager* irManager;
    SignalStorage* signalStorage;
    SystemMonitor* systemMonitor;
    TimerManagerBackend* timerManager;
    
    // 任务句柄
    TaskHandle_t networkTaskHandle;
    TaskHandle_t irTaskHandle;
    TaskHandle_t systemTaskHandle;
    TaskHandle_t storageTaskHandle;
    
    // 同步原语
    SemaphoreHandle_t systemMutex;
    SemaphoreHandle_t configMutex;
    QueueHandle_t eventQueue;
    
    // 系统配置
    JsonDocument systemConfig;
    bool isConfigLoaded;
    
    // 错误处理
    std::vector<String> errorLog;
    uint32_t lastErrorTime;
    uint8_t consecutiveErrors;
    
    // 私有构造函数 (单例模式)
    SystemManager();
    
    // 禁用拷贝构造和赋值
    SystemManager(const SystemManager&) = delete;
    SystemManager& operator=(const SystemManager&) = delete;

public:
    /**
     * @brief 获取系统管理器实例
     * @return SystemManager& 系统管理器引用
     */
    static SystemManager& getInstance();
    
    /**
     * @brief 析构函数
     */
    ~SystemManager();
    
    // ==================== 系统生命周期管理 ====================
    
    /**
     * @brief 系统初始化
     * @return bool 初始化是否成功
     */
    bool begin();
    
    /**
     * @brief 系统主循环
     * @details 在main.cpp的loop()中调用
     */
    void loop();
    
    /**
     * @brief 系统关闭
     * @details 优雅关闭所有组件
     */
    void shutdown();
    
    /**
     * @brief 系统重启
     * @param reason 重启原因
     */
    void restart(const String& reason = "Manual restart");

    // ==================== 双核任务管理 ====================

    /**
     * @brief 设置双核任务分配
     * @details 按照架构文档要求分配任务到不同核心
     * @return bool 设置是否成功
     */
    bool setupDualCoreTasks();

    /**
     * @brief 停止双核任务
     * @details 优雅停止所有FreeRTOS任务
     */
    void stopDualCoreTasks();

    /**
     * @brief 获取任务状态信息
     * @return JsonDocument 任务状态JSON
     */
    JsonDocument getTaskStatus() const;
    
    // ==================== 组件管理 ====================
    
    /**
     * @brief 获取WiFi管理器
     * @return WiFiManager* WiFi管理器指针
     */
    WiFiManager* getWiFiManager() const { return wifiManager; }
    
    /**
     * @brief 获取HTTP服务器
     * @return HTTPAPIServer* HTTP服务器指针
     */
    HTTPAPIServer* getHTTPServer() const { return httpServer; }
    
    /**
     * @brief 获取WebSocket管理器
     * @return WebSocketManager* WebSocket管理器指针
     */
    WebSocketManager* getWebSocketManager() const { return wsManager; }
    
    /**
     * @brief 获取红外管理器
     * @return IRSignalManager* 红外管理器指针
     */
    IRSignalManager* getIRManager() const { return irManager; }
    
    /**
     * @brief 获取信号存储管理器
     * @return SignalStorage* 信号存储管理器指针
     */
    SignalStorage* getSignalStorage() const { return signalStorage; }
    
    /**
     * @brief 获取系统监控器
     * @return SystemMonitor* 系统监控器指针
     */
    SystemMonitor* getSystemMonitor() const { return systemMonitor; }

    /**
     * @brief 获取定时器管理器
     * @return TimerManagerBackend* 定时器管理器指针
     */
    TimerManagerBackend* getTimerManager() const { return timerManager; }
    
    // ==================== 状态查询 ====================
    
    /**
     * @brief 获取系统状态
     * @return SystemStatus 系统状态结构
     */
    SystemStatus getSystemStatus() const;
    
    /**
     * @brief 检查系统是否就绪
     * @return bool 系统是否就绪
     */
    bool isReady() const { return currentState == RUNNING; }
    
    /**
     * @brief 获取运行时间
     * @return uint32_t 运行时间(秒)
     */
    uint32_t getUptime() const;
    
    /**
     * @brief 获取可用堆内存
     * @return uint32_t 可用堆内存(字节)
     */
    uint32_t getFreeHeap() const { return ESP.getFreeHeap(); }
    
    /**
     * @brief 获取内存使用率
     * @return float 内存使用率(%)
     */
    float getMemoryUsage() const;
    
    /**
     * @brief 获取芯片温度
     * @return float 芯片温度(°C)
     */
    float getChipTemperature() const;
    
    // ==================== 配置管理 ====================
    
    /**
     * @brief 加载系统配置
     * @return bool 加载是否成功
     */
    bool loadConfig();
    
    /**
     * @brief 保存系统配置
     * @return bool 保存是否成功
     */
    bool saveConfig();

    /**
     * @brief 更新硬件配置
     * @param hwSettings 硬件配置JSON对象
     * @return bool 更新是否成功
     */
    bool updateHardwareConfig(JsonObject hwSettings);

    /**
     * @brief 获取系统配置
     * @return JsonDocument& 系统配置引用
     */
    JsonDocument& getSystemConfig() { return systemConfig; }

    /**
     * @brief 获取配置加载状态
     * @return bool 配置是否已加载
     */
    bool getIsConfigLoaded() const { return isConfigLoaded; }
    
    /**
     * @brief 重置配置为默认值
     * @return bool 重置是否成功
     */
    bool resetConfig();
    
    /**
     * @brief 获取配置值
     * @param key 配置键
     * @param defaultValue 默认值
     * @return String 配置值
     */
    String getConfigValue(const String& key, const String& defaultValue = "") const;
    
    /**
     * @brief 设置配置值
     * @param key 配置键
     * @param value 配置值
     * @return bool 设置是否成功
     */
    bool setConfigValue(const String& key, const String& value);
    
    // ==================== 错误处理 ====================
    
    /**
     * @brief 记录错误
     * @param error 错误信息
     * @param context 错误上下文
     * @param errorCode 错误代码
     */
    void logError(const String& error, const String& context = "", uint16_t errorCode = 0);
    
    /**
     * @brief 记录警告
     * @param warning 警告信息
     * @param context 警告上下文
     */
    void logWarning(const String& warning, const String& context = "");
    
    /**
     * @brief 记录信息
     * @param info 信息内容
     * @param context 信息上下文
     */
    void logInfo(const String& info, const String& context = "");
    
    /**
     * @brief 获取错误日志
     * @return std::vector<String> 错误日志列表
     */
    std::vector<String> getErrorLog() const { return errorLog; }
    
    /**
     * @brief 清空错误日志
     */
    void clearErrorLog() { errorLog.clear(); }
    
    // ==================== 事件处理 ====================
    
    /**
     * @brief 发送系统事件
     * @param eventType 事件类型
     * @param eventData 事件数据
     */
    void sendSystemEvent(const String& eventType, const JsonDocument& eventData);
    
    /**
     * @brief 处理系统事件
     * @details 在系统任务中处理事件队列
     */
    void processSystemEvents();
    
    // ==================== 看门狗管理 ====================
    
    /**
     * @brief 喂看门狗
     * @param taskName 任务名称
     */
    void feedWatchdog(const String& taskName = "");
    
    /**
     * @brief 启用看门狗
     * @param timeout 超时时间(秒)
     */
    void enableWatchdog(uint32_t timeout = SYSTEM_WATCHDOG_TIMEOUT);
    
    /**
     * @brief 禁用看门狗
     */
    void disableWatchdog();

private:
    // ==================== 私有方法 ====================
    
    /**
     * @brief 初始化硬件
     * @return bool 初始化是否成功
     */
    bool initializeHardware();
    
    /**
     * @brief 初始化文件系统
     * @return bool 初始化是否成功
     */
    bool initializeFileSystem();

    /**
     * @brief 递归创建目录
     * @param path 目录路径
     * @return bool 创建是否成功
     */
    bool createDirectoryRecursive(const String& path);
    
    /**
     * @brief 创建系统任务
     * @return bool 创建是否成功
     */
    bool createSystemTasks();
    
    /**
     * @brief 初始化组件
     * @return bool 初始化是否成功
     */
    bool initializeComponents();
    
    /**
     * @brief 系统自检
     * @return bool 自检是否通过
     */
    bool performSelfTest();
    
    /**
     * @brief 处理系统错误
     * @param error 错误信息
     * @param isCritical 是否为关键错误
     */
    void handleSystemError(const String& error, bool isCritical = false);
    
    // ==================== 静态任务函数 ====================
    
    /**
     * @brief 网络任务 (Core 0)
     * @param parameter 任务参数
     */
    static void networkTask(void* parameter);
    
    /**
     * @brief 红外任务 (Core 1)
     * @param parameter 任务参数
     */
    static void irTask(void* parameter);
    
    /**
     * @brief 系统任务 (Core 0)
     * @param parameter 任务参数
     */
    static void systemTask(void* parameter);
    
    /**
     * @brief 存储任务 (Core 1)
     * @param parameter 任务参数
     */
    static void storageTask(void* parameter);
};

#endif // SYSTEM_MANAGER_H
