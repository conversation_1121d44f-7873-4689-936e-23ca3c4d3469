#include "wifi_manager.h"
#include "core/system_manager.h"
#include <esp_wifi.h>
#include <esp_netif.h>

WiFiManager::WiFiManager() 
    : currentState(DISCONNECTED)
    , currentMode(STA_MODE)
    , apSSID(WIFI_SSID)
    , apPassword(WIFI_PASSWORD)
    , apIP(192, 168, 4, 1)
    , apGateway(192, 168, 4, 1)
    , apSubnet(255, 255, 255, 0)
    , apChannel(WIFI_CHANNEL)
    , maxClients(WIFI_MAX_CLIENTS)
    , lastConnectionAttempt(0)
    , connectionTimeout(WIFI_CONNECT_TIMEOUT)
    , reconnectInterval(30000)
    , maxReconnectAttempts(5)
    , currentReconnectAttempts(0)
    , lastStatusCheck(0)
    , statusCheckInterval(5000)
    , lastRSSI(0)
    , autoReconnect(true) {
}

WiFiManager::~WiFiManager() {
    stop();
}

bool WiFiManager::begin() {
    Serial.println("📶 初始化WiFi管理器...");

    // 关键WiFi稳定性配置
    WiFi.persistent(false);  // 禁用WiFi配置持久化，避免flash写入问题
    WiFi.setAutoConnect(false);  // 禁用自动连接
    WiFi.setAutoReconnect(false);  // 禁用自动重连，我们手动管理
    WiFi.setSleep(false);  // 全局禁用WiFi睡眠模式

    // 设置适中的发射功率，避免过热
    WiFi.setTxPower(WIFI_POWER_19_5dBm);

    // 设置WiFi模式
    WiFi.mode(WIFI_STA);

    // 设置WiFi事件处理
    WiFi.onEvent(onWiFiEvent);
    
    // 加载网络配置
    loadNetworkConfig();
    
    // 如果有配置的网络，尝试连接
    if (!configuredNetworks.empty()) {
        Serial.println("📶 尝试连接到已配置的网络...");
        
        // 添加所有配置的网络到WiFiMulti
        for (const auto& network : configuredNetworks) {
            if (network.isConfigured) {
                wifiMulti.addAP(network.ssid.c_str(), network.password.c_str());
                Serial.printf("  添加网络: %s\n", network.ssid.c_str());
            }
        }
        
        // 尝试连接
        if (wifiMulti.run(connectionTimeout) == WL_CONNECTED) {
            currentState = CONNECTED;
            currentNetwork.ssid = WiFi.SSID();
            currentNetwork.rssi = WiFi.RSSI();
            
            Serial.printf("✅ WiFi连接成功: %s (RSSI: %d dBm)\n", 
                          currentNetwork.ssid.c_str(), currentNetwork.rssi);
            Serial.printf("📍 IP地址: %s\n", WiFi.localIP().toString().c_str());
            
            handleStateChange(CONNECTED);
        } else {
            Serial.println("⚠️ 无法连接到已配置的网络，启动AP模式");
            startAP();
        }
    } else {
        Serial.println("📶 没有配置的网络，启动AP模式");
        startAP();
    }
    
    lastStatusCheck = millis();
    Serial.println("✅ WiFi管理器初始化完成");
    return true;
}

void WiFiManager::loop() {
    uint32_t currentTime = millis();

    // AP模式下减少状态检查频率，避免干扰
    uint32_t checkInterval = (currentState == AP_MODE) ? 10000 : statusCheckInterval;  // AP模式10秒检查一次

    // 定期检查连接状态
    if (currentTime - lastStatusCheck >= checkInterval) {
        lastStatusCheck = currentTime;
        checkConnectionStatus();
    }

    // 处理自动重连 - 只在STA模式下进行，避免干扰AP模式
    if (autoReconnect && currentState == DISCONNECTED && currentMode != AP_MODE_ONLY) {
        handleAutoReconnect();
    }

    // AP模式下的额外稳定性检查
    if (currentState == AP_MODE && currentTime - lastStatusCheck >= 30000) {  // 30秒检查一次
        // 确保AP仍然活跃
        if (WiFi.getMode() != WIFI_AP && WiFi.getMode() != WIFI_AP_STA) {
            Serial.println("⚠️ AP模式意外关闭，重新启动...");
            startAP();  // 重新启动AP模式
        }
    }
}

void WiFiManager::stop() {
    Serial.println("🛑 停止WiFi管理器");
    
    // 断开连接
    disconnect();
    
    // 停止AP模式
    stopAP();
    
    // 清理WiFiMulti
    // WiFiMulti没有clear方法，重新创建实例
    wifiMulti = WiFiMulti();
    
    currentState = DISCONNECTED;
}

bool WiFiManager::connect(const String& ssid, const String& password, uint32_t timeout) {
    Serial.printf("📶 连接WiFi: %s\n", ssid.c_str());
    
    currentState = CONNECTING;
    lastConnectionAttempt = millis();
    
    // 断开当前连接
    WiFi.disconnect();
    delay(100);
    
    // 开始连接
    WiFi.begin(ssid.c_str(), password.c_str());
    
    // 等待连接
    uint32_t startTime = millis();
    while (WiFi.status() != WL_CONNECTED && (millis() - startTime) < timeout) {
        delay(500);
        Serial.print(".");
    }
    Serial.println();
    
    if (WiFi.status() == WL_CONNECTED) {
        currentState = CONNECTED;
        currentNetwork.ssid = ssid;
        currentNetwork.password = password;
        currentNetwork.rssi = WiFi.RSSI();
        currentNetwork.lastConnected = millis();
        currentReconnectAttempts = 0;
        
        Serial.printf("✅ WiFi连接成功: %s\n", ssid.c_str());
        Serial.printf("📍 IP地址: %s\n", WiFi.localIP().toString().c_str());
        Serial.printf("📶 信号强度: %d dBm\n", currentNetwork.rssi);
        
        // 保存网络配置
        addNetwork(ssid, password);
        
        handleStateChange(CONNECTED);
        return true;
    } else {
        currentState = DISCONNECTED;
        Serial.printf("❌ WiFi连接失败: %s\n", ssid.c_str());
        
        handleStateChange(DISCONNECTED);
        return false;
    }
}

void WiFiManager::disconnect() {
    if (currentState == CONNECTED || currentState == CONNECTING) {
        Serial.println("📶 断开WiFi连接");
        WiFi.disconnect();
        currentState = DISCONNECTED;
        handleStateChange(DISCONNECTED);
    }
}

bool WiFiManager::reconnect() {
    if (currentNetwork.ssid.isEmpty()) {
        Serial.println("⚠️ 没有可重连的网络");
        return false;
    }
    
    Serial.printf("🔄 重新连接WiFi: %s\n", currentNetwork.ssid.c_str());
    return connect(currentNetwork.ssid, currentNetwork.password, connectionTimeout);
}

bool WiFiManager::startAP(const String& ssid, const String& password, uint8_t channel) {
    Serial.printf("📡 启动AP模式: %s\n", ssid.c_str());

    // 先断开所有连接，确保干净的状态
    WiFi.disconnect(true);
    delay(100);

    // 设置AP模式
    WiFi.mode(WIFI_AP);
    delay(100);  // 等待模式切换完成

    // AP模式稳定性优化配置
    WiFi.setSleep(false);  // 禁用WiFi睡眠模式，提高AP稳定性

    // ESP32-S3特定的WiFi稳定性配置
    WiFi.setTxPower(WIFI_POWER_19_5dBm);  // 设置适中的发射功率，避免过热

    // 配置AP网络参数
    WiFi.softAPConfig(apIP, apGateway, apSubnet);

    // 启动AP - 使用隐藏SSID=false确保可见性
    bool success = WiFi.softAP(ssid.c_str(), password.c_str(), channel, false, maxClients);
    
    if (success) {
        apSSID = ssid;
        apPassword = password;
        apChannel = channel;
        currentMode = AP_MODE_ONLY;
        currentState = AP_MODE;

        Serial.printf("✅ AP模式启动成功\n");
        Serial.printf("📡 SSID: %s\n", apSSID.c_str());
        Serial.printf("📍 IP地址: %s\n", WiFi.softAPIP().toString().c_str());
        Serial.printf("📻 信道: %d\n", apChannel);

        handleStateChange(AP_MODE);
        return true;
    } else {
        Serial.println("❌ AP模式启动失败");
        currentState = ERROR;
        handleStateChange(ERROR);
        return false;
    }
}

void WiFiManager::stopAP() {
    if (isAPMode()) {
        Serial.println("📡 停止AP模式");
        WiFi.softAPdisconnect(true);
        
        if (currentMode == AP_MODE_ONLY) {
            WiFi.mode(WIFI_STA);
            currentMode = STA_MODE;
            currentState = DISCONNECTED;
        } else if (currentMode == AP_STA_MODE) {
            WiFi.mode(WIFI_STA);
            currentMode = STA_MODE;
        }
    }
}

bool WiFiManager::startAPSTA() {
    Serial.println("📶📡 启动AP+STA模式");
    
    // 设置AP+STA模式
    WiFi.mode(WIFI_AP_STA);
    
    // 启动AP
    WiFi.softAPConfig(apIP, apGateway, apSubnet);
    bool apSuccess = WiFi.softAP(apSSID.c_str(), apPassword.c_str(), apChannel, 0, maxClients);
    
    if (apSuccess) {
        currentMode = AP_STA_MODE;
        
        Serial.printf("✅ AP+STA模式启动成功\n");
        Serial.printf("📡 AP SSID: %s\n", apSSID.c_str());
        Serial.printf("📍 AP IP: %s\n", WiFi.softAPIP().toString().c_str());
        
        // 尝试连接到已配置的网络
        if (!configuredNetworks.empty()) {
            for (const auto& network : configuredNetworks) {
                if (network.isConfigured) {
                    wifiMulti.addAP(network.ssid.c_str(), network.password.c_str());
                }
            }
            
            if (wifiMulti.run(connectionTimeout) == WL_CONNECTED) {
                currentState = CONNECTED;
                currentNetwork.ssid = WiFi.SSID();
                currentNetwork.rssi = WiFi.RSSI();
                
                Serial.printf("✅ STA连接成功: %s\n", currentNetwork.ssid.c_str());
                Serial.printf("📍 STA IP: %s\n", WiFi.localIP().toString().c_str());
            }
        }
        
        return true;
    } else {
        Serial.println("❌ AP+STA模式启动失败");
        return false;
    }
}

bool WiFiManager::addNetwork(const String& ssid, const String& password) {
    // 检查是否已存在
    WiFiNetwork* existing = findNetwork(ssid);
    if (existing) {
        existing->password = password;
        existing->isConfigured = true;
        Serial.printf("🔄 更新网络配置: %s\n", ssid.c_str());
    } else {
        WiFiNetwork network;
        network.ssid = ssid;
        network.password = password;
        network.isConfigured = true;
        configuredNetworks.push_back(network);
        Serial.printf("➕ 添加网络配置: %s\n", ssid.c_str());
    }
    
    return saveNetworkConfig();
}

bool WiFiManager::removeNetwork(const String& ssid) {
    auto it = std::find_if(configuredNetworks.begin(), configuredNetworks.end(),
                          [&ssid](const WiFiNetwork& network) {
                              return network.ssid == ssid;
                          });
    
    if (it != configuredNetworks.end()) {
        configuredNetworks.erase(it);
        Serial.printf("➖ 移除网络配置: %s\n", ssid.c_str());
        return saveNetworkConfig();
    }
    
    return false;
}

void WiFiManager::clearNetworks() {
    configuredNetworks.clear();
    wifiMulti = WiFiMulti();  // 重新创建实例以清空
    saveNetworkConfig();
    Serial.println("🗑️ 清空所有网络配置");
}

bool WiFiManager::saveNetworkConfig() {
    JsonDocument doc;
    JsonArray networks = doc["networks"].to<JsonArray>();

    for (const auto& network : configuredNetworks) {
        if (network.isConfigured) {
            JsonObject networkObj = networks.add<JsonObject>();
            networkObj["ssid"] = network.ssid;
            networkObj["password"] = network.password;
            networkObj["last_connected"] = network.lastConnected;
            networkObj["connect_attempts"] = network.connectAttempts;
        }
    }

    File configFile = LittleFS.open(WIFI_CONFIG_FILE, "w");
    if (!configFile) {
        Serial.println("❌ 无法创建WiFi配置文件");
        return false;
    }

    if (serializeJson(doc, configFile) == 0) {
        configFile.close();
        Serial.println("❌ WiFi配置写入失败");
        return false;
    }

    configFile.close();
    Serial.println("💾 WiFi配置保存成功");
    return true;
}

bool WiFiManager::loadNetworkConfig() {
    if (!LittleFS.exists(WIFI_CONFIG_FILE)) {
        Serial.println("📄 WiFi配置文件不存在，创建默认配置");
        // 创建空的配置文件
        File configFile = LittleFS.open(WIFI_CONFIG_FILE, "w");
        if (configFile) {
            configFile.println("{}");
            configFile.close();
        }
        return false;
    }

    File configFile = LittleFS.open(WIFI_CONFIG_FILE, "r");
    if (!configFile) {
        Serial.println("❌ 无法打开WiFi配置文件");
        return false;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, configFile);
    configFile.close();

    if (error) {
        Serial.printf("❌ WiFi配置解析失败: %s\n", error.c_str());
        return false;
    }

    configuredNetworks.clear();
    JsonArray networks = doc["networks"];

    for (JsonVariant networkVar : networks) {
        JsonObject networkObj = networkVar.as<JsonObject>();

        WiFiNetwork network;
        network.ssid = networkObj["ssid"].as<String>();
        network.password = networkObj["password"].as<String>();
        network.lastConnected = networkObj["last_connected"].as<uint32_t>();
        network.connectAttempts = networkObj["connect_attempts"].as<uint32_t>();
        network.isConfigured = true;

        configuredNetworks.push_back(network);
        Serial.printf("📋 加载网络配置: %s\n", network.ssid.c_str());
    }

    Serial.printf("✅ 加载了 %d 个网络配置\n", configuredNetworks.size());
    return true;
}

std::vector<WiFiManager::WiFiNetwork> WiFiManager::scanNetworks() {
    std::vector<WiFiNetwork> networks;

    Serial.println("🔍 扫描WiFi网络...");
    int networkCount = WiFi.scanNetworks();

    if (networkCount == 0) {
        Serial.println("📶 未发现WiFi网络");
        return networks;
    }

    Serial.printf("📶 发现 %d 个WiFi网络:\n", networkCount);

    for (int i = 0; i < networkCount; i++) {
        WiFiNetwork network;
        network.ssid = WiFi.SSID(i);
        network.rssi = WiFi.RSSI(i);
        network.encryptionType = WiFi.encryptionType(i);

        // 检查是否已配置
        WiFiNetwork* configured = findNetwork(network.ssid);
        network.isConfigured = (configured != nullptr);
        if (configured) {
            network.password = configured->password;
            network.lastConnected = configured->lastConnected;
            network.connectAttempts = configured->connectAttempts;
        }

        networks.push_back(network);

        Serial.printf("  %d: %s (%d dBm) %s %s\n",
                      i + 1,
                      network.ssid.c_str(),
                      network.rssi,
                      getEncryptionTypeString(network.encryptionType).c_str(),
                      network.isConfigured ? "[已配置]" : "");
    }

    // 清理扫描结果
    WiFi.scanDelete();

    return networks;
}

IPAddress WiFiManager::getLocalIP() const {
    if (currentState == CONNECTED) {
        return WiFi.localIP();
    } else if (currentState == AP_MODE) {
        return WiFi.softAPIP();
    }
    return IPAddress(0, 0, 0, 0);
}

String WiFiManager::getMACAddress() const {
    if (isAPMode()) {
        return WiFi.softAPmacAddress();
    } else {
        return WiFi.macAddress();
    }
}

String WiFiManager::getCurrentSSID() const {
    if (currentState == CONNECTED) {
        return WiFi.SSID();
    } else if (currentState == AP_MODE) {
        return apSSID;
    }
    return "";
}

int32_t WiFiManager::getRSSI() const {
    if (currentState == CONNECTED) {
        return WiFi.RSSI();
    }
    return 0;
}

uint8_t WiFiManager::getConnectedClients() const {
    if (isAPMode()) {
        return WiFi.softAPgetStationNum();
    }
    return 0;
}

JsonDocument WiFiManager::getNetworkStatus() const {
    JsonDocument doc;

    doc["state"] = getStateString(currentState);
    doc["mode"] = getModeString(currentMode);
    doc["auto_reconnect"] = autoReconnect;
    doc["reconnect_attempts"] = currentReconnectAttempts;
    doc["max_reconnect_attempts"] = maxReconnectAttempts;

    if (currentState == CONNECTED) {
        doc["ssid"] = getCurrentSSID();
        doc["ip"] = getLocalIP().toString();
        doc["mac"] = getMACAddress();
        doc["rssi"] = getRSSI();
        doc["gateway"] = WiFi.gatewayIP().toString();
        doc["subnet"] = WiFi.subnetMask().toString();
        doc["dns"] = WiFi.dnsIP().toString();
    }

    if (isAPMode()) {
        JsonObject apInfo = doc["ap"].to<JsonObject>();
        apInfo["ssid"] = apSSID;
        apInfo["ip"] = WiFi.softAPIP().toString();
        apInfo["mac"] = WiFi.softAPmacAddress();
        apInfo["channel"] = apChannel;
        apInfo["connected_clients"] = getConnectedClients();
        apInfo["max_clients"] = maxClients;
    }

    return doc;
}

JsonDocument WiFiManager::getNetworkConfig() const {
    JsonDocument doc;

    doc["connection_timeout"] = connectionTimeout;
    doc["reconnect_interval"] = reconnectInterval;
    doc["status_check_interval"] = statusCheckInterval;
    doc["auto_reconnect"] = autoReconnect;

    JsonArray networks = doc["configured_networks"].to<JsonArray>();
    for (const auto& network : configuredNetworks) {
        if (network.isConfigured) {
            JsonObject networkObj = networks.add<JsonObject>();
            networkObj["ssid"] = network.ssid;
            networkObj["last_connected"] = network.lastConnected;
            networkObj["connect_attempts"] = network.connectAttempts;
            // 不包含密码以保护安全
        }
    }

    JsonObject apConfig = doc["ap_config"].to<JsonObject>();
    apConfig["ssid"] = apSSID;
    apConfig["channel"] = apChannel;
    apConfig["max_clients"] = maxClients;
    apConfig["ip"] = apIP.toString();

    return doc;
}

JsonDocument WiFiManager::getNetworkStats() const {
    JsonDocument doc;

    doc["total_configured_networks"] = configuredNetworks.size();
    doc["current_reconnect_attempts"] = currentReconnectAttempts;
    doc["last_connection_attempt"] = lastConnectionAttempt;
    doc["uptime_connected"] = (currentState == CONNECTED) ? (millis() - lastConnectionAttempt) : 0;

    // 统计各网络的连接尝试次数
    JsonArray networkStats = doc["network_stats"].to<JsonArray>();
    for (const auto& network : configuredNetworks) {
        JsonObject networkObj = networkStats.add<JsonObject>();
        networkObj["ssid"] = network.ssid;
        networkObj["connect_attempts"] = network.connectAttempts;
        networkObj["last_connected"] = network.lastConnected;
        networkObj["is_current"] = (network.ssid == currentNetwork.ssid);
    }

    return doc;
}

void WiFiManager::setAutoReconnect(bool enable, uint32_t interval, uint8_t maxAttempts) {
    autoReconnect = enable;
    reconnectInterval = interval;
    maxReconnectAttempts = maxAttempts;

    Serial.printf("🔄 自动重连设置: %s (间隔: %d ms, 最大尝试: %d 次)\n",
                  enable ? "启用" : "禁用", interval, maxAttempts);
}

// ==================== 私有方法实现 ====================

void WiFiManager::handleStateChange(WiFiState newState) {
    if (newState != currentState) {
        WiFiState oldState = currentState;
        currentState = newState;

        Serial.printf("📶 WiFi状态变化: %s -> %s\n",
                      getStateString(oldState).c_str(),
                      getStateString(newState).c_str());

        // 调用状态变化回调
        if (stateChangeCallback) {
            stateChangeCallback(newState);
        }

        // 发送系统事件
        SystemManager& systemManager = SystemManager::getInstance();
        JsonDocument eventData;
        eventData["old_state"] = getStateString(oldState);
        eventData["new_state"] = getStateString(newState);
        eventData["timestamp"] = millis();

        if (newState == CONNECTED) {
            eventData["ssid"] = getCurrentSSID();
            eventData["ip"] = getLocalIP().toString();
            eventData["rssi"] = getRSSI();
        }

        systemManager.sendSystemEvent("wifi_state_changed", eventData);
    }
}

void WiFiManager::checkConnectionStatus() {
    if (currentState == CONNECTED) {
        // 检查连接是否仍然有效
        if (WiFi.status() != WL_CONNECTED) {
            Serial.println("⚠️ WiFi连接丢失");
            currentState = DISCONNECTED;
            handleStateChange(DISCONNECTED);
        } else {
            // 更新信号强度
            int32_t currentRSSI = WiFi.RSSI();
            if (abs(currentRSSI - lastRSSI) > 5) {  // 信号强度变化超过5dBm
                lastRSSI = currentRSSI;
                currentNetwork.rssi = currentRSSI;

                if (DEBUG_LEVEL >= 4) {
                    Serial.printf("📶 信号强度更新: %d dBm\n", currentRSSI);
                }
            }
        }
    } else if (currentState == AP_MODE) {
        // 检查AP模式状态和稳定性
        uint8_t clientCount = getConnectedClients();

        // 检查AP是否仍然活跃
        if (WiFi.getMode() != WIFI_AP && WiFi.getMode() != WIFI_AP_STA) {
            Serial.println("⚠️ AP模式意外关闭，重新启动...");
            startAP();  // 重新启动AP模式
        }

        if (DEBUG_LEVEL >= 4 && clientCount > 0) {
            Serial.printf("📡 AP客户端数量: %d\n", clientCount);
        }
    }
}

void WiFiManager::handleAutoReconnect() {
    // 在AP模式下不进行自动重连，避免干扰AP稳定性
    if (!autoReconnect || currentReconnectAttempts >= maxReconnectAttempts ||
        currentState == AP_MODE || currentMode == AP_MODE_ONLY) {
        return;
    }

    uint32_t currentTime = millis();
    if (currentTime - lastConnectionAttempt >= reconnectInterval) {
        currentReconnectAttempts++;

        Serial.printf("🔄 自动重连尝试 %d/%d\n",
                      currentReconnectAttempts, maxReconnectAttempts);

        if (!configuredNetworks.empty()) {
            // 尝试连接到最近连接过的网络
            WiFiNetwork* bestNetwork = nullptr;
            uint32_t latestConnection = 0;

            for (auto& network : configuredNetworks) {
                if (network.isConfigured && network.lastConnected > latestConnection) {
                    latestConnection = network.lastConnected;
                    bestNetwork = &network;
                }
            }

            if (bestNetwork) {
                bestNetwork->connectAttempts++;
                if (connect(bestNetwork->ssid, bestNetwork->password, connectionTimeout)) {
                    currentReconnectAttempts = 0;  // 重置重连计数
                }
            }
        }

        lastConnectionAttempt = currentTime;
    }
}

WiFiManager::WiFiNetwork* WiFiManager::findNetwork(const String& ssid) {
    for (auto& network : configuredNetworks) {
        if (network.ssid == ssid) {
            return &network;
        }
    }
    return nullptr;
}

String WiFiManager::getStateString(WiFiState state) const {
    switch (state) {
        case DISCONNECTED:
            return "disconnected";
        case CONNECTING:
            return "connecting";
        case CONNECTED:
            return "connected";
        case AP_MODE:
            return "ap_mode";
        case ERROR:
            return "error";
        default:
            return "unknown";
    }
}

String WiFiManager::getModeString(WiFiMode mode) const {
    switch (mode) {
        case STA_MODE:
            return "sta";
        case AP_MODE_ONLY:
            return "ap";
        case AP_STA_MODE:
            return "ap_sta";
        default:
            return "unknown";
    }
}

String WiFiManager::getEncryptionTypeString(uint8_t encType) const {
    switch (encType) {
        case WIFI_AUTH_OPEN:
            return "Open";
        case WIFI_AUTH_WEP:
            return "WEP";
        case WIFI_AUTH_WPA_PSK:
            return "WPA";
        case WIFI_AUTH_WPA2_PSK:
            return "WPA2";
        case WIFI_AUTH_WPA_WPA2_PSK:
            return "WPA/WPA2";
        case WIFI_AUTH_WPA2_ENTERPRISE:
            return "WPA2-Enterprise";
        case WIFI_AUTH_WPA3_PSK:
            return "WPA3";
        default:
            return "Unknown";
    }
}

void WiFiManager::onWiFiEvent(WiFiEvent_t event) {
    switch (event) {
        case ARDUINO_EVENT_WIFI_STA_START:
            Serial.println("📶 WiFi STA 启动");
            break;
        case ARDUINO_EVENT_WIFI_STA_CONNECTED:
            Serial.println("📶 WiFi STA 连接成功");
            break;
        case ARDUINO_EVENT_WIFI_STA_GOT_IP:
            Serial.printf("📍 获得IP地址: %s\n", WiFi.localIP().toString().c_str());
            break;
        case ARDUINO_EVENT_WIFI_STA_DISCONNECTED:
            Serial.println("📶 WiFi STA 断开连接");
            break;
        case ARDUINO_EVENT_WIFI_AP_START:
            Serial.println("📡 WiFi AP 启动");
            break;
        case ARDUINO_EVENT_WIFI_AP_STACONNECTED:
            Serial.println("📡 客户端连接到AP");
            break;
        case ARDUINO_EVENT_WIFI_AP_STADISCONNECTED:
            Serial.println("📡 客户端从AP断开");
            break;
        default:
            break;
    }
}
