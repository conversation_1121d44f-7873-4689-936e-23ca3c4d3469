#include "timer_manager_backend.h"
#include "time_manager.h"
#include "../storage/data_structures.h"
#include <algorithm>
#include <LittleFS.h>

// 前向声明避免循环依赖
class SystemManager;
class WebSocketManager;

// 静态成员初始化
TimerManagerBackend* TimerManagerBackend::instance = nullptr;
TimerManagerBackend* timerManagerBackend = nullptr;

TimerManagerBackend::TimerManagerBackend()
    : signalStorage(nullptr)
    , irManager(nullptr)
    , isRunning(false)
    , lastCheckTime(0)
    , checkInterval(60000)  // 1分钟检查一次
    , isExecuting(false)
{
    // 初始化状态
    status.isEnabled = false;
    status.totalTasks = 0;
    status.activeTasks = 0;
    status.executedTasks = 0;
    status.nextTaskTime = 0;
    status.nextTaskName = "";
    status.lastExecutionTime = 0;
    status.totalExecutions = 0;
}

TimerManagerBackend::~TimerManagerBackend() {
    stop();
}

TimerManagerBackend* TimerManagerBackend::getInstance() {
    if (instance == nullptr) {
        instance = new TimerManagerBackend();
    }
    return instance;
}

bool TimerManagerBackend::begin(SignalStorage* storage, IRSignalManager* ir) {
    if (!storage || !ir) {
        Serial.println("❌ TimerManagerBackend: 无效的组件引用");
        return false;
    }
    
    signalStorage = storage;
    irManager = ir;
    
    Serial.println("🕐 初始化定时器管理器...");
    
    // 加载保存的任务
    loadTasksFromStorage();
    
    // 更新系统状态
    updateSystemStatus();
    
    isRunning = true;
    lastCheckTime = millis();
    
    Serial.printf("✅ 定时器管理器初始化完成，加载了 %d 个任务\n", tasks.size());
    
    return true;
}

void TimerManagerBackend::loop() {
    if (!isRunning || !config.masterEnabled) {
        return;
    }
    
    uint32_t currentTime = millis();
    
    // 检查是否到了检查时间
    if (currentTime - lastCheckTime >= checkInterval) {
        checkDueTasks();
        updateSystemStatus();
        lastCheckTime = currentTime;
        
        // 定期清理过期任务
        if (config.autoCleanup) {
            cleanupExpiredTasks();
        }
    }
}

void TimerManagerBackend::stop() {
    isRunning = false;
    executionQueue.clear();
    isExecuting = false;
    
    Serial.println("🛑 定时器管理器已停止");
}

String TimerManagerBackend::createTask(const TimerTask& task) {
    if (!validateTask(task)) {
        Serial.println("❌ TimerManagerBackend: 任务数据验证失败");
        return "";
    }
    
    if (tasks.size() >= config.maxTasks) {
        Serial.printf("❌ TimerManagerBackend: 任务数量已达上限 (%d)\n", config.maxTasks);
        return "";
    }
    
    TimerTask newTask = task;
    newTask.id = generateTaskId();
    newTask.createdTime = getCurrentUnixMs();
    newTask.updatedTime = newTask.createdTime;
    
    // 计算下次执行时间
    newTask.calculateNextExecutionTime();
    
    tasks.push_back(newTask);
    
    // 保存到存储
    saveTasksToStorage();
    
    // 更新系统状态
    updateSystemStatus();
    
    Serial.printf("✅ TimerManagerBackend: 创建任务成功 - %s (ID: %s)\n", 
                  newTask.name.c_str(), newTask.id.c_str());
    
    return newTask.id;
}

bool TimerManagerBackend::updateTask(const String& taskId, const TimerTask& task) {
    auto it = std::find_if(tasks.begin(), tasks.end(), 
                          [&taskId](const TimerTask& t) { return t.id == taskId; });
    
    if (it == tasks.end()) {
        Serial.printf("❌ TimerManagerBackend: 未找到任务 %s\n", taskId.c_str());
        return false;
    }
    
    if (!validateTask(task)) {
        Serial.println("❌ TimerManagerBackend: 任务数据验证失败");
        return false;
    }
    
    // 保留原有的统计信息
    TimerTask updatedTask = task;
    updatedTask.id = taskId;
    updatedTask.createdTime = it->createdTime;
    updatedTask.executionCount = it->executionCount;
    updatedTask.lastExecutionTime = it->lastExecutionTime;
    updatedTask.updatedTime = getCurrentUnixMs();
    
    // 重新计算下次执行时间
    updatedTask.calculateNextExecutionTime();
    
    *it = updatedTask;
    
    // 保存到存储
    saveTasksToStorage();
    
    // 更新系统状态
    updateSystemStatus();
    
    Serial.printf("✅ TimerManagerBackend: 更新任务成功 - %s\n", taskId.c_str());
    
    return true;
}

bool TimerManagerBackend::deleteTask(const String& taskId) {
    auto it = std::find_if(tasks.begin(), tasks.end(), 
                          [&taskId](const TimerTask& t) { return t.id == taskId; });
    
    if (it == tasks.end()) {
        Serial.printf("❌ TimerManagerBackend: 未找到任务 %s\n", taskId.c_str());
        return false;
    }
    
    String taskName = it->name;
    tasks.erase(it);
    
    // 保存到存储
    saveTasksToStorage();
    
    // 更新系统状态
    updateSystemStatus();
    
    Serial.printf("✅ TimerManagerBackend: 删除任务成功 - %s\n", taskName.c_str());
    
    return true;
}

TimerTask* TimerManagerBackend::getTask(const String& taskId) {
    auto it = std::find_if(tasks.begin(), tasks.end(), 
                          [&taskId](const TimerTask& t) { return t.id == taskId; });
    
    return (it != tasks.end()) ? &(*it) : nullptr;
}

std::vector<TimerTask> TimerManagerBackend::getAllTasks() const {
    return tasks;
}

bool TimerManagerBackend::clearAllTasks() {
    tasks.clear();
    
    // 保存到存储
    saveTasksToStorage();
    
    // 更新系统状态
    updateSystemStatus();
    
    Serial.println("✅ TimerManagerBackend: 清空所有任务");
    
    return true;
}

bool TimerManagerBackend::enableTask(const String& taskId, bool enabled) {
    TimerTask* task = getTask(taskId);
    if (!task) {
        return false;
    }
    
    task->isEnabled = enabled;
    task->updatedTime = getCurrentUnixMs();
    
    // 重新计算下次执行时间
    task->calculateNextExecutionTime();
    
    // 保存到存储
    saveTasksToStorage();
    
    // 更新系统状态
    updateSystemStatus();
    
    Serial.printf("✅ TimerManagerBackend: 任务 %s %s\n", 
                  taskId.c_str(), enabled ? "已启用" : "已禁用");
    
    return true;
}

bool TimerManagerBackend::executeTask(const String& taskId) {
    TimerTask* task = getTask(taskId);
    if (!task) {
        return false;
    }
    
    executeTaskInternal(*task);
    return true;
}

bool TimerManagerBackend::isTaskDue(const String& taskId) const {
    auto it = std::find_if(tasks.begin(), tasks.end(), 
                          [&taskId](const TimerTask& t) { return t.id == taskId; });
    
    return (it != tasks.end()) ? it->isDue() : false;
}

bool TimerManagerBackend::setMasterEnabled(bool enabled) {
    config.masterEnabled = enabled;
    status.isEnabled = enabled;
    
    Serial.printf("🔧 TimerManagerBackend: 主开关 %s\n", enabled ? "已启用" : "已禁用");
    
    return true;
}

TimerSystemStatus TimerManagerBackend::getSystemStatus() const {
    return status;
}

bool TimerManagerBackend::updateConfig(const TimerConfig& newConfig) {
    config = newConfig;
    checkInterval = config.checkInterval;
    
    Serial.println("🔧 TimerManagerBackend: 配置已更新");
    
    return true;
}

uint32_t TimerManagerBackend::getActiveTaskCount() const {
    return std::count_if(tasks.begin(), tasks.end(), 
                        [](const TimerTask& t) { return t.isEnabled; });
}

uint32_t TimerManagerBackend::getTotalExecutions() const {
    uint32_t total = 0;
    for (const auto& task : tasks) {
        total += task.executionCount;
    }
    return total;
}

TimerTask* TimerManagerBackend::getNextDueTask() {
    TimerTask* nextTask = nullptr;
    uint64_t earliestTime = UINT64_MAX;
    
    for (auto& task : tasks) {
        if (task.isEnabled && task.nextExecutionTime > 0 && task.nextExecutionTime < earliestTime) {
            earliestTime = task.nextExecutionTime;
            nextTask = &task;
        }
    }
    
    return nextTask;
}

void TimerManagerBackend::checkDueTasks() {
    if (isExecuting) {
        return; // 避免重复执行
    }

    for (auto& task : tasks) {
        if (task.isEnabled && task.isDue()) {
            executeTaskInternal(task);
        }
    }
}

void TimerManagerBackend::executeTaskInternal(TimerTask& task) {
    if (isExecuting) {
        Serial.printf("⚠️ TimerManagerBackend: 正在执行其他任务，跳过 %s\n", task.name.c_str());
        return;
    }

    isExecuting = true;

    Serial.printf("⏰ TimerManagerBackend: 开始执行任务 - %s\n", task.name.c_str());

    bool success = emitSignals(task.signalIds);

    if (success) {
        task.updateExecutionStats();
        status.lastExecutionTime = task.lastExecutionTime;
        status.totalExecutions++;

        // 保存更新的任务数据
        saveTasksToStorage();
    }

    // 通过回调函数发送WebSocket事件通知前端
    if (onTaskExecuted) {
        onTaskExecuted(task.id, task.name, success);
    }

    Serial.printf("📡 定时任务执行事件: %s - %s\n", task.name.c_str(), success ? "成功" : "失败");

    logTaskExecution(task, success);

    isExecuting = false;
}

void TimerManagerBackend::updateSystemStatus() {
    status.totalTasks = tasks.size();
    status.activeTasks = getActiveTaskCount();
    status.executedTasks = std::count_if(tasks.begin(), tasks.end(),
                                        [](const TimerTask& t) { return t.executionCount > 0; });

    // 找到下个要执行的任务
    TimerTask* nextTask = getNextDueTask();
    if (nextTask) {
        status.nextTaskTime = nextTask->nextExecutionTime;
        status.nextTaskName = nextTask->name;
    } else {
        status.nextTaskTime = 0;
        status.nextTaskName = "";
    }
}

void TimerManagerBackend::cleanupExpiredTasks() {
    if (!config.autoCleanup) {
        return;
    }

    uint64_t cutoffTime = getCurrentUnixMs() - (config.cleanupDays * 24 * 60 * 60 * 1000ULL);

    auto it = std::remove_if(tasks.begin(), tasks.end(),
                            [cutoffTime](const TimerTask& t) {
                                return !t.isEnabled &&
                                       t.lastExecutionTime > 0 &&
                                       t.lastExecutionTime < cutoffTime;
                            });

    if (it != tasks.end()) {
        size_t removedCount = std::distance(it, tasks.end());
        tasks.erase(it, tasks.end());

        Serial.printf("🧹 TimerManagerBackend: 清理了 %d 个过期任务\n", removedCount);

        saveTasksToStorage();
        updateSystemStatus();
    }
}

String TimerManagerBackend::generateTaskId() {
    return "timer_" + String(random(10000000, 99999999));
}

bool TimerManagerBackend::validateTask(const TimerTask& task) const {
    if (task.name.isEmpty()) {
        Serial.println("❌ 任务名称不能为空");
        return false;
    }

    if (task.startTime.isEmpty() || task.endTime.isEmpty()) {
        Serial.println("❌ 开始时间和结束时间不能为空");
        return false;
    }

    if (task.signalIds.empty()) {
        Serial.println("❌ 必须选择至少一个信号");
        return false;
    }

    if (task.intervalMinutes == 0) {
        Serial.println("❌ 执行间隔必须大于0");
        return false;
    }

    // 验证时间格式
    int hour, minute;
    if (sscanf(task.startTime.c_str(), "%d:%d", &hour, &minute) != 2 ||
        hour < 0 || hour > 23 || minute < 0 || minute > 59) {
        Serial.printf("❌ 无效的开始时间格式: %s\n", task.startTime.c_str());
        return false;
    }

    if (sscanf(task.endTime.c_str(), "%d:%d", &hour, &minute) != 2 ||
        hour < 0 || hour > 23 || minute < 0 || minute > 59) {
        Serial.printf("❌ 无效的结束时间格式: %s\n", task.endTime.c_str());
        return false;
    }

    return true;
}

void TimerManagerBackend::logTaskExecution(const TimerTask& task, bool success) {
    Serial.printf("%s TimerManagerBackend: 任务执行%s - %s (执行次数: %d)\n",
                  success ? "✅" : "❌",
                  success ? "成功" : "失败",
                  task.name.c_str(),
                  task.executionCount);
}

bool TimerManagerBackend::emitSignals(const std::vector<String>& signalIds) {
    if (!irManager || !signalStorage) {
        Serial.println("❌ TimerManagerBackend: 组件未初始化");
        return false;
    }

    bool allSuccess = true;

    for (const String& signalId : signalIds) {
        if (!emitSingleSignal(signalId)) {
            allSuccess = false;
        }

        // 信号间延迟
        delay(500);
    }

    return allSuccess;
}

bool TimerManagerBackend::emitSingleSignal(const String& signalId) {
    // 修复：getSignal返回SignalData对象，不是指针
    SignalData signal = signalStorage->getSignal(signalId);
    if (signal.id.isEmpty()) {
        Serial.printf("❌ TimerManagerBackend: 未找到信号 %s\n", signalId.c_str());
        return false;
    }

    // 修复：使用emitSignal方法，不是sendSignal
    bool success = irManager->emitSignal(signalId);

    if (success) {
        signal.sentCount++;
        signal.lastSent = getCurrentUnixMs();
        // 修复：updateSignal只需要一个参数
        signalStorage->updateSignal(signal);
    }

    return success;
}

bool TimerManagerBackend::saveTasksToStorage() {
    // 检查LittleFS是否已经挂载（避免重复挂载警告）
    if (!LittleFS.totalBytes()) {
        Serial.println("❌ TimerManagerBackend: LittleFS未就绪");
        return false;
    }

    JsonDocument doc;
    JsonArray tasksArray = doc["tasks"].to<JsonArray>();

    // 将所有任务转换为JSON
    for (const TimerTask& task : tasks) {
        JsonObject taskObj = tasksArray.add<JsonObject>();
        JsonDocument taskDoc = task.toJson();
        taskObj.set(taskDoc.as<JsonObject>());
    }

    // 保存配置信息
    JsonObject configObj = doc["config"].to<JsonObject>();
    configObj["masterEnabled"] = config.masterEnabled;
    configObj["maxTasks"] = config.maxTasks;
    configObj["autoCleanup"] = config.autoCleanup;
    configObj["cleanupDays"] = config.cleanupDays;

    // 写入文件
    File file = LittleFS.open("/timer_tasks.json", "w");
    if (!file) {
        Serial.println("❌ TimerManagerBackend: 无法创建任务文件");
        return false;
    }

    size_t bytesWritten = serializeJson(doc, file);
    file.close();

    if (bytesWritten > 0) {
        Serial.printf("✅ TimerManagerBackend: 保存了 %d 个任务到存储 (%d 字节)\n",
                      tasks.size(), bytesWritten);
        return true;
    } else {
        Serial.println("❌ TimerManagerBackend: 任务数据写入失败");
        return false;
    }
}

bool TimerManagerBackend::loadTasksFromStorage() {
    // 检查LittleFS是否已经挂载（避免重复挂载警告）
    if (!LittleFS.totalBytes()) {
        Serial.println("❌ TimerManagerBackend: LittleFS未就绪");
        return false;
    }

    if (!LittleFS.exists("/timer_tasks.json")) {
        Serial.println("📂 TimerManagerBackend: 任务文件不存在，使用默认配置");
        return true;  // 不是错误，只是没有保存的数据
    }

    File file = LittleFS.open("/timer_tasks.json", "r");
    if (!file) {
        Serial.println("❌ TimerManagerBackend: 无法打开任务文件");
        return false;
    }

    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        Serial.printf("❌ TimerManagerBackend: JSON解析失败: %s\n", error.c_str());
        return false;
    }

    // 清空现有任务
    tasks.clear();

    // 加载任务数据
    JsonArray tasksArray = doc["tasks"].as<JsonArray>();
    for (JsonVariant taskVar : tasksArray) {
        JsonObject taskObj = taskVar.as<JsonObject>();

        TimerTask task;
        task.id = taskObj["id"].as<String>();
        task.name = taskObj["name"].as<String>();
        task.startTime = taskObj["startTime"].as<String>();
        task.endTime = taskObj["endTime"].as<String>();
        task.isDaily = taskObj["isDaily"] | true;
        task.intervalMinutes = taskObj["intervalMinutes"] | 60;
        task.isEnabled = taskObj["isEnabled"] | true;
        task.nextExecutionTime = taskObj["nextExecutionTime"] | 0;
        task.lastExecutionTime = taskObj["lastExecutionTime"] | 0;
        task.executionCount = taskObj["executionCount"] | 0;
        task.createdTime = taskObj["createdTime"] | getCurrentUnixMs();
        task.updatedTime = taskObj["updatedTime"] | getCurrentUnixMs();

        // 加载信号ID列表
        JsonArray signalArray = taskObj["signalIds"].as<JsonArray>();
        for (JsonVariant signalId : signalArray) {
            task.signalIds.push_back(signalId.as<String>());
        }

        // 重新计算下次执行时间
        task.calculateNextExecutionTime();

        tasks.push_back(task);
    }

    // 加载配置信息
    if (doc["config"].is<JsonObject>()) {
        JsonObject configObj = doc["config"].as<JsonObject>();
        config.masterEnabled = configObj["masterEnabled"] | false;
        config.maxTasks = configObj["maxTasks"] | 50;
        config.autoCleanup = configObj["autoCleanup"] | true;
        config.cleanupDays = configObj["cleanupDays"] | 30;
    }

    Serial.printf("✅ TimerManagerBackend: 加载了 %d 个任务从存储\n", tasks.size());
    return true;
}

// 便捷函数实现
TimerManagerBackend* getTimerManagerBackend() {
    return TimerManagerBackend::getInstance();
}
