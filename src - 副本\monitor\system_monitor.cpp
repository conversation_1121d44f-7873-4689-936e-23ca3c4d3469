#include "system_monitor.h"
#include <WiFi.h>
#include <LittleFS.h>

SystemMonitor::SystemMonitor() 
    : isMonitoring(false)
    , monitoringInterval(5000)  // 5秒默认间隔
    , lastMonitorTime(0)
    , maxHistorySize(100)       // 保存100个历史记录
    , memoryWarningThreshold(80) // 80%内存使用警告
    , memoryErrorThreshold(95)   // 95%内存使用错误
    , temperatureWarningThreshold(70.0)  // 70°C温度警告
    , temperatureErrorThreshold(85.0)    // 85°C温度错误
    , wifiSignalWarningThreshold(-70) {  // -70dBm WiFi信号警告
}

SystemMonitor::~SystemMonitor() {
    stop();
}

bool SystemMonitor::begin() {
    Serial.println("📊 初始化系统监控器...");
    
    // 初始化性能指标
    updateMetrics();
    peakMetrics = currentMetrics;
    
    // 初始化健康状态
    updateHealthStatus();
    
    Serial.println("✅ 系统监控器初始化完成");
    return true;
}

void SystemMonitor::loop() {
    if (!isMonitoring) {
        return;
    }
    
    uint32_t currentTime = millis();
    if (currentTime - lastMonitorTime >= monitoringInterval) {
        lastMonitorTime = currentTime;
        
        // 更新性能指标
        updateMetrics();
        
        // 更新健康状态
        updateHealthStatus();
        
        // 添加到历史记录
        addToHistory(currentMetrics);
        
        // 检查是否需要发送警告
        if (!healthStatus.memoryHealthy || !healthStatus.temperatureHealthy) {
            logMonitor("WARN", "系统健康状态异常");
        }
    }
}

void SystemMonitor::stop() {
    if (isMonitoring) {
        Serial.println("🛑 停止系统监控器");
        stopMonitoring();
    }
}

void SystemMonitor::startMonitoring(uint32_t interval) {
    monitoringInterval = interval;
    isMonitoring = true;
    lastMonitorTime = millis();
    
    logMonitor("INFO", "开始系统监控 (间隔: " + String(interval) + "ms)");
}

void SystemMonitor::stopMonitoring() {
    isMonitoring = false;
    logMonitor("INFO", "停止系统监控");
}

JsonDocument SystemMonitor::getCurrentMetrics() {
    updateMetrics();
    
    JsonDocument doc;
    doc["cpu_usage_percent"] = currentMetrics.cpuUsagePercent;
    doc["memory_usage_percent"] = currentMetrics.memoryUsagePercent;
    doc["free_heap_bytes"] = currentMetrics.freeHeapBytes;
    doc["free_heap_kb"] = currentMetrics.freeHeapBytes / 1024;
    doc["min_free_heap_bytes"] = currentMetrics.minFreeHeapBytes;
    doc["max_alloc_heap_bytes"] = currentMetrics.maxAllocHeapBytes;
    doc["psram_usage_percent"] = currentMetrics.psramUsagePercent;
    doc["cpu_temperature"] = currentMetrics.cpuTemperature;
    doc["uptime_seconds"] = currentMetrics.uptimeSeconds;
    doc["wifi_signal_strength"] = currentMetrics.wifiSignalStrength;
    doc["task_count"] = currentMetrics.taskCount;
    doc["timestamp"] = millis();
    
    return doc;
}

JsonDocument SystemMonitor::getPeakMetrics() const {
    JsonDocument doc;
    doc["max_cpu_usage_percent"] = peakMetrics.cpuUsagePercent;
    doc["max_memory_usage_percent"] = peakMetrics.memoryUsagePercent;
    doc["min_free_heap_bytes"] = peakMetrics.minFreeHeapBytes;
    doc["max_alloc_heap_bytes"] = peakMetrics.maxAllocHeapBytes;
    doc["max_psram_usage_percent"] = peakMetrics.psramUsagePercent;
    doc["max_cpu_temperature"] = peakMetrics.cpuTemperature;
    doc["max_task_count"] = peakMetrics.taskCount;
    
    return doc;
}

JsonDocument SystemMonitor::getHealthStatus() {
    updateHealthStatus();
    
    JsonDocument doc;
    doc["overall_healthy"] = (healthStatus.memoryHealthy && 
                             healthStatus.storageHealthy && 
                             healthStatus.wifiHealthy && 
                             healthStatus.temperatureHealthy && 
                             healthStatus.tasksHealthy);
    doc["memory_healthy"] = healthStatus.memoryHealthy;
    doc["storage_healthy"] = healthStatus.storageHealthy;
    doc["wifi_healthy"] = healthStatus.wifiHealthy;
    doc["temperature_healthy"] = healthStatus.temperatureHealthy;
    doc["tasks_healthy"] = healthStatus.tasksHealthy;
    doc["last_error"] = healthStatus.lastError;
    doc["error_count"] = healthStatus.errorCount;
    doc["timestamp"] = millis();
    
    return doc;
}

bool SystemMonitor::checkMemoryHealth() {
    uint32_t memUsage = getMemoryUsage();
    
    if (memUsage >= memoryErrorThreshold) {
        healthStatus.memoryHealthy = false;
        healthStatus.lastError = "内存使用率过高: " + String(memUsage) + "%";
        healthStatus.errorCount++;
        return false;
    } else if (memUsage >= memoryWarningThreshold) {
        logMonitor("WARN", "内存使用率警告: " + String(memUsage) + "%");
    }
    
    healthStatus.memoryHealthy = true;
    return true;
}

bool SystemMonitor::checkStorageHealth() {
    size_t totalBytes = LittleFS.totalBytes();
    size_t usedBytes = LittleFS.usedBytes();
    
    if (totalBytes == 0) {
        healthStatus.storageHealthy = false;
        healthStatus.lastError = "文件系统不可用";
        healthStatus.errorCount++;
        return false;
    }
    
    uint32_t usagePercent = (usedBytes * 100) / totalBytes;
    
    if (usagePercent >= 95) {
        healthStatus.storageHealthy = false;
        healthStatus.lastError = "存储空间不足: " + String(usagePercent) + "%";
        healthStatus.errorCount++;
        return false;
    } else if (usagePercent >= 85) {
        logMonitor("WARN", "存储空间警告: " + String(usagePercent) + "%");
    }
    
    healthStatus.storageHealthy = true;
    return true;
}

bool SystemMonitor::checkWiFiHealth() {
    if (WiFi.status() != WL_CONNECTED) {
        healthStatus.wifiHealthy = false;
        healthStatus.lastError = "WiFi连接断开";
        healthStatus.errorCount++;
        return false;
    }
    
    int32_t rssi = WiFi.RSSI();
    if (rssi < wifiSignalWarningThreshold) {
        logMonitor("WARN", "WiFi信号弱: " + String(rssi) + "dBm");
    }
    
    healthStatus.wifiHealthy = true;
    return true;
}

bool SystemMonitor::checkTemperatureHealth() {
    float temp = getCPUTemperature();
    
    if (temp >= temperatureErrorThreshold) {
        healthStatus.temperatureHealthy = false;
        healthStatus.lastError = "CPU温度过高: " + String(temp) + "°C";
        healthStatus.errorCount++;
        return false;
    } else if (temp >= temperatureWarningThreshold) {
        logMonitor("WARN", "CPU温度警告: " + String(temp) + "°C");
    }
    
    healthStatus.temperatureHealthy = true;
    return true;
}

bool SystemMonitor::checkTasksHealth() {
    uint32_t taskCount = getTaskCount();
    
    // 检查任务数量是否异常
    if (taskCount > 20) {  // 假设正常情况下不超过20个任务
        logMonitor("WARN", "任务数量异常: " + String(taskCount));
    }
    
    healthStatus.tasksHealthy = true;
    return true;
}

JsonDocument SystemMonitor::getSystemInfo() const {
    JsonDocument doc;
    
    doc["chip_model"] = ESP.getChipModel();
    doc["chip_revision"] = ESP.getChipRevision();
    doc["chip_cores"] = ESP.getChipCores();
    doc["cpu_freq_mhz"] = ESP.getCpuFreqMHz();
    doc["flash_chip_size"] = ESP.getFlashChipSize();
    doc["flash_chip_speed"] = ESP.getFlashChipSpeed();
    doc["heap_size"] = ESP.getHeapSize();
    doc["psram_size"] = ESP.getPsramSize();
    doc["mac_address"] = WiFi.macAddress();
    doc["firmware_version"] = FIRMWARE_VERSION;
    doc["build_date"] = BUILD_DATE;
    doc["build_time"] = BUILD_TIME;
    
    return doc;
}

JsonDocument SystemMonitor::getMemoryInfo() const {
    JsonDocument doc;
    
    doc["heap_size"] = ESP.getHeapSize();
    doc["free_heap"] = ESP.getFreeHeap();
    doc["min_free_heap"] = ESP.getMinFreeHeap();
    doc["max_alloc_heap"] = ESP.getMaxAllocHeap();
    doc["heap_usage_percent"] = ((ESP.getHeapSize() - ESP.getFreeHeap()) * 100) / ESP.getHeapSize();
    
    if (ESP.getPsramSize() > 0) {
        doc["psram_size"] = ESP.getPsramSize();
        doc["free_psram"] = ESP.getFreePsram();
        doc["min_free_psram"] = ESP.getMinFreePsram();
        doc["max_alloc_psram"] = ESP.getMaxAllocPsram();
        doc["psram_usage_percent"] = ((ESP.getPsramSize() - ESP.getFreePsram()) * 100) / ESP.getPsramSize();
    } else {
        doc["psram_size"] = 0;
        doc["psram_usage_percent"] = 0;
    }
    
    return doc;
}

JsonDocument SystemMonitor::getStorageInfo() const {
    JsonDocument doc;
    
    doc["total_bytes"] = LittleFS.totalBytes();
    doc["used_bytes"] = LittleFS.usedBytes();
    doc["free_bytes"] = LittleFS.totalBytes() - LittleFS.usedBytes();
    doc["usage_percent"] = (LittleFS.usedBytes() * 100) / LittleFS.totalBytes();
    
    return doc;
}

// ==================== 私有方法实现 ====================

void SystemMonitor::updateMetrics() {
    currentMetrics.cpuUsagePercent = getCPUUsage();
    currentMetrics.memoryUsagePercent = getMemoryUsage();
    currentMetrics.freeHeapBytes = ESP.getFreeHeap();
    currentMetrics.minFreeHeapBytes = ESP.getMinFreeHeap();
    currentMetrics.maxAllocHeapBytes = ESP.getMaxAllocHeap();
    currentMetrics.psramUsagePercent = getPSRAMUsage();
    currentMetrics.cpuTemperature = getCPUTemperature();
    currentMetrics.uptimeSeconds = millis() / 1000;
    currentMetrics.wifiSignalStrength = getWiFiSignalStrength();
    currentMetrics.taskCount = getTaskCount();
    
    // 更新峰值指标
    if (currentMetrics.cpuUsagePercent > peakMetrics.cpuUsagePercent) {
        peakMetrics.cpuUsagePercent = currentMetrics.cpuUsagePercent;
    }
    if (currentMetrics.memoryUsagePercent > peakMetrics.memoryUsagePercent) {
        peakMetrics.memoryUsagePercent = currentMetrics.memoryUsagePercent;
    }
    if (currentMetrics.freeHeapBytes < peakMetrics.minFreeHeapBytes || peakMetrics.minFreeHeapBytes == 0) {
        peakMetrics.minFreeHeapBytes = currentMetrics.freeHeapBytes;
    }
    if (currentMetrics.maxAllocHeapBytes > peakMetrics.maxAllocHeapBytes) {
        peakMetrics.maxAllocHeapBytes = currentMetrics.maxAllocHeapBytes;
    }
    if (currentMetrics.cpuTemperature > peakMetrics.cpuTemperature) {
        peakMetrics.cpuTemperature = currentMetrics.cpuTemperature;
    }
    if (currentMetrics.taskCount > peakMetrics.taskCount) {
        peakMetrics.taskCount = currentMetrics.taskCount;
    }
}

void SystemMonitor::updateHealthStatus() {
    checkMemoryHealth();
    checkStorageHealth();
    checkWiFiHealth();
    checkTemperatureHealth();
    checkTasksHealth();
}

void SystemMonitor::addToHistory(const PerformanceMetrics& metrics) {
    metricsHistory.push_back(metrics);

    // 限制历史记录大小
    if (metricsHistory.size() > maxHistorySize) {
        metricsHistory.erase(metricsHistory.begin());
    }
}

uint32_t SystemMonitor::getCPUUsage() {
    // ESP32没有直接的CPU使用率API，这里使用简化的估算方法
    // 基于空闲任务的运行时间来估算
    static uint32_t lastIdleTime = 0;
    static uint32_t lastTotalTime = 0;

    uint32_t currentTime = millis();
    uint32_t idleTime = 0;  // 这里需要实际的空闲时间获取方法

    if (lastTotalTime > 0) {
        uint32_t totalTimeDiff = currentTime - lastTotalTime;
        uint32_t idleTimeDiff = idleTime - lastIdleTime;

        if (totalTimeDiff > 0) {
            uint32_t usage = 100 - ((idleTimeDiff * 100) / totalTimeDiff);
            lastIdleTime = idleTime;
            lastTotalTime = currentTime;
            return (usage > 100) ? 100 : usage;
        }
    }

    lastIdleTime = idleTime;
    lastTotalTime = currentTime;

    // 简化估算：基于任务数量和内存使用率
    uint32_t taskCount = getTaskCount();
    uint32_t memUsage = getMemoryUsage();
    return (taskCount * 5 + memUsage / 4) > 100 ? 100 : (taskCount * 5 + memUsage / 4);
}

uint32_t SystemMonitor::getMemoryUsage() {
    uint32_t totalHeap = ESP.getHeapSize();
    uint32_t freeHeap = ESP.getFreeHeap();

    if (totalHeap == 0) return 0;

    return ((totalHeap - freeHeap) * 100) / totalHeap;
}

uint32_t SystemMonitor::getPSRAMUsage() {
    uint32_t totalPsram = ESP.getPsramSize();

    if (totalPsram == 0) return 0;

    uint32_t freePsram = ESP.getFreePsram();
    return ((totalPsram - freePsram) * 100) / totalPsram;
}

float SystemMonitor::getCPUTemperature() {
    // ESP32-S3的温度传感器读取
    return temperatureRead();
}

int32_t SystemMonitor::getWiFiSignalStrength() {
    if (WiFi.status() == WL_CONNECTED) {
        return WiFi.RSSI();
    }
    return -100;  // 无连接时返回最弱信号
}

uint32_t SystemMonitor::getTaskCount() {
    return uxTaskGetNumberOfTasks();
}

void SystemMonitor::resetPeakMetrics() {
    peakMetrics = currentMetrics;
    logMonitor("INFO", "峰值指标已重置");
}

void SystemMonitor::clearHistory() {
    metricsHistory.clear();
    logMonitor("INFO", "历史数据已清空");
}

JsonDocument SystemMonitor::getMetricsHistory(uint32_t count) const {
    JsonDocument doc;
    JsonArray historyArray = doc["history"].to<JsonArray>();

    uint32_t startIndex = 0;
    if (count > 0 && count < metricsHistory.size()) {
        startIndex = metricsHistory.size() - count;
    }

    for (uint32_t i = startIndex; i < metricsHistory.size(); i++) {
        const PerformanceMetrics& metrics = metricsHistory[i];

        JsonObject metricObj = historyArray.add<JsonObject>();
        metricObj["cpu_usage"] = metrics.cpuUsagePercent;
        metricObj["memory_usage"] = metrics.memoryUsagePercent;
        metricObj["free_heap"] = metrics.freeHeapBytes;
        metricObj["cpu_temperature"] = metrics.cpuTemperature;
        metricObj["wifi_signal"] = metrics.wifiSignalStrength;
        metricObj["task_count"] = metrics.taskCount;
        metricObj["uptime"] = metrics.uptimeSeconds;
    }

    doc["total_records"] = metricsHistory.size();
    doc["returned_records"] = historyArray.size();

    return doc;
}

JsonDocument SystemMonitor::getTaskInfo() const {
    JsonDocument doc;

    doc["task_count"] = uxTaskGetNumberOfTasks();
    doc["high_water_mark"] = uxTaskGetStackHighWaterMark(nullptr);

    // 获取任务列表（简化版本）
    JsonArray tasksArray = doc["tasks"].to<JsonArray>();

    // 这里可以添加更详细的任务信息获取
    // 由于FreeRTOS任务信息获取比较复杂，这里提供基础信息

    return doc;
}

void SystemMonitor::setMemoryThresholds(uint32_t warningThreshold, uint32_t errorThreshold) {
    memoryWarningThreshold = warningThreshold;
    memoryErrorThreshold = errorThreshold;

    logMonitor("INFO", "内存阈值已更新 - 警告: " + String(warningThreshold) + "%, 错误: " + String(errorThreshold) + "%");
}

void SystemMonitor::setTemperatureThresholds(float warningThreshold, float errorThreshold) {
    temperatureWarningThreshold = warningThreshold;
    temperatureErrorThreshold = errorThreshold;

    logMonitor("INFO", "温度阈值已更新 - 警告: " + String(warningThreshold) + "°C, 错误: " + String(errorThreshold) + "°C");
}

void SystemMonitor::setWiFiSignalThreshold(int32_t warningThreshold) {
    wifiSignalWarningThreshold = warningThreshold;

    logMonitor("INFO", "WiFi信号阈值已更新 - 警告: " + String(warningThreshold) + "dBm");
}

void SystemMonitor::logMonitor(const String& level, const String& message) {
    String logMessage = "[MONITOR] " + message;

    if (level == "ERROR") {
        Serial.println("❌ " + logMessage);
    } else if (level == "WARN") {
        Serial.println("⚠️ " + logMessage);
    } else if (level == "INFO") {
        Serial.println("ℹ️ " + logMessage);
    } else if (level == "DEBUG" && DEBUG_LEVEL >= 4) {
        Serial.println("🔍 " + logMessage);
    }
}
