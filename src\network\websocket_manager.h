#ifndef WEBSOCKET_MANAGER_H
#define WEBSOCKET_MANAGER_H

/**
 * @file websocket_manager.h
 * @brief WebSocket管理器 - 实时通信管理
 * @details 管理WebSocket连接，发送实时事件到前端
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <ESPAsyncWebServer.h>
#include <ArduinoJson.h>
#include <vector>
#include <map>

#include "config/config.h"
#include "config/constants.h"
#include "storage/data_structures.h"

/**
 * @brief WebSocket管理器类
 * @details 管理WebSocket连接和实时事件推送
 */
class WebSocketManager {
private:
    AsyncWebServer wsServer;        // 独立的WebSocket服务器 (端口8001)
    AsyncWebSocket ws;              // WebSocket处理器
    
    // 客户端管理
    struct ClientInfo {
        uint32_t id;
        IPAddress ip;
        uint32_t connectTime;
        uint32_t lastPing;
        bool isActive;
        String userAgent;
        
        ClientInfo() : id(0), connectTime(0), lastPing(0), isActive(false) {}
    };
    
    std::map<uint32_t, ClientInfo> connectedClients;
    
    // 消息统计
    uint32_t totalMessagesSent;
    uint32_t totalMessagesReceived;
    uint32_t totalConnections;
    uint32_t totalDisconnections;
    
    // 服务器状态
    bool isRunning;
    uint32_t startTime;
    
    // 心跳检测
    uint32_t lastHeartbeatCheck;
    uint32_t heartbeatInterval;
    uint32_t clientTimeout;

    // 统计信息
    uint32_t messagesSent;

public:
    /**
     * @brief 构造函数
     */
    WebSocketManager();
    
    /**
     * @brief 析构函数
     */
    ~WebSocketManager();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * @brief 初始化WebSocket管理器
     * @return bool 初始化是否成功
     */
    bool begin();
    
    /**
     * @brief WebSocket管理器主循环
     */
    void loop();
    
    /**
     * @brief 停止WebSocket管理器
     */
    void stop();
    
    // ==================== 连接管理 ====================
    
    /**
     * @brief 获取连接的客户端数量
     * @return uint32_t 客户端数量
     */
    uint32_t getConnectedClientCount() const { return connectedClients.size(); }

    /**
     * @brief 获取已发送消息数量
     * @return uint32_t 消息数量
     */
    uint32_t getMessagesSentCount() const { return messagesSent; }
    
    /**
     * @brief 检查是否有活跃客户端
     * @return bool 是否有活跃客户端
     */
    bool hasActiveClients() const;
    
    /**
     * @brief 获取客户端信息
     * @param clientId 客户端ID
     * @return ClientInfo 客户端信息
     */
    ClientInfo getClientInfo(uint32_t clientId) const;
    
    /**
     * @brief 断开指定客户端
     * @param clientId 客户端ID
     */
    void disconnectClient(uint32_t clientId);
    
    /**
     * @brief 断开所有客户端
     */
    void disconnectAllClients();
    
    // ==================== 消息发送 ====================
    
    /**
     * @brief 广播消息到所有客户端
     * @param message 消息内容
     */
    void broadcastMessage(const String& message);
    
    /**
     * @brief 发送消息到指定客户端
     * @param clientId 客户端ID
     * @param message 消息内容
     */
    void sendToClient(uint32_t clientId, const String& message);
    
    /**
     * @brief 发送JSON消息到所有客户端
     * @param doc JSON文档
     */
    void broadcastJson(const JsonDocument& doc);
    
    /**
     * @brief 发送JSON消息到指定客户端
     * @param clientId 客户端ID
     * @param doc JSON文档
     */
    void sendJsonToClient(uint32_t clientId, const JsonDocument& doc);
    
    // ==================== 前端期望的WebSocket事件 ====================
    
    /**
     * @brief 发送连接成功事件
     * @param client 客户端指针
     */
    void sendConnectedEvent(AsyncWebSocketClient *client);
    
    /**
     * @brief 发送信号学习完成事件
     * @param signalData 信号数据
     */
    void sendSignalLearnedEvent(const JsonDocument& signalData);
    
    /**
     * @brief 发送信号发射完成事件
     * @param signalId 信号ID
     * @param success 是否成功
     */
    void sendSignalSentEvent(const String& signalId, bool success);
    
    /**
     * @brief 发送系统状态更新事件
     * @param statusData 状态数据
     */
    void sendStatusUpdateEvent(const JsonDocument& statusData);
    
    /**
     * @brief 发送错误事件
     * @param error 错误信息
     * @param context 错误上下文
     */
    void sendErrorEvent(const String& error, const String& context = "");
    
    /**
     * @brief 发送自定义事件
     * @param eventType 事件类型
     * @param payload 事件数据
     */
    void sendCustomEvent(const String& eventType, const JsonDocument& payload);

    /**
     * @brief 发送学习开始事件
     * @param timeout 学习超时时间(毫秒)
     */
    void sendLearningStartedEvent(uint32_t timeout);

    /**
     * @brief 发送学习超时事件
     */
    void sendLearningTimeoutEvent();

    /**
     * @brief 发送定时任务执行事件
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param success 执行是否成功
     */
    void sendTimerExecutedEvent(const String& taskId, const String& taskName, bool success);

    /**
     * @brief 发送定时器状态变更事件
     * @param enabled 是否启用
     */
    void sendTimerStatusEvent(bool enabled);
    
    // ==================== 状态查询 ====================
    
    /**
     * @brief 检查服务器是否运行
     * @return bool 是否运行
     */
    bool isServerRunning() const { return isRunning; }
    
    /**
     * @brief 获取服务器统计信息
     * @return JsonDocument 统计信息
     */
    JsonDocument getServerStats() const;
    
    /**
     * @brief 获取所有客户端信息
     * @return JsonDocument 客户端信息列表
     */
    JsonDocument getClientsInfo() const;
    
    // ==================== 配置管理 ====================
    
    /**
     * @brief 设置心跳检测间隔
     * @param interval 间隔时间(ms)
     */
    void setHeartbeatInterval(uint32_t interval) { heartbeatInterval = interval; }
    
    /**
     * @brief 设置客户端超时时间
     * @param timeout 超时时间(ms)
     */
    void setClientTimeout(uint32_t timeout) { clientTimeout = timeout; }

private:
    // ==================== 私有方法 ====================
    
    /**
     * @brief WebSocket事件处理器
     * @param server WebSocket服务器
     * @param client 客户端
     * @param type 事件类型
     * @param arg 参数
     * @param data 数据
     * @param len 数据长度
     */
    static void handleWebSocketEvent(AsyncWebSocket *server, AsyncWebSocketClient *client,
                                   AwsEventType type, void *arg, uint8_t *data, size_t len);
    
    /**
     * @brief 处理客户端连接
     * @param client 客户端
     */
    void handleClientConnect(AsyncWebSocketClient *client);
    
    /**
     * @brief 处理客户端断开
     * @param client 客户端
     */
    void handleClientDisconnect(AsyncWebSocketClient *client);
    
    /**
     * @brief 处理客户端消息
     * @param client 客户端
     * @param data 消息数据
     * @param len 数据长度
     */
    void handleClientMessage(AsyncWebSocketClient *client, uint8_t *data, size_t len);
    
    /**
     * @brief 处理客户端Pong
     * @param client 客户端
     */
    void handleClientPong(AsyncWebSocketClient *client);
    
    /**
     * @brief 心跳检测
     */
    void performHeartbeatCheck();
    
    /**
     * @brief 清理断开的客户端
     */
    void cleanupDisconnectedClients();
    
    /**
     * @brief 创建WebSocket消息
     * @param type 消息类型
     * @param payload 消息数据
     * @return String JSON消息字符串
     */
    String createWebSocketMessage(const String& type, const JsonDocument& payload);
    
    /**
     * @brief 获取客户端IP地址
     * @param client 客户端
     * @return IPAddress IP地址
     */
    IPAddress getClientIP(AsyncWebSocketClient *client);
    
    /**
     * @brief 记录WebSocket日志
     * @param level 日志级别
     * @param message 日志消息
     * @param clientId 客户端ID (可选)
     */
    void logWebSocket(const String& level, const String& message, uint32_t clientId = 0);

    /**
     * @brief 获取当前Unix时间戳(毫秒)
     * @return uint64_t Unix时间戳
     */
    uint64_t getCurrentUnixMs() const;

    // 静态实例指针，用于静态回调函数
    static WebSocketManager* instance;
};

#endif // WEBSOCKET_MANAGER_H
