#ifndef CONSTANTS_H
#define CONSTANTS_H

/**
 * @file constants.h
 * @brief 系统常量定义
 * @details 包含所有系统级常量、枚举、错误代码等
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>

// ==================== 系统常量 ====================
namespace SystemConstants {
    // 系统标识
    const char* const SYSTEM_NAME = "ESP32-S3 IR Control System";
    const char* const MANUFACTURER = "ESP32 Community";
    const char* const MODEL = "ESP32-S3-IR-V1.0";
    
    // 时间常量 (毫秒)
    const uint32_t SECOND = 1000;
    const uint32_t MINUTE = 60 * SECOND;
    const uint32_t HOUR = 60 * MINUTE;
    const uint32_t DAY = 24 * HOUR;
    
    // 重试常量
    const uint8_t MAX_RETRY_COUNT = 3;
    const uint32_t RETRY_DELAY = 1000;
    const uint32_t EXPONENTIAL_BACKOFF_BASE = 2;
}

// ==================== 网络常量 ====================
namespace NetworkConstants {
    // HTTP状态码
    const uint16_t HTTP_OK = 200;
    const uint16_t HTTP_CREATED = 201;
    const uint16_t HTTP_NO_CONTENT = 204;
    const uint16_t HTTP_BAD_REQUEST = 400;
    const uint16_t HTTP_UNAUTHORIZED = 401;
    const uint16_t HTTP_NOT_FOUND = 404;
    const uint16_t HTTP_METHOD_NOT_ALLOWED = 405;
    const uint16_t HTTP_INTERNAL_ERROR = 500;
    const uint16_t HTTP_SERVICE_UNAVAILABLE = 503;
    
    // 内容类型
    const char* const CONTENT_TYPE_JSON = "application/json";
    const char* const CONTENT_TYPE_TEXT = "text/plain";
    const char* const CONTENT_TYPE_HTML = "text/html";
    
    // CORS头
    const char* const CORS_ORIGIN = "*";
    const char* const CORS_METHODS = "GET, POST, PUT, DELETE, OPTIONS";
    const char* const CORS_HEADERS = "Content-Type, Authorization";
}

// ==================== 红外常量 ====================
namespace IRConstants {
    // 红外协议类型
    enum class Protocol : uint8_t {
        UNKNOWN = 0,
        NEC = 1,
        SONY = 2,
        RC5 = 3,
        RC6 = 4,
        SAMSUNG = 5,
        LG = 6,
        PANASONIC = 7,
        MITSUBISHI = 8,
        DISH = 9,
        SHARP = 10,
        COOLIX = 11,
        DAIKIN = 12,
        KELVINATOR = 13,
        MITSUBISHI_AC = 14,
        FUJITSU_AC = 15,
        GREE = 16,
        HAIER_AC = 17,
        HITACHI_AC = 18,
        WHIRLPOOL_AC = 19,
        ELECTRA_AC = 20
    };
    
    // 信号类型
    enum class SignalType : uint8_t {
        TV = 0,
        AC = 1,
        FAN = 2,
        LIGHT = 3,
        AUDIO = 4,
        OTHER = 5
    };
    
    // 学习状态
    enum class LearningState : uint8_t {
        IDLE = 0,
        WAITING = 1,
        LEARNING = 2,
        COMPLETED = 3,
        FAILED = 4,
        TIMEOUT = 5
    };
    
    // 发射状态
    enum class EmitState : uint8_t {
        IDLE = 0,
        PREPARING = 1,
        EMITTING = 2,
        COMPLETED = 3,
        FAILED = 4
    };
    
    // 默认值
    const uint32_t DEFAULT_FREQUENCY = 38000;  // 38kHz
    const uint8_t DEFAULT_DUTY_CYCLE = 33;     // 33%
    const uint16_t MAX_SIGNAL_LENGTH = 1000;   // 最大信号长度
    const uint16_t MIN_SIGNAL_LENGTH = 10;     // 最小信号长度
}

// ==================== 存储常量 ====================
namespace StorageConstants {
    // 文件扩展名
    const char* const JSON_EXT = ".json";
    const char* const LOG_EXT = ".log";
    const char* const BACKUP_EXT = ".bak";
    const char* const TEMP_EXT = ".tmp";
    
    // 文件大小限制 (字节)
    const uint32_t MAX_FILE_SIZE = 1024 * 1024;  // 1MB
    const uint32_t MAX_LOG_SIZE = 512 * 1024;    // 512KB
    const uint32_t MAX_CONFIG_SIZE = 64 * 1024;  // 64KB
    const uint32_t MAX_SIGNAL_SIZE = 4 * 1024;   // 4KB
    
    // 缓存设置
    const uint16_t CACHE_EXPIRE_TIME = 300;      // 5分钟
    const uint8_t MAX_CACHE_ENTRIES = 50;
}

// ==================== 错误代码 ====================
namespace ErrorCodes {
    enum class System : uint16_t {
        SUCCESS = 0,
        UNKNOWN_ERROR = 1000,
        INITIALIZATION_FAILED = 1001,
        MEMORY_ALLOCATION_FAILED = 1002,
        TASK_CREATION_FAILED = 1003,
        WATCHDOG_TIMEOUT = 1004,
        SYSTEM_OVERLOAD = 1005
    };
    
    enum class Network : uint16_t {
        WIFI_CONNECTION_FAILED = 2000,
        HTTP_SERVER_START_FAILED = 2001,
        WEBSOCKET_CONNECTION_FAILED = 2002,
        REQUEST_TIMEOUT = 2003,
        INVALID_REQUEST = 2004,
        RATE_LIMIT_EXCEEDED = 2005
    };
    
    enum class IR : uint16_t {
        IR_INIT_FAILED = 3000,
        LEARNING_TIMEOUT = 3001,
        LEARNING_FAILED = 3002,
        EMIT_FAILED = 3003,
        INVALID_SIGNAL = 3004,
        PROTOCOL_NOT_SUPPORTED = 3005
    };
    
    enum class Storage : uint16_t {
        FILESYSTEM_INIT_FAILED = 4000,
        FILE_NOT_FOUND = 4001,
        FILE_READ_ERROR = 4002,
        FILE_WRITE_ERROR = 4003,
        DISK_FULL = 4004,
        INVALID_JSON = 4005
    };
}

// ==================== 日志级别 ====================
namespace LogLevel {
    enum Level : uint8_t {
        NONE = 0,
        ERROR = 1,
        WARN = 2,
        INFO = 3,
        DEBUG = 4,
        VERBOSE = 5
    };
    
    const char* const LEVEL_NAMES[] = {
        "NONE", "ERROR", "WARN", "INFO", "DEBUG", "VERBOSE"
    };
}

// ==================== 性能指标 ====================
namespace PerformanceMetrics {
    // 内存阈值
    const uint32_t CRITICAL_HEAP_SIZE = 30000;   // 30KB
    const uint32_t WARNING_HEAP_SIZE = 50000;    // 50KB
    const uint32_t NORMAL_HEAP_SIZE = 100000;    // 100KB
    
    // 响应时间阈值 (毫秒)
    const uint32_t FAST_RESPONSE = 100;
    const uint32_t NORMAL_RESPONSE = 500;
    const uint32_t SLOW_RESPONSE = 1000;
    const uint32_t TIMEOUT_RESPONSE = 5000;
    
    // CPU使用率阈值 (百分比)
    const uint8_t LOW_CPU_USAGE = 30;
    const uint8_t NORMAL_CPU_USAGE = 60;
    const uint8_t HIGH_CPU_USAGE = 80;
    const uint8_t CRITICAL_CPU_USAGE = 95;
}

// ==================== 字符串常量 ====================
namespace StringConstants {
    // API端点
    const char* const API_STATUS = "/api/status";
    const char* const API_SIGNALS = "/api/signals";
    const char* const API_LEARNING = "/api/learning";
    const char* const API_EMIT = "/api/emit/signal";
    const char* const API_BATCH = "/api/batch";

    // 定时器API端点
    const char* const API_TIMER_TASKS = "/api/timer/tasks";
    const char* const API_TIMER_ENABLE = "/api/timer/enable";
    const char* const API_TIMER_STATUS = "/api/timer/status";
    
    // WebSocket事件类型
    const char* const WS_CONNECTED = "connected";
    const char* const WS_DISCONNECTED = "disconnected";
    const char* const WS_SIGNAL_LEARNED = "signal_learned";
    const char* const WS_SIGNAL_SENT = "signal_sent";
    const char* const WS_STATUS_UPDATE = "status_update";
    const char* const WS_ERROR = "error";
    
    // JSON字段名
    const char* const JSON_SUCCESS = "success";
    const char* const JSON_DATA = "data";
    const char* const JSON_ERROR = "error";
    const char* const JSON_MESSAGE = "message";
    const char* const JSON_TIMESTAMP = "timestamp";
    const char* const JSON_TYPE = "type";
    const char* const JSON_PAYLOAD = "payload";
    
    // 系统消息
    const char* const MSG_SYSTEM_READY = "System ready";
    const char* const MSG_WIFI_CONNECTED = "WiFi connected";
    const char* const MSG_SERVER_STARTED = "Server started";
    const char* const MSG_LEARNING_STARTED = "Learning started";
    const char* const MSG_SIGNAL_EMITTED = "Signal emitted";
}

#endif // CONSTANTS_H
