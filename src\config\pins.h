#ifndef PINS_H
#define PINS_H

/**
 * @file pins.h
 * @brief ESP32-S3引脚定义和硬件配置
 * @details 专门针对ESP32-S3 WROOM-1-N16R8的引脚分配
 * @version 1.0.0
 * @date 2025-01-07
 */

// ==================== ESP32-S3引脚映射 ====================

// ==================== 红外相关引脚 ====================
#define IR_RECV_PIN         4    // 红外接收器数据引脚 (GPIO4)
#define IR_SEND_PIN         5    // 红外发射器数据引脚 (GPIO5)
#define IR_POWER_PIN        6    // 红外发射器电源控制引脚 (GPIO6, 可选)

// ==================== 状态指示引脚 ====================
#define STATUS_LED_PIN      2    // 系统状态LED (GPIO2, 内置LED)
#define WIFI_LED_PIN        8    // WiFi状态LED (GPIO8, 可选)
#define IR_LED_PIN          9    // 红外状态LED (GPIO9, 可选)

// ==================== 用户交互引脚 ====================
#define LEARN_BUTTON_PIN    0    // 学习按钮 (GPIO0, BOOT按钮)
#define RESET_BUTTON_PIN    1    // 重置按钮 (GPIO1, 可选)
#define MODE_SWITCH_PIN     10   // 模式切换开关 (GPIO10, 可选)

// ==================== 扩展接口引脚 ====================
#define I2C_SDA_PIN         21   // I2C数据线 (GPIO21)
#define I2C_SCL_PIN         22   // I2C时钟线 (GPIO22)
#define SPI_MOSI_PIN        23   // SPI主出从入 (GPIO23)
#define SPI_MISO_PIN        19   // SPI主入从出 (GPIO19)
#define SPI_CLK_PIN         18   // SPI时钟 (GPIO18)
#define SPI_CS_PIN          5    // SPI片选 (GPIO5)

// ==================== 调试和测试引脚 ====================
#define DEBUG_TX_PIN        43   // 调试串口发送 (GPIO43, USB串口)
#define DEBUG_RX_PIN        44   // 调试串口接收 (GPIO44, USB串口)
#define TEST_PIN_1          11   // 测试引脚1 (GPIO11)
#define TEST_PIN_2          12   // 测试引脚2 (GPIO12)

// ==================== 模拟输入引脚 ====================
#define ADC_BATTERY_PIN     1    // 电池电压检测 (GPIO1/ADC1_CH0)
#define ADC_TEMP_PIN        2    // 温度传感器 (GPIO2/ADC1_CH1, 可选)
#define ADC_LIGHT_PIN       3    // 光线传感器 (GPIO3/ADC1_CH2, 可选)

// ==================== 保留和特殊引脚 ====================
// 以下引脚在ESP32-S3中有特殊用途，避免使用

// USB相关引脚 (ESP32-S3内置USB)
#define USB_DM_PIN          19   // USB D- (GPIO19)
#define USB_DP_PIN          20   // USB D+ (GPIO20)

// PSRAM相关引脚 (我们禁用了PSRAM，但仍需避免使用)
#define PSRAM_CLK_PIN       30   // PSRAM时钟
#define PSRAM_CS_PIN        26   // PSRAM片选
#define PSRAM_DQ0_PIN       27   // PSRAM数据0
#define PSRAM_DQ1_PIN       28   // PSRAM数据1
#define PSRAM_DQ2_PIN       29   // PSRAM数据2
#define PSRAM_DQ3_PIN       32   // PSRAM数据3

// Flash相关引脚 (系统使用，不可更改)
#define FLASH_CLK_PIN       6    // Flash时钟
#define FLASH_CS_PIN        7    // Flash片选
#define FLASH_DQ0_PIN       8    // Flash数据0
#define FLASH_DQ1_PIN       9    // Flash数据1
#define FLASH_DQ2_PIN       10   // Flash数据2
#define FLASH_DQ3_PIN       11   // Flash数据3

// ==================== 引脚状态定义 ====================
#define PIN_HIGH            1
#define PIN_LOW             0
#define PIN_INPUT           INPUT
#define PIN_OUTPUT          OUTPUT
#define PIN_INPUT_PULLUP    INPUT_PULLUP
#define PIN_INPUT_PULLDOWN  INPUT_PULLDOWN

// ==================== 引脚功能验证宏 ====================
/**
 * @brief 验证引脚是否可用于GPIO功能
 * @param pin 引脚号
 * @return true 如果引脚可用，false 如果引脚被保留
 */
#define IS_VALID_GPIO_PIN(pin) \
    ((pin >= 0 && pin <= 48) && \
     (pin != 6 && pin != 7 && pin != 8 && pin != 9 && pin != 10 && pin != 11) && \
     (pin != 26 && pin != 27 && pin != 28 && pin != 29 && pin != 30 && pin != 32))

/**
 * @brief 验证引脚是否可用于ADC功能
 * @param pin 引脚号
 * @return true 如果引脚支持ADC
 */
#define IS_ADC_PIN(pin) \
    ((pin >= 1 && pin <= 10) || (pin >= 11 && pin <= 20))

/**
 * @brief 验证引脚是否可用于PWM功能
 * @param pin 引脚号
 * @return true 如果引脚支持PWM
 */
#define IS_PWM_PIN(pin) \
    IS_VALID_GPIO_PIN(pin)

// ==================== 硬件特性定义 ====================
#define ESP32_S3_GPIO_COUNT     49   // ESP32-S3总GPIO数量
#define ESP32_S3_ADC_COUNT      20   // ADC通道数量
#define ESP32_S3_PWM_COUNT      8    // PWM通道数量
#define ESP32_S3_I2C_COUNT      2    // I2C控制器数量
#define ESP32_S3_SPI_COUNT      3    // SPI控制器数量
#define ESP32_S3_UART_COUNT     3    // UART控制器数量

// ==================== 引脚初始化宏 ====================
/**
 * @brief 初始化红外相关引脚
 */
#define INIT_IR_PINS() do { \
    pinMode(IR_RECV_PIN, INPUT); \
    pinMode(IR_SEND_PIN, OUTPUT); \
    digitalWrite(IR_SEND_PIN, LOW); \
} while(0)

/**
 * @brief 初始化状态LED引脚
 */
#define INIT_STATUS_LEDS() do { \
    pinMode(STATUS_LED_PIN, OUTPUT); \
    digitalWrite(STATUS_LED_PIN, LOW); \
} while(0)

/**
 * @brief 初始化用户按钮引脚
 */
#define INIT_USER_BUTTONS() do { \
    pinMode(LEARN_BUTTON_PIN, INPUT_PULLUP); \
} while(0)

#endif // PINS_H
