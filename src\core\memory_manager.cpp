#include "memory_manager.h"
#include "system_manager.h"

// 静态成员初始化
MemoryManager* MemoryManager::instance = nullptr;

MemoryManager::MemoryManager() 
    : memoryMutex(nullptr)
    , monitoringEnabled(false)
    , lastCheckTime(0)
    , checkInterval(5000)
    , lowMemoryThreshold(PerformanceMetrics::WARNING_HEAP_SIZE)
    , criticalMemoryThreshold(PerformanceMetrics::CRITICAL_HEAP_SIZE) {
}

MemoryManager& MemoryManager::getInstance() {
    if (!instance) {
        instance = new MemoryManager();
    }
    return *instance;
}

MemoryManager::~MemoryManager() {
    stop();
    
    // 清理所有内存池
    for (auto& poolPair : memoryPools) {
        cleanupPool(poolPair.first);
    }
    
    if (memoryMutex) {
        vSemaphoreDelete(memoryMutex);
    }
}

bool MemoryManager::begin() {
    Serial.println("🧠 初始化内存管理器...");
    
    // 创建互斥锁
    memoryMutex = xSemaphoreCreateMutex();
    if (!memoryMutex) {
        Serial.println("❌ 内存管理器互斥锁创建失败");
        return false;
    }
    
    // 初始化内存池
    initializePool(SYSTEM_POOL, 2500000, 1024000);      // 2.5MB 系统池
    initializePool(NETWORK_POOL, 1500000, 512000);      // 1.5MB 网络池
    initializePool(IR_POOL, 2000000, 256000);           // 2MB 红外池
    initializePool(STORAGE_POOL, 1000000, 128000);      // 1MB 存储池
    initializePool(TEMP_POOL, 1000000, 64000);          // 1MB 临时池
    
    // 更新初始统计信息
    updateMemoryStats();
    
    // 启用监控
    enableMonitoring();
    
    Serial.printf("✅ 内存管理器初始化完成 - 总堆内存: %d bytes\n", currentStats.totalHeap);
    return true;
}

void MemoryManager::loop() {
    if (!monitoringEnabled) {
        return;
    }
    
    uint32_t currentTime = millis();
    if (currentTime - lastCheckTime >= checkInterval) {
        lastCheckTime = currentTime;
        
        if (xSemaphoreTake(memoryMutex, pdMS_TO_TICKS(10)) == pdTRUE) {
            // 更新内存统计
            updateMemoryStats();
            
            // 检查内存警告
            checkMemoryWarnings();
            
            // 自动清理临时内存
            if (currentStats.freeHeap < lowMemoryThreshold) {
                cleanupTempMemory();
            }
            
            // 紧急清理
            if (currentStats.freeHeap < criticalMemoryThreshold) {
                emergencyCleanup();
            }
            
            xSemaphoreGive(memoryMutex);
        }
    }
}

void MemoryManager::stop() {
    monitoringEnabled = false;
    Serial.println("🛑 内存管理器已停止");
}

void* MemoryManager::allocate(size_t size, MemoryPoolType pool, const String& tag) {
    if (size == 0) {
        return nullptr;
    }
    
    void* ptr = nullptr;
    
    if (xSemaphoreTake(memoryMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        // 检查内存池容量
        auto& poolInfo = memoryPools[pool];
        if (poolInfo.usedSize + size > poolInfo.totalSize) {
            Serial.printf("⚠️ 内存池 %s 容量不足\n", getPoolName(pool).c_str());
            xSemaphoreGive(memoryMutex);
            return nullptr;
        }
        
        // 分配内存
        ptr = malloc(size);
        if (ptr) {
            // 创建内存块记录
            MemoryBlock* block = new MemoryBlock();
            block->ptr = ptr;
            block->size = size;
            block->pool = pool;
            block->tag = tag;
            block->allocTime = millis();
            block->inUse = true;
            
            // 更新内存池信息
            poolInfo.blocks.push_back(block);
            poolInfo.usedSize += size;
            poolInfo.allocCount++;
            
            if (size > poolInfo.maxBlockSize) {
                poolInfo.maxBlockSize = size;
            }
            
            // 更新全局统计
            currentStats.allocCount++;
            
            if (DEBUG_LEVEL >= 4) {
                Serial.printf("🔹 分配内存: %d bytes, 池: %s, 标签: %s\n", 
                              size, getPoolName(pool).c_str(), tag.c_str());
            }
        } else {
            Serial.printf("❌ 内存分配失败: %d bytes\n", size);
        }
        
        xSemaphoreGive(memoryMutex);
    }
    
    return ptr;
}

bool MemoryManager::deallocate(void* ptr) {
    if (!ptr) {
        return false;
    }
    
    bool success = false;
    
    if (xSemaphoreTake(memoryMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        MemoryBlock* block = findMemoryBlock(ptr);
        if (block) {
            // 更新内存池信息
            auto& poolInfo = memoryPools[block->pool];
            poolInfo.usedSize -= block->size;
            poolInfo.freeCount++;
            
            // 从内存池中移除
            auto it = std::find(poolInfo.blocks.begin(), poolInfo.blocks.end(), block);
            if (it != poolInfo.blocks.end()) {
                poolInfo.blocks.erase(it);
            }
            
            // 释放内存
            free(ptr);
            delete block;
            
            // 更新全局统计
            currentStats.freeCount++;
            success = true;
            
            if (DEBUG_LEVEL >= 4) {
                Serial.printf("🔸 释放内存: %s\n", getPoolName(block->pool).c_str());
            }
        } else {
            Serial.println("⚠️ 尝试释放未知内存块");
        }
        
        xSemaphoreGive(memoryMutex);
    }
    
    return success;
}

void* MemoryManager::reallocate(void* ptr, size_t newSize) {
    if (!ptr) {
        return allocate(newSize);
    }
    
    if (newSize == 0) {
        deallocate(ptr);
        return nullptr;
    }
    
    void* newPtr = nullptr;
    
    if (xSemaphoreTake(memoryMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        MemoryBlock* block = findMemoryBlock(ptr);
        if (block) {
            MemoryPoolType pool = block->pool;
            String tag = block->tag;
            size_t oldSize = block->size;
            
            // 检查新大小是否超出内存池限制
            auto& poolInfo = memoryPools[pool];
            if (poolInfo.usedSize - oldSize + newSize > poolInfo.totalSize) {
                Serial.printf("⚠️ 重新分配超出内存池容量\n");
                xSemaphoreGive(memoryMutex);
                return nullptr;
            }
            
            // 重新分配内存
            newPtr = realloc(ptr, newSize);
            if (newPtr) {
                // 更新内存块信息
                block->ptr = newPtr;
                block->size = newSize;
                
                // 更新内存池信息
                poolInfo.usedSize = poolInfo.usedSize - oldSize + newSize;
                
                if (newSize > poolInfo.maxBlockSize) {
                    poolInfo.maxBlockSize = newSize;
                }
                
                if (DEBUG_LEVEL >= 4) {
                    Serial.printf("🔄 重新分配内存: %d -> %d bytes\n", oldSize, newSize);
                }
            }
        }
        
        xSemaphoreGive(memoryMutex);
    }
    
    return newPtr;
}

void* MemoryManager::allocateAligned(size_t size, size_t alignment, MemoryPoolType pool) {
    size_t alignedSize = alignSize(size, alignment);
    void* ptr = allocate(alignedSize, pool, "AlignedMemory");
    
    if (ptr) {
        // 确保对齐
        uintptr_t addr = (uintptr_t)ptr;
        uintptr_t alignedAddr = (addr + alignment - 1) & ~(alignment - 1);
        
        if (alignedAddr != addr) {
            // 如果需要调整对齐，重新分配
            deallocate(ptr);
            ptr = allocate(alignedSize + alignment, pool, "AlignedMemory");
            if (ptr) {
                addr = (uintptr_t)ptr;
                alignedAddr = (addr + alignment - 1) & ~(alignment - 1);
                ptr = (void*)alignedAddr;
            }
        }
    }
    
    return ptr;
}

void* MemoryManager::allocateNetworkBuffer(size_t size, const String& tag) {
    return allocate(size, NETWORK_POOL, tag);
}

void* MemoryManager::allocateIRBuffer(size_t size, const String& tag) {
    return allocate(size, IR_POOL, tag);
}

void* MemoryManager::allocateJsonBuffer(size_t size, const String& tag) {
    return allocate(size, SYSTEM_POOL, tag);
}

void* MemoryManager::allocateTemp(size_t size, const String& tag) {
    return allocate(size, TEMP_POOL, tag);
}

bool MemoryManager::initializePool(MemoryPoolType pool, size_t totalSize, size_t maxBlockSize) {
    if (xSemaphoreTake(memoryMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        MemoryPool& poolInfo = memoryPools[pool];
        poolInfo.name = getPoolName(pool);
        poolInfo.totalSize = totalSize;
        poolInfo.maxBlockSize = maxBlockSize;
        poolInfo.usedSize = 0;
        poolInfo.allocCount = 0;
        poolInfo.freeCount = 0;
        poolInfo.blocks.clear();
        
        Serial.printf("📦 初始化内存池: %s (%d bytes)\n", 
                      poolInfo.name.c_str(), totalSize);
        
        xSemaphoreGive(memoryMutex);
        return true;
    }
    
    return false;
}

bool MemoryManager::cleanupPool(MemoryPoolType pool) {
    if (xSemaphoreTake(memoryMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        auto& poolInfo = memoryPools[pool];
        
        // 释放所有内存块
        for (auto* block : poolInfo.blocks) {
            if (block->ptr) {
                free(block->ptr);
            }
            delete block;
        }
        
        poolInfo.blocks.clear();
        poolInfo.usedSize = 0;
        
        Serial.printf("🧹 清理内存池: %s\n", poolInfo.name.c_str());
        
        xSemaphoreGive(memoryMutex);
        return true;
    }

    return false;
}

// ==================== 私有方法实现 ====================

void MemoryManager::updateMemoryStats() {
    currentStats.totalHeap = ESP.getHeapSize();
    currentStats.freeHeap = ESP.getFreeHeap();
    currentStats.minFreeHeap = ESP.getMinFreeHeap();
    currentStats.maxAllocHeap = ESP.getMaxAllocHeap();

    // 计算碎片数量 (简化估算)
    if (currentStats.freeHeap > 0 && currentStats.maxAllocHeap > 0) {
        currentStats.fragmentCount = currentStats.freeHeap / currentStats.maxAllocHeap;
    }
}

void MemoryManager::checkMemoryWarnings() {
    size_t freeHeap = ESP.getFreeHeap();

    if (freeHeap < criticalMemoryThreshold) {
        Serial.printf("🚨 关键内存警告: %d bytes 可用\n", freeHeap);

        // 发送系统事件
        SystemManager& systemManager = SystemManager::getInstance();
        JsonDocument eventData;
        eventData["level"] = "critical";
        eventData["free_heap"] = freeHeap;
        eventData["threshold"] = criticalMemoryThreshold;
        systemManager.sendSystemEvent("memory_warning", eventData);

    } else if (freeHeap < lowMemoryThreshold) {
        Serial.printf("⚠️ 低内存警告: %d bytes 可用\n", freeHeap);

        // 发送系统事件
        SystemManager& systemManager = SystemManager::getInstance();
        JsonDocument eventData;
        eventData["level"] = "low";
        eventData["free_heap"] = freeHeap;
        eventData["threshold"] = lowMemoryThreshold;
        systemManager.sendSystemEvent("memory_warning", eventData);
    }
}

MemoryManager::MemoryBlock* MemoryManager::findMemoryBlock(void* ptr) {
    for (auto& poolPair : memoryPools) {
        for (auto* block : poolPair.second.blocks) {
            if (block->ptr == ptr) {
                return block;
            }
        }
    }
    return nullptr;
}

String MemoryManager::getPoolName(MemoryPoolType pool) const {
    switch (pool) {
        case SYSTEM_POOL:
            return "System";
        case NETWORK_POOL:
            return "Network";
        case IR_POOL:
            return "IR";
        case STORAGE_POOL:
            return "Storage";
        case TEMP_POOL:
            return "Temp";
        default:
            return "Unknown";
    }
}

size_t MemoryManager::alignSize(size_t size, size_t alignment) const {
    return (size + alignment - 1) & ~(alignment - 1);
}

void MemoryManager::printMemoryReport() const {
    Serial.println("\n📊 ===== 内存使用报告 =====");

    MemoryStats stats = getMemoryStats();
    Serial.printf("总堆内存: %d bytes\n", stats.totalHeap);
    Serial.printf("可用堆内存: %d bytes (%.1f%%)\n",
                  stats.freeHeap, (float)stats.freeHeap / stats.totalHeap * 100.0);
    Serial.printf("最小可用堆: %d bytes\n", stats.minFreeHeap);
    Serial.printf("最大可分配: %d bytes\n", stats.maxAllocHeap);
    Serial.printf("总分配次数: %d\n", stats.allocCount);
    Serial.printf("总释放次数: %d\n", stats.freeCount);
    Serial.printf("碎片估算: %d\n", stats.fragmentCount);

    Serial.println("\n📦 内存池详情:");
    for (const auto& poolPair : memoryPools) {
        const MemoryPool& pool = poolPair.second;
        Serial.printf("  %s: %d/%d bytes (%.1f%%), %d 块\n",
                      pool.name.c_str(),
                      pool.usedSize,
                      pool.totalSize,
                      (float)pool.usedSize / pool.totalSize * 100.0,
                      pool.blocks.size());
    }

    Serial.println("========================\n");
}

JsonDocument MemoryManager::getMemoryLeakReport() const {
    JsonDocument doc;

    uint32_t currentTime = millis();
    JsonArray leaks = doc["potential_leaks"].to<JsonArray>();

    if (xSemaphoreTake(memoryMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        for (const auto& poolPair : memoryPools) {
            const MemoryPool& pool = poolPair.second;

            for (const auto* block : pool.blocks) {
                // 检查长时间未释放的内存块 (超过10分钟)
                if (currentTime - block->allocTime > 600000) {
                    JsonObject leak = leaks.add<JsonObject>();
                    leak["pool"] = pool.name;
                    leak["size"] = block->size;
                    leak["tag"] = block->tag;
                    leak["age_ms"] = currentTime - block->allocTime;
                    leak["ptr"] = String((uintptr_t)block->ptr, HEX);
                }
            }
        }

        xSemaphoreGive(memoryMutex);
    }

    doc["leak_count"] = leaks.size();
    doc["scan_time"] = currentTime;

    return doc;
}

bool MemoryManager::validateMemoryIntegrity() const {
    bool isValid = true;

    if (xSemaphoreTake(memoryMutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        for (const auto& poolPair : memoryPools) {
            const MemoryPool& pool = poolPair.second;

            // 检查内存池统计的一致性
            size_t calculatedUsed = 0;
            for (const auto* block : pool.blocks) {
                if (block->inUse) {
                    calculatedUsed += block->size;
                }

                // 检查内存块的有效性
                if (!block->ptr || block->size == 0) {
                    Serial.printf("❌ 发现无效内存块: 池=%s, ptr=%p, size=%d\n",
                                  pool.name.c_str(), block->ptr, block->size);
                    isValid = false;
                }
            }

            if (calculatedUsed != pool.usedSize) {
                Serial.printf("❌ 内存池统计不一致: %s, 计算=%d, 记录=%d\n",
                              pool.name.c_str(), calculatedUsed, pool.usedSize);
                isValid = false;
            }
        }

        xSemaphoreGive(memoryMutex);
    }

    return isValid;
}
