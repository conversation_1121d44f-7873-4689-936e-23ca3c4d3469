# R1系统架构标准文档

**基于ESP32S3-WROOM-N16R8优化的事件驱动架构**

## 📋 概述

本文档基于R1智能红外控制系统的实际实现，规范化当前已验证的架构设计。该架构专门为ESP32S3-WROOM-N16R8硬件平台优化，遵循事件驱动、按需分配、模块解耦的核心原则。

### 🎯 设计原则

1. **事件驱动优先** - 避免无意义循环，所有功能通过事件触发
2. **按需资源分配** - 需要时分配，用完释放，避免资源浪费
3. **模块完全解耦** - 模块间零直接依赖，通过事件通信
4. **硬件性能优化** - 针对ESP32S3双核240MHz处理器和8MB内存优化

## 🏗️ 核心架构组件

### 1. EventBus - 事件驱动核心

EventBus是系统的通信中枢，所有模块间通信都通过事件机制实现。

#### 核心特性
- **Map存储优化** - O(1)查找效率，适合ESP32S3内存特性
- **异步事件处理** - 充分利用双核架构
- **优先级支持** - 关键事件优先处理
- **性能统计** - 内置轻量级性能监控

#### 标准接口
```javascript
class EventBus {
  on(event, handler, options = {})     // 监听事件
  once(event, handler)                 // 一次性监听
  off(event, handler)                  // 取消监听
  emit(event, data = null)             // 发布事件
  clear()                              // 清空所有监听器
  getPerformance()                     // 获取性能统计
}
```

### 2. BaseModule - 统一模块基类

所有模块必须继承BaseModule，确保统一的生命周期和接口标准。

#### 标准生命周期
```javascript
class BaseModule {
  constructor(eventBus, esp32, moduleName)  // 构造函数
  async init()                              // 初始化入口
  async setupEventListeners()               // 事件监听器设置（子类实现）
  async setupUI()                           // UI设置（子类实现）
  async loadModuleData()                    // 数据加载（子类实现）
  destroy()                                 // 销毁清理
}
```

#### 统一接口方法
```javascript
// 通信接口
emitEvent(eventType, data)              // 发布事件（自动添加source字段）
async requestESP32(endpoint, options)   // ESP32请求

// 状态管理
handleError(error, operation)           // 错误处理
handleSuccess(message, operation, data) // 成功处理
getStatus()                             // 获取模块状态

// 系统控制
emergencyStop()                         // 紧急停止
async refresh()                         // 刷新数据
```

#### emitEvent方法详细说明
```javascript
// BaseModule.emitEvent() 实现原理
emitEvent(eventType, data = null) {
  // 自动添加source字段（如果数据中没有）
  if (data && typeof data === 'object' && !data.source) {
    data.source = this.moduleName;
  }

  // 直接发送原始数据，不包装
  this.eventBus.emit(eventType, data);
}

// 使用示例
this.emitEvent('signal.selected', {
  signalId: 'signal_001',
  signalName: '客厅电视开关'
});
// 实际发送的数据：{ source: 'SignalManager', signalId: 'signal_001', signalName: '客厅电视开关' }
```

### 3. ESP32Communicator - 硬件通信层

专门为ESP32S3硬件通信优化的通信管理器。

#### 核心功能
- **连接管理** - 自动重连、超时处理、优雅降级
- **批量请求** - 减少通信开销，提高效率
- **错误恢复** - 指数退避重连机制
- **性能优化** - 请求队列和批量处理

## 📊 统一数据结构标准

### 1. 事件数据结构

**重要更新：基于实际实现的统一事件数据结构**

所有事件数据都直接传递原始数据，不进行包装。BaseModule.emitEvent()会自动添加source字段：

```javascript
// 发送事件时的数据结构
const EventDataStructure = {
  source: "事件源模块名",           // BaseModule自动添加
  // ... 其他业务数据字段
}

// 接收事件时的数据结构
eventBus.on('event.name', (data) => {
  // data 就是发送时的原始数据
  const source = data.source;        // 事件源模块
  const businessData = data.xxx;     // 业务数据
});
```

**核心原则：**
- ✅ **直接传递** - 发送什么数据，接收方就得到什么数据
- ✅ **自动source** - BaseModule.emitEvent()自动添加source字段
- ✅ **无包装** - 不进行额外的数据包装，保持简洁高效
- ✅ **一致性** - 所有模块使用相同的数据传递方式

#### 标准事件类型
```javascript
const STANDARD_EVENTS = {
  // 系统级事件
  'system.ready': '系统启动完成',
  'system.error': '系统错误',
  'system.refresh': '系统刷新',

  // 模块级事件
  'module.ready': '模块就绪',
  'module.error': '模块错误',
  'module.success': '模块操作成功',
  'module.switch': '模块切换',

  // ESP32通信事件
  'esp32.connected': 'ESP32连接成功',
  'esp32.disconnected': 'ESP32连接断开',
  'esp32.error': 'ESP32通信错误',
  'esp32.request.success': '请求成功',
  'esp32.request.error': '请求失败'
}
```

#### 事件处理最佳实践

**发送事件：**
```javascript
// ✅ 正确方式 - 使用BaseModule.emitEvent()
this.emitEvent('signal.selected', {
  signalId: 'signal_001',
  signalName: '客厅电视开关',
  timestamp: Date.now()
});

// ❌ 错误方式 - 直接使用eventBus.emit()
this.eventBus.emit('signal.selected', {
  source: this.moduleName,  // 手动添加source容易出错
  data: { signalId: 'signal_001' }  // 不必要的包装
});
```

**接收事件：**
```javascript
// ✅ 正确方式 - 直接解构数据
this.eventBus.on('signal.selected', (data) => {
  const { source, signalId, signalName } = data;
  console.log(`来自${source}的信号选择: ${signalName}`);
});

// ❌ 错误方式 - 假设数据被包装
this.eventBus.on('signal.selected', (eventData) => {
  const data = eventData.data || eventData;  // 不必要的兼容处理
  const signalId = data.signalId;
});
```

### 2. 模块状态数据结构

#### 基础状态结构（BaseModule提供）
```javascript
class BaseModule {
  constructor() {
    // 基础状态（所有模块必需）
    this.state = {
      loading: false,           // 加载状态
      error: null,              // 错误信息
      data: new Map()           // 通用数据存储
    };

    // 生命周期状态
    this.isInitialized = false; // 是否已初始化
    this.isActive = false;      // 是否激活状态

    // 性能统计
    this.performance = {
      initTime: 0,              // 初始化耗时
      operationCount: 0,        // 操作计数
      errorCount: 0,            // 错误计数
      lastOperation: null       // 最后操作
    };
  }
}
```

#### 模块特有状态（推荐实践）
```javascript
class SignalManager extends BaseModule {
  constructor() {
    super();

    // 业务数据（推荐直接属性，性能更好）
    this.signals = new Map();          // 信号数据
    this.selectedSignals = new Set();  // 选中信号

    // 业务状态
    this.currentView = 'grid';         // 当前视图
    this.isMultiSelectMode = false;    // 多选模式

    // 复杂业务状态
    this.learningState = {
      isLearning: false,
      hasUnsavedSignal: false,
      // ... 其他学习相关状态
    };
  }
}
```

### 3. ESP32通信数据结构

#### 请求格式
```javascript
const ESP32RequestStructure = {
  endpoint: "/api/endpoint",     // API端点
  method: "GET|POST|PUT|DELETE", // HTTP方法
  headers: {},                   // 请求头
  body: {},                     // 请求体
  timeout: 10000                // 超时时间(ms)
}
```

#### 响应格式
```javascript
const ESP32ResponseStructure = {
  success: true,                // 是否成功
  data: {},                     // 响应数据
  error: null,                  // 错误信息
  timestamp: 1234567890,        // 响应时间
  responseTime: 123             // 响应耗时(ms)
}
```

## 🔧 模块开发标准

### 1. 新模块开发流程

#### 步骤1: 创建模块类
```javascript
class NewModule extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'NewModule');
    
    // 模块特有属性
    this.moduleData = new Map();
    this.moduleConfig = {};
  }
}
```

#### 步骤2: 实现必需方法
```javascript
// 事件监听器设置（必需实现）
async setupEventListeners() {
  // 监听系统事件（推荐）
  this.eventBus.on('system.refresh', () => {
    this.refresh();
  });

  // 监听模块特定事件
  this.eventBus.on('newmodule.action', (data) => {
    this.handleAction(data);
  });
}

// UI设置（必需实现）
async setupUI() {
  // 事件委托（推荐模式）
  this.initEventDelegation();

  // 输入元素初始化
  this.initInputElements();

  // 渲染初始界面
  this.renderInitialUI();
}

// 数据加载（可选实现）
async loadModuleData() {
  try {
    // 从本地存储加载
    this.loadFromStorage();

    // 从ESP32加载
    await this.loadFromESP32();

    // 渲染数据
    this.renderData();
  } catch (error) {
    this.handleError(error, '数据加载');
  }
}
```

#### 步骤3: 注册到系统
```javascript
// 在main.js中添加模块配置
const moduleConfigs = [
  // 现有模块...
  { name: 'newModule', class: NewModule }
];
```

### 2. 模块命名规范

- **类名**: PascalCase (如: SignalManager)
- **实例名**: camelCase (如: signalManager)  
- **DOM ID**: kebab-case (如: signal-manager)
- **事件名**: 点分隔 (如: module.ready)

### 3. DOM操作规范

#### 直接DOM操作标准
```javascript
// 推荐的DOM操作方式
const element = $('#elementId');           // 单元素查询
const elements = $$('.className');        // 多元素查询

// 事件委托模式
container.addEventListener('click', (e) => {
  const actionElement = e.target.closest('[data-action]');
  if (actionElement) {
    this.handleAction(actionElement.dataset.action);
  }
});
```

#### addEventListener使用规范

**✅ 允许使用addEventListener** - 这是浏览器标准API，性能最佳，调试友好

**规范要求：**

1. **使用位置规范**
   ```javascript
   class SomeModule extends BaseModule {
     async setupEventListeners() {
       // ✅ 只在这里使用addEventListener
       this.elements.button.addEventListener('click', this.handleClick.bind(this));

       // ✅ 事件委托模式
       this.elements.container.addEventListener('click', (e) => {
         const target = e.target.closest('[data-action]');
         if (target) this.handleAction(target.dataset.action);
       });

       // ✅ 监听其他模块事件
       this.eventBus.on('other.module.event', this.handleOtherEvent.bind(this));
     }
   }
   ```

2. **事件清理要求**
   ```javascript
   class SomeModule extends BaseModule {
     constructor() {
       super();
       this.boundHandlers = new Map(); // 存储绑定的处理器
     }

     async setupEventListeners() {
       // 存储绑定的处理器以便清理
       this.boundHandlers.set('click', this.handleClick.bind(this));
       this.elements.button.addEventListener('click', this.boundHandlers.get('click'));
     }

     destroy() {
       // ✅ 必须清理所有事件监听器
       this.boundHandlers.forEach((handler, eventType) => {
         this.elements.button.removeEventListener(eventType, handler);
       });
       this.boundHandlers.clear();

       super.destroy();
     }
   }
   ```

3. **模块间通信规范**
   ```javascript
   handleClick() {
     // ✅ 处理自己的UI逻辑
     this.updateUI();

     // ✅ 通过EventBus通知其他模块
     this.emitEvent('this.module.clicked', { data: 'some data' });

     // ❌ 禁止直接调用其他模块
     // this.modules.otherModule.doSomething();
   }
   ```

**架构优势：**
- 🚀 **性能最佳** - 浏览器原生API，无额外开销
- 🔍 **调试友好** - 开发者工具完美支持，错误堆栈清晰
- 🏗️ **模块解耦** - 每个模块独立管理自己的事件，通过EventBus通信
- 📈 **扩展性强** - 新模块独立开发，不影响现有模块

## 🌐 通信协议标准

### 1. 模块间通信

**核心原则：模块间通信只能通过EventBus，禁止直接调用**

```javascript
// ✅ 正确方式 - 使用BaseModule.emitEvent()
this.emitEvent('signal.selected', {
  signalId: 'signal_001',
  signalName: '客厅电视开关'
});

// ✅ 正确方式 - 状态查询（自动添加source）
this.emitEvent('timer.status.request', {
  requestId: 'req_001',
  callback: (response) => { /* 处理响应 */ }
});

// ❌ 错误方式 - 直接调用其他模块
this.modules.signalManager.selectSignal('signal_001');

// ❌ 错误方式 - 直接访问其他模块状态
if (window.R1System.modules.timerSettings.isEnabled) { ... }

// ❌ 错误方式 - 手动包装数据
this.eventBus.emit('signal.selected', {
  source: this.moduleName,
  data: { signalId: 'signal_001' }  // 不必要的包装
});
```

#### 解耦通信示例
```javascript
// SignalManager暂停其他模块
pauseOtherModules() {
  // 发布暂停请求，让各模块自行响应
  this.emitEvent('system.pause.request', {
    source: 'SignalLearning',
    reason: '信号学习模式启动'
  });

  this.emitEvent('timer.pause.request', { source: 'SignalLearning' });
  this.emitEvent('control.pause.request', { source: 'SignalLearning' });
}

// 其他模块响应暂停请求
async setupEventListeners() {
  this.eventBus.on('timer.pause.request', (data) => {
    if (data.source === 'SignalLearning') {
      this.pauseTimer();
    }
  });
}
```

### 2. ESP32通信标准

#### 统一请求方法
```javascript
// 在BaseModule中使用
const response = await this.requestESP32('/api/signals', {
  method: 'POST',
  body: JSON.stringify(signalData)
});
```

#### 错误处理标准
```javascript
try {
  const result = await this.requestESP32('/api/action');
  this.handleSuccess('操作成功', '信号发送', result);
} catch (error) {
  this.handleError(error, '信号发送');
}
```

## 📈 性能优化标准

### 1. 禁止轮询和实时监控

**核心原则：严格禁止使用轮询或实时监控方式，除非是唯一可能的技术实现**

```javascript
// ❌ 禁止的轮询模式
setInterval(() => {
  checkSystemStatus();  // 无意义的定时检查
}, 1000);

// ✅ 推荐的事件驱动模式
this.eventBus.on('system.status.changed', (data) => {
  updateStatusDisplay(data);  // 状态变化时才更新
});
```

#### 1.1 技术约束例外

以下情况由于技术限制，允许使用轮询方式：

**实时系统时间显示**：
- **技术原因**：Web环境无法监听系统时间变化事件，轮询是唯一可行方案
- **实现方式**：
  - `UnifiedTimerManager`: 100ms间隔检查定时器到期（系统级调度引擎）
  - `system_time_update`: 1秒间隔更新时间显示（UI更新任务）
- **性能影响**：CPU占用 < 0.02%，内存占用 < 1KB，完全可忽略
- **架构地位**：与错误收集器同等级别的系统必需功能
- **标记要求**：代码中必须添加 `// 技术约束例外：实时时间显示轮询` 注释

**错误收集功能**：
- **技术原因**：需要持续监控和收集系统错误
- **架构地位**：系统核心功能，不受轮询限制约束

#### 1.2 轮询检测规则

开发过程中必须检测和消除所有非必要轮询：
- `setInterval()` 和 `setTimeout()` 的重复调用
- 实时状态检查和数据轮询
- 定时DOM更新和UI刷新
- 除技术约束例外外的所有轮询行为

### 2. DOM操作优化

```javascript
// ✅ 使用DocumentFragment减少重排重绘
const fragment = document.createDocumentFragment();
elements.forEach(el => fragment.appendChild(el));
container.appendChild(fragment);

// ✅ 缓存DOM查询结果
this.cachedElements = {
  container: $('#container'),
  buttons: $$('.button')
};
```

### 3. 内存管理

```javascript
// ✅ 模块销毁时清理资源
destroy() {
  // 清理事件监听器
  this.eventBus.off('event.name');
  
  // 清理数据
  this.moduleData.clear();
  
  // 调用父类销毁方法
  super.destroy();
}
```

## ⚠️ 常见问题与解决方案

### 1. 事件数据结构不一致问题

**问题描述：**
在早期实现中，可能存在事件处理器中使用了不一致的数据解构方式，导致运行时错误。

**错误示例：**
```javascript
// ❌ 错误的事件处理方式
this.eventBus.on('control.emit.progress', (eventData) => {
  const data = eventData.data || eventData;  // 假设数据被包装
  const task = data.task;
  console.log('任务信息:', rawEventData);     // 使用了未定义的变量
});
```

**正确修复：**
```javascript
// ✅ 正确的事件处理方式
this.eventBus.on('control.emit.progress', (data) => {
  const task = data.task;                    // 直接使用data参数
  const signal = data.currentSignal;
  console.log('任务信息:', data);            // 使用正确的变量名
});
```

### 2. 事件处理器错误排查

**排查步骤：**
1. 检查控制台是否有 `Event handler error for xxx: {}` 错误
2. 确认事件处理器中的变量名是否正确
3. 验证数据解构是否使用了正确的字段名
4. 确保没有访问未定义的变量

**预防措施：**
- 统一使用 `data` 作为事件处理器参数名
- 直接解构数据，不使用包装处理
- 使用 `console.log` 调试时确保变量名正确

## 🔍 质量保证标准

### 1. 错误处理分级

```javascript
const ERROR_LEVELS = {
  INFO: '信息',      // 一般信息，不影响功能
  WARN: '警告',      // 可能的问题，系统可继续运行
  ERROR: '错误',     // 功能错误，需要处理
  FATAL: '致命'      // 系统级错误，需要重启
}
```

### 2. 性能监控指标

```javascript
const PERFORMANCE_METRICS = {
  initTime: '初始化耗时',        // < 100ms
  responseTime: '响应耗时',      // < 50ms
  memoryUsage: '内存使用',       // < 80%
  errorRate: '错误率'           // < 1%
}
```

## 🚀 扩展开发指南

### 1. 新模块检查清单

- [ ] 继承BaseModule基类
- [ ] 实现必需方法：`setupEventListeners()`, `setupUI()`, `loadModuleData()`
- [ ] 遵循命名规范
- [ ] 使用BaseModule基础状态结构
- [ ] 业务数据推荐直接属性（性能考虑）
- [ ] 纯EventBus通信，无直接模块访问
- [ ] 包含完整错误处理
- [ ] 更新性能统计
- [ ] 编写文档注释

### 2. 代码审查标准

- [ ] 无直接模块依赖（严格要求）
- [ ] 无直接访问 `window.R1System.modules.*`
- [ ] 无无意义循环
- [ ] 使用BaseModule基础状态 + 直接属性模式
- [ ] 错误处理完整，更新性能统计
- [ ] 事件委托模式，性能优化
- [ ] 代码注释清晰

### 3. 最佳实践参考

**SignalManager** 是完美的参考实现：
- ✅ 完全解耦：纯事件通信，零直接依赖
- ✅ 性能优化：直接属性访问，事件委托，渲染缓存
- ✅ 架构合规：继承BaseModule，实现所有必需方法
- ✅ 功能完整：信号管理、学习、导入导出、批量操作
- ✅ 代码质量：清晰的结构，完整的错误处理

## 📝 总结

本架构文档基于R1系统的实际实现，规范化了已验证的优秀架构设计。该架构充分利用ESP32S3硬件特性，通过事件驱动机制实现高效、稳定、可扩展的系统架构。

所有新模块开发都应严格遵循本文档标准，确保系统的一致性和可维护性。

---

**文档版本**: 1.2
**最后更新**: 2025年6月17日
**适用平台**: ESP32S3-WROOM-N16R8
**系统版本**: R1 v1.0

### 版本更新记录

**v1.2 (2025-06-17)**
- ✅ 添加轮询约束和技术例外规范
- ✅ 明确实时时间显示的技术约束例外地位
- ✅ 规范轮询检测规则和标记要求
- ✅ 更新性能优化标准章节

**v1.1 (2025-06-17)**
- ✅ 更新事件数据结构标准，基于实际实现
- ✅ 明确BaseModule.emitEvent()的工作原理
- ✅ 添加事件处理最佳实践
- ✅ 增加常见问题与解决方案章节
- ✅ 统一所有模块的事件处理方式
