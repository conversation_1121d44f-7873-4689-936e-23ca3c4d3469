#ifndef CONFIG_H
#define CONFIG_H

/**
 * @file config.h
 * @brief ESP32-S3红外控制系统全局配置文件
 * @details 包含所有系统配置、硬件定义、网络设置等
 * @version 1.0.0
 * @date 2025-01-07
 */

// ==================== 硬件引脚定义 ====================
#define IR_RECV_PIN         4    // 红外接收引脚 (GPIO4)
#define IR_SEND_PIN         5    // 红外发射引脚 (GPIO5)
#define STATUS_LED_PIN      2    // 状态LED引脚 (GPIO2)

// ==================== 网络配置 ====================
// 匹配前端期望的端口配置
#define HTTP_PORT           8000  // HTTP API端口
#define WEBSOCKET_PORT      8001  // WebSocket端口 (前端期望 ws://127.0.0.1:8001/ws)
#define WEBSOCKET_PATH      "/ws"

// WiFi配置
#define WIFI_SSID           "ESP32-IR-Control"  // 默认AP模式SSID
#define WIFI_PASSWORD       "12345678"          // 默认AP模式密码
#define WIFI_CHANNEL        1                   // WiFi信道
#define WIFI_MAX_CLIENTS    4                   // 最大客户端连接数
#define WIFI_CONNECT_TIMEOUT 10000              // WiFi连接超时(ms)

// ==================== 系统配置 ====================
#define MAX_SIGNALS         1000  // 最大信号数量
#define SIGNAL_CACHE_SIZE   100   // 信号缓存大小
#define IR_LEARNING_TIMEOUT 30000 // 学习超时时间(ms)
#define SYSTEM_UPDATE_INTERVAL 5000 // 系统状态更新间隔(ms)

// ==================== 内存配置 ====================
#define HTTP_BUFFER_SIZE    4096  // HTTP缓冲区大小
#define WS_BUFFER_SIZE      2048  // WebSocket缓冲区大小
#define JSON_BUFFER_SIZE    2048  // JSON缓冲区大小
#define IR_BUFFER_SIZE      1024  // 红外数据缓冲区大小

// ==================== 文件系统路径 ====================
#define SIGNALS_DIR         "/signals"  // 信号存储目录
#define CONFIG_DIR          "/config"   // 配置文件目录
#define LOGS_DIR            "/logs"     // 日志文件目录
#define TEMP_DIR            "/temp"     // 临时文件目录

// 具体文件路径
#define WIFI_CONFIG_FILE    "/config/wifi.json"
#define SYSTEM_CONFIG_FILE  "/config/system.json"
#define SIGNALS_INDEX_FILE  "/signals/index.json"

// ==================== 任务配置 ====================
// 任务优先级 (数值越高优先级越高)
#define NETWORK_TASK_PRIORITY    2
#define IR_TASK_PRIORITY         3
#define SYSTEM_TASK_PRIORITY     1
#define STORAGE_TASK_PRIORITY    1

// 任务堆栈大小 - 优化内存使用
#define NETWORK_TASK_STACK       6144   // 6KB - 网络处理需要较大堆栈
#define IR_TASK_STACK           5120   // 5KB - 红外信号处理
#define SYSTEM_TASK_STACK       3072   // 3KB - 系统监控任务
#define STORAGE_TASK_STACK      4096   // 4KB - 文件系统操作需要足够堆栈

// 任务核心分配 - 优化负载均衡
// Core 0: 网络任务 + 系统任务 (与WiFi协议栈协同工作)
// Core 1: 红外任务 + 存储任务 (硬件相关任务)
#define NETWORK_TASK_CORE       0  // 网络任务运行在Core 0 (与WiFi协议栈协同)
#define IR_TASK_CORE           1  // 红外任务运行在Core 1 (硬件操作)
#define SYSTEM_TASK_CORE       0  // 系统任务运行在Core 0 (系统监控)
#define STORAGE_TASK_CORE      1  // 存储任务运行在Core 1 (文件系统操作)

// ==================== 调试配置 ====================
#define DEBUG_LEVEL             3    // 0=无, 1=错误, 2=警告, 3=信息, 4=调试
#define ENABLE_SERIAL_DEBUG     true
#define ENABLE_WEBSOCKET_DEBUG  true
#define ENABLE_PERFORMANCE_LOG  true

// ==================== 红外配置 ====================
#define IR_FREQUENCY           38000  // 默认红外载波频率 (Hz)
#define IR_DUTY_CYCLE          33     // 红外占空比 (%)
#define IR_TIMEOUT             150    // 红外接收超时 (100ms单位)
#define IR_TOLERANCE           25     // 红外信号容差 (%)

// ==================== 版本信息 ====================
#define FIRMWARE_VERSION    "1.0.0"
#define API_VERSION         "1.0"
#define BUILD_DATE          __DATE__
#define BUILD_TIME          __TIME__

// ==================== 安全配置 ====================
#define MAX_REQUEST_SIZE    8192      // 最大请求大小
#define MAX_CONCURRENT_REQUESTS 10    // 最大并发请求数
#define HTTP_REQUEST_TIMEOUT 30000    // 请求超时时间(ms)
#define RATE_LIMIT_REQUESTS 100       // 速率限制：每分钟最大请求数

// ==================== 性能配置 ====================
#define SYSTEM_WATCHDOG_TIMEOUT 120   // 看门狗超时时间(秒) - 增加到2分钟
#define HEAP_CHECK_INTERVAL 60000     // 堆内存检查间隔(ms)
#define MIN_FREE_HEAP       40000     // 最小可用堆内存(字节) - 降低阈值

#endif // CONFIG_H
