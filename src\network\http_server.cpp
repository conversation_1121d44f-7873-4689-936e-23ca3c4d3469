#include "http_server.h"
#include "api_handlers.h"
#include "core/system_manager.h"
#include "core/time_manager.h"
#include "core/timer_manager_backend.h"
#include "core/task_scheduler.h"
#include "ir/ir_manager.h"
#include "storage/signal_storage.h"
#include "network/websocket_manager.h"
#include <Update.h>

// 辅助函数：格式化运行时间
String formatUptime(uint32_t seconds) {
    uint32_t days = seconds / 86400;
    uint32_t hours = (seconds % 86400) / 3600;
    uint32_t minutes = (seconds % 3600) / 60;
    uint32_t secs = seconds % 60;

    if (days > 0) {
        return String(days) + "天" + String(hours) + "小时" + String(minutes) + "分钟";
    } else if (hours > 0) {
        return String(hours) + "小时" + String(minutes) + "分钟";
    } else if (minutes > 0) {
        return String(minutes) + "分钟" + String(secs) + "秒";
    } else {
        return String(secs) + "秒";
    }
}

HTTPAPIServer::HTTPAPIServer() 
    : server(HTTP_PORT)
    , systemManager(nullptr)
    , irManager(nullptr)
    , signalStorage(nullptr)
    , taskScheduler(nullptr)
    , isRunning(false)
    , requestCount(0)
    , errorCount(0)
    , startTime(0) {
}

HTTPAPIServer::~HTTPAPIServer() {
    stop();
}

bool HTTPAPIServer::begin() {
    Serial.printf("🌐 初始化HTTP服务器 (端口: %d)...\n", HTTP_PORT);
    
    // 获取组件引用
    systemManager = &SystemManager::getInstance();
    irManager = systemManager->getIRManager();
    signalStorage = systemManager->getSignalStorage();
    taskScheduler = &TaskScheduler::getInstance();
    
    // 设置CORS
    setupCORS();
    
    // 设置路由
    setupRoutes();
    
    // 设置静态文件服务
    setupStaticFiles();
    
    // 设置404处理
    server.onNotFound(APIHandlers::handleNotFound);
    
    // 启动服务器
    server.begin();
    
    isRunning = true;
    startTime = millis();
    
    Serial.printf("✅ HTTP服务器启动成功: http://%s:%d\n", 
                  WiFi.localIP().toString().c_str(), HTTP_PORT);
    
    return true;
}

void HTTPAPIServer::loop() {
    // HTTP服务器是异步的，不需要在loop中处理
    // 这里可以添加一些统计信息更新或清理工作
    
    static uint32_t lastStatsUpdate = 0;
    uint32_t currentTime = millis();
    
    if (currentTime - lastStatsUpdate > 60000) {  // 每分钟更新一次统计
        lastStatsUpdate = currentTime;
        
        // 清理速率限制映射
        auto it = rateLimitMap.begin();
        while (it != rateLimitMap.end()) {
            if (currentTime - it->lastResetTime > 60000) {  // 1分钟过期
                it = rateLimitMap.erase(it);
            } else {
                ++it;
            }
        }
    }
}

void HTTPAPIServer::stop() {
    if (isRunning) {
        Serial.println("🛑 停止HTTP服务器");
        server.end();
        isRunning = false;
    }
}

JsonDocument HTTPAPIServer::getServerStats() const {
    JsonDocument doc;
    
    doc["is_running"] = isRunning;
    doc["port"] = HTTP_PORT;
    doc["uptime"] = (millis() - startTime) / 1000;
    doc["total_requests"] = requestCount;
    doc["error_count"] = errorCount;
    doc["success_rate"] = (requestCount > 0) ? 
                         ((float)(requestCount - errorCount) / requestCount * 100.0) : 100.0;
    doc["active_rate_limits"] = rateLimitMap.size();
    
    return doc;
}

void HTTPAPIServer::setupRoutes() {
    Serial.println("🛣️ 设置API路由...");

    // ==================== 系统管理API ====================

    // GET /api/status - 系统状态查询
    server.on("/api/status", HTTP_GET, handleGetStatus);

    // GET /api/system/stats - 系统统计信息
    server.on("/api/system/stats", HTTP_GET, handleGetSystemStats);

    // GET /api/system/memory - 内存使用详情
    server.on("/api/system/memory", HTTP_GET, handleGetMemoryStats);

    // GET /api/system/performance - 性能指标
    server.on("/api/system/performance", HTTP_GET, handleGetPerformanceStats);

    // GET /api/system/disk - 磁盘使用率
    server.on("/api/system/disk", HTTP_GET, handleGetDiskStats);

    // GET /api/system/tasks - 任务状态查询
    server.on("/api/system/tasks", HTTP_GET, handleGetTaskStatus);
    
    // ==================== 信号管理API ====================
    
    // GET /api/signals - 获取信号列表
    server.on("/api/signals", HTTP_GET, handleGetSignals);
    
    // POST /api/signals - 创建信号
    server.on("/api/signals", HTTP_POST,
              nullptr,  // 不处理无请求体的请求
              nullptr,  // 不处理文件上传
              APIHandlers::handleCreateSignal);  // 处理有请求体的请求

    // PUT /api/signals/{id} - 更新信号
    server.on("^\\/api\\/signals\\/([a-zA-Z0-9_]+)$", HTTP_PUT,
              [](AsyncWebServerRequest *request) {
                  request->send(400, "application/json", "{\"error\":\"Missing body\"}");
              },
              nullptr,
              APIHandlers::handleUpdateSignal);

    // DELETE /api/signals/{id} - 删除信号
    server.on("^\\/api\\/signals\\/([a-zA-Z0-9_]+)$", HTTP_DELETE, APIHandlers::handleDeleteSignal);
    
    // POST /api/signals/clear - 清空所有信号
    server.on("/api/signals/clear", HTTP_POST, handleClearSignals);

    // GET /api/signals/export - 导出信号
    server.on("/api/signals/export", HTTP_GET, APIHandlers::handleExportSignals);

    // POST /api/signals/import - 导入信号
    server.on("/api/signals/import", HTTP_POST,
              [](AsyncWebServerRequest *request) {},
              [](AsyncWebServerRequest *request, const String& filename, size_t index, uint8_t *data, size_t len, bool final) {},
              APIHandlers::handleImportSignals);
    
    // ==================== 信号学习API ====================
    
    // POST /api/learning - 信号学习控制
    Serial.println("🔧 注册路由: POST /api/learning");
    server.on("/api/learning", HTTP_POST,
              [](AsyncWebServerRequest *request) {
                  request->send(400, "application/json", "{\"error\":\"Missing body\"}");
              },
              nullptr,
              APIHandlers::handleLearningControl);
    Serial.println("✅ 路由已注册: POST /api/learning");

    // POST /api/learning/detect - 信号检测
    Serial.println("🔧 注册路由: POST /api/learning/detect");
    server.on("/api/learning/detect", HTTP_POST,
              [](AsyncWebServerRequest *request) {
                  request->send(400, "application/json", "{\"error\":\"Missing body\"}");
              },
              nullptr,
              APIHandlers::handleLearningDetect);
    Serial.println("✅ 路由已注册: POST /api/learning/detect");
    
    // ==================== 信号发射API ====================
    
    // POST /api/emit/signal - 信号发射
    Serial.println("🔧 注册路由: POST /api/emit/signal");
    server.on("/api/emit/signal", HTTP_POST,
              [](AsyncWebServerRequest *request) {
                  request->send(400, "application/json", "{\"error\":\"Missing body\"}");
              },
              nullptr,
              APIHandlers::handleEmitSignal);
    Serial.println("✅ 路由已注册: POST /api/emit/signal");

    // ==================== 定时器管理API ====================

    // GET /api/timer/tasks - 获取定时任务列表
    server.on("/api/timer/tasks", HTTP_GET, handleGetTimerTasks);

    // POST /api/timer/tasks - 创建定时任务
    server.on("/api/timer/tasks", HTTP_POST,
              [](AsyncWebServerRequest *request) {
                  request->send(400, "application/json", "{\"error\":\"Missing body\"}");
              },
              nullptr,
              APIHandlers::handleCreateTimerTask);

    // PUT /api/timer/tasks/{id} - 更新定时任务
    server.on("^\\/api\\/timer\\/tasks\\/([a-zA-Z0-9_]+)$", HTTP_PUT,
              [](AsyncWebServerRequest *request) {
                  request->send(400, "application/json", "{\"error\":\"Missing body\"}");
              },
              nullptr,
              APIHandlers::handleUpdateTimerTask);

    // DELETE /api/timer/tasks/{id} - 删除定时任务
    server.on("^\\/api\\/timer\\/tasks\\/([a-zA-Z0-9_]+)$", HTTP_DELETE, APIHandlers::handleDeleteTimerTask);

    // POST /api/timer/enable - 启用/禁用定时器系统
    server.on("/api/timer/enable", HTTP_POST,
              [](AsyncWebServerRequest *request) {
                  request->send(400, "application/json", "{\"error\":\"Missing body\"}");
              },
              nullptr,
              APIHandlers::handleTimerEnable);

    // GET /api/timer/status - 获取定时器系统状态
    server.on("/api/timer/status", HTTP_GET, handleGetTimerStatus);

    // POST /api/timer/tasks/{id}/execute - 手动执行定时任务
    server.on("^\\/api\\/timer\\/tasks\\/([a-zA-Z0-9_]+)\\/execute$", HTTP_POST, handleExecuteTimerTask);

    // POST /api/timer/tasks/{id}/enable - 启用/禁用单个任务
    server.on("^\\/api\\/timer\\/tasks\\/([a-zA-Z0-9_]+)\\/enable$", HTTP_POST,
              [](AsyncWebServerRequest *request) {},
              nullptr,
              handleTimerTaskEnable);

    // ==================== 批量操作API ====================

    // POST /api/batch - 批量操作
    server.on("/api/batch", HTTP_POST,
              [](AsyncWebServerRequest *request) {},
              nullptr,
              APIHandlers::handleBatchOperation);

    // ==================== 信号分组管理API ====================

    // GET /api/groups - 获取信号分组列表
    server.on("/api/groups", HTTP_GET, APIHandlers::handleGetSignalGroups);

    // POST /api/groups - 创建信号分组
    server.on("/api/groups", HTTP_POST,
              [](AsyncWebServerRequest *request) {},
              nullptr,
              APIHandlers::handleCreateSignalGroup);

    // PUT /api/groups/{id} - 更新信号分组
    server.on("^\\/api\\/groups\\/([a-zA-Z0-9_-]+)$", HTTP_PUT,
              [](AsyncWebServerRequest *request) {},
              nullptr,
              APIHandlers::handleUpdateSignalGroup);

    // DELETE /api/groups/{id} - 删除信号分组
    server.on("^\\/api\\/groups\\/([a-zA-Z0-9_-]+)$", HTTP_DELETE, APIHandlers::handleDeleteSignalGroup);

    // ==================== 用户配置管理API ====================

    // GET /api/config - 获取用户配置
    server.on("/api/config", HTTP_GET, APIHandlers::handleGetUserConfig);

    // PUT /api/config - 更新用户配置
    server.on("/api/config", HTTP_PUT,
              [](AsyncWebServerRequest *request) {},
              nullptr,
              APIHandlers::handleUpdateUserConfig);

    // POST /api/config/reset - 重置配置为默认值
    server.on("/api/config/reset", HTTP_POST, APIHandlers::handleResetUserConfig);

    // ==================== 系统备份恢复API ====================

    // GET /api/backup - 导出系统备份
    server.on("/api/backup", HTTP_GET, APIHandlers::handleExportBackup);

    // POST /api/restore - 导入系统备份
    server.on("/api/restore", HTTP_POST,
              [](AsyncWebServerRequest *request) {},
              nullptr,
              APIHandlers::handleImportBackup);

    // ==================== 固件更新API ====================

    // GET /api/firmware/info - 获取固件信息
    server.on("/api/firmware/info", HTTP_GET, handleGetFirmwareInfo);

    // POST /api/firmware/update - 固件更新
    server.on("/api/firmware/update", HTTP_POST,
              [](AsyncWebServerRequest *request) {},
              handleFirmwareUpload,
              nullptr);

    // POST /api/firmware/check - 检查固件更新
    server.on("/api/firmware/check", HTTP_POST, handleCheckFirmwareUpdate);

    // POST /api/system/restart - 系统重启
    server.on("/api/system/restart", HTTP_POST, handleSystemRestart);

    // ==================== 错误日志管理API ====================

    // GET /api/logs - 获取系统日志
    server.on("/api/logs", HTTP_GET, APIHandlers::handleGetSystemLogs);

    // DELETE /api/logs - 清空系统日志
    server.on("/api/logs", HTTP_DELETE, APIHandlers::handleClearSystemLogs);

    // GET /api/logs/download - 下载日志文件
    server.on("/api/logs/download", HTTP_GET, APIHandlers::handleDownloadLogs);

    // ==================== OPTIONS处理 (CORS预检) ====================
    
    server.on("/api/status", HTTP_OPTIONS, APIHandlers::handleOptions);
    server.on("/api/signals", HTTP_OPTIONS, APIHandlers::handleOptions);
    server.on("/api/signals/export", HTTP_OPTIONS, APIHandlers::handleOptions);
    server.on("/api/signals/import", HTTP_OPTIONS, APIHandlers::handleOptions);
    server.on("/api/learning", HTTP_OPTIONS, APIHandlers::handleOptions);
    server.on("/api/emit/signal", HTTP_OPTIONS, APIHandlers::handleOptions);
    server.on("/api/timer/tasks", HTTP_OPTIONS, APIHandlers::handleOptions);
    server.on("/api/timer/enable", HTTP_OPTIONS, APIHandlers::handleOptions);
    server.on("/api/timer/status", HTTP_OPTIONS, APIHandlers::handleOptions);
    server.on("/api/batch", HTTP_OPTIONS, APIHandlers::handleOptions);
    
    Serial.println("✅ API路由设置完成");
}

void HTTPAPIServer::setupCORS() {
    // 设置全局CORS头
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Origin", "*");
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    DefaultHeaders::Instance().addHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
    DefaultHeaders::Instance().addHeader("Access-Control-Max-Age", "86400");


}

void HTTPAPIServer::setupStaticFiles() {
    // 静态文件服务：直接从data目录提供前端文件
    // 调试阶段使用未压缩文件，便于实时修改和调试

    // 检查前端文件是否存在（在根目录或/www目录）
    if (LittleFS.exists("/index.html")) {
        // 前端文件在根目录
        auto& staticHandler = server.serveStatic("/", LittleFS, "/");
        staticHandler.setDefaultFile("index.html");
        staticHandler.setCacheControl("max-age=86400");

        Serial.println("📁 静态文件服务已启用 (根目录) - 调试模式");
    } else if (LittleFS.exists("/www/index.html")) {
        // 前端文件在/www目录
        auto& staticHandler = server.serveStatic("/", LittleFS, "/www/");
        staticHandler.setDefaultFile("index.html");
        staticHandler.setCacheControl("max-age=86400");

        Serial.println("📁 静态文件服务已启用 (/www目录) - 调试模式");
    } else {
        // 没有前端文件时，提供API信息页面
        server.on("/", HTTP_GET, [](AsyncWebServerRequest *request) {
            String html = R"(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>ESP32-S3红外控制系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .status { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .api-list { background: #f8f9fa; padding: 20px; border-radius: 5px; }
        .api-item { margin: 10px 0; padding: 10px; background: white; border-radius: 3px; }
        .method { display: inline-block; padding: 2px 8px; border-radius: 3px; color: white; font-size: 12px; }
        .get { background: #28a745; }
        .post { background: #007bff; }
        .put { background: #ffc107; color: black; }
        .delete { background: #dc3545; }
        code { background: #f1f1f1; padding: 2px 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 ESP32-S3红外控制系统</h1>

        <div class="status">
            <h3>✅ 系统状态：正常运行</h3>
            <p><strong>版本：</strong>1.0.0</p>
            <p><strong>HTTP API：</strong>http://192.168.4.1:8000</p>
            <p><strong>WebSocket：</strong>ws://192.168.4.1:8001/ws</p>
        </div>

        <div class="api-list">
            <h3>📡 可用API端点</h3>

            <div class="api-item">
                <span class="method get">GET</span>
                <code>/api/status</code> - 获取系统状态
            </div>

            <div class="api-item">
                <span class="method get">GET</span>
                <code>/api/signals</code> - 获取信号列表
            </div>

            <div class="api-item">
                <span class="method post">POST</span>
                <code>/api/signals</code> - 创建新信号
            </div>

            <div class="api-item">
                <span class="method put">PUT</span>
                <code>/api/signals/{id}</code> - 更新信号
            </div>

            <div class="api-item">
                <span class="method delete">DELETE</span>
                <code>/api/signals/{id}</code> - 删除信号
            </div>

            <div class="api-item">
                <span class="method post">POST</span>
                <code>/api/signals/clear</code> - 清空所有信号
            </div>

            <div class="api-item">
                <span class="method post">POST</span>
                <code>/api/learning</code> - 信号学习控制
            </div>

            <div class="api-item">
                <span class="method post">POST</span>
                <code>/api/emit/signal</code> - 发射红外信号
            </div>

            <div class="api-item">
                <span class="method post">POST</span>
                <code>/api/batch</code> - 批量操作
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>🔧 系统已就绪，等待前端连接或API调用</p>
        </div>
    </div>
</body>
</html>
            )";
            request->send(200, "text/html", html);
        });
        Serial.println("📄 API信息页面已启用");
    }
}

// ==================== API处理器实现 ====================

void HTTPAPIServer::handleGetStatus(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    SystemManager* systemManager = APIHandlers::getSystemManager();
    if (!systemManager) {
        APIHandlers::sendErrorResponse(request, "系统管理器未初始化", 500);
        return;
    }

    // 获取系统状态
    SystemStatus status = systemManager->getSystemStatus();
    JsonDocument statusDoc = status.toJson();

    APIHandlers::sendSuccessResponse(request, statusDoc, "系统状态获取成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleGetSystemStats(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    SystemManager* systemManager = APIHandlers::getSystemManager();
    if (!systemManager) {
        APIHandlers::sendErrorResponse(request, "系统管理器未初始化", 500);
        return;
    }

    JsonDocument statsDoc;

    // ==================== CPU使用率概览 ====================
    float cpuUsagePercent = 25.0; // TODO: 实现真实的CPU使用率计算
    statsDoc["cpu"]["usage_percent"] = cpuUsagePercent;
    statsDoc["cpu"]["frequency_mhz"] = ESP.getCpuFreqMHz();
    statsDoc["cpu"]["temperature_celsius"] = round(temperatureRead() * 100) / 100;
    statsDoc["cpu"]["cores"] = 2; // ESP32-S3双核

    // ==================== 内存使用率概览 ====================
    uint32_t totalHeap = ESP.getHeapSize();
    uint32_t freeHeap = ESP.getFreeHeap();
    uint32_t usedHeap = totalHeap - freeHeap;
    float memoryUsagePercent = ((float)usedHeap / totalHeap) * 100;

    statsDoc["memory"]["usage_percent"] = round(memoryUsagePercent * 100) / 100;
    statsDoc["memory"]["total_mb"] = round((totalHeap / 1024.0 / 1024.0) * 100) / 100;
    statsDoc["memory"]["used_mb"] = round((usedHeap / 1024.0 / 1024.0) * 100) / 100;
    statsDoc["memory"]["free_mb"] = round((freeHeap / 1024.0 / 1024.0) * 100) / 100;

    // ==================== 磁盘使用率概览 ====================
    uint32_t totalFlash = ESP.getFlashChipSize();
    uint32_t usedFlash = ESP.getSketchSize();
    float flashUsagePercent = ((float)usedFlash / totalFlash) * 100;

    uint32_t totalFS = LittleFS.totalBytes();
    uint32_t usedFS = LittleFS.usedBytes();
    float fsUsagePercent = totalFS > 0 ? ((float)usedFS / totalFS) * 100 : 0;

    statsDoc["disk"]["flash_usage_percent"] = round(flashUsagePercent * 100) / 100;
    statsDoc["disk"]["filesystem_usage_percent"] = round(fsUsagePercent * 100) / 100;
    statsDoc["disk"]["total_flash_mb"] = round((totalFlash / 1024.0 / 1024.0) * 100) / 100;
    statsDoc["disk"]["total_filesystem_mb"] = round((totalFS / 1024.0 / 1024.0) * 100) / 100;

    // ==================== 系统状态评估 ====================
    String systemStatus = "正常";
    if (cpuUsagePercent > 90 || memoryUsagePercent > 90 || flashUsagePercent > 90 || fsUsagePercent > 90) {
        systemStatus = "严重负载";
    } else if (cpuUsagePercent > 80 || memoryUsagePercent > 80 || flashUsagePercent > 80 || fsUsagePercent > 80) {
        systemStatus = "高负载";
    } else if (cpuUsagePercent > 60 || memoryUsagePercent > 60 || flashUsagePercent > 60 || fsUsagePercent > 60) {
        systemStatus = "中等负载";
    } else {
        systemStatus = "低负载";
    }

    statsDoc["system"]["status"] = systemStatus;
    statsDoc["system"]["uptime_seconds"] = millis() / 1000;
    statsDoc["system"]["uptime_formatted"] = formatUptime(millis() / 1000);

    // ==================== 组件状态 ====================
    statsDoc["components"]["signals"] = systemManager->getSignalStorage() ?
                                       systemManager->getSignalStorage()->getSignalCount() : 0;
    statsDoc["components"]["timer_tasks"] = systemManager->getTimerManager() ?
                                           systemManager->getTimerManager()->getTaskCount() : 0;
    statsDoc["components"]["websocket_clients"] = systemManager->getWebSocketManager() ?
                                                 systemManager->getWebSocketManager()->getConnectedClientCount() : 0;

    // ==================== 网络状态 ====================
    if (WiFi.isConnected()) {
        statsDoc["network"]["wifi_connected"] = true;
        statsDoc["network"]["wifi_rssi"] = WiFi.RSSI();
        statsDoc["network"]["wifi_ssid"] = WiFi.SSID();
        statsDoc["network"]["ip_address"] = WiFi.localIP().toString();
    } else {
        statsDoc["network"]["wifi_connected"] = false;
        statsDoc["network"]["ap_mode"] = true;
        statsDoc["network"]["ap_ip"] = WiFi.softAPIP().toString();
    }

    statsDoc["timestamp"] = getCurrentUnixMs();

    // 使用HTTPAPIServer的sendSuccessResponse方法，确保包含responseTime字段
    sendSuccessResponse(request, statsDoc, "系统统计信息获取成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleGetSignals(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    SignalStorage* storage = APIHandlers::getSignalStorage();
    if (!storage) {
        APIHandlers::sendErrorResponse(request, "信号存储未初始化", 500);
        return;
    }

    // 获取所有信号
    std::vector<SignalData> signals = storage->getAllSignals();

    JsonDocument responseData;
    JsonArray signalsArray = responseData["signals"].to<JsonArray>();

    for (const auto& signal : signals) {
        JsonDocument signalDoc = signal.toJson();
        signalsArray.add(signalDoc);
    }

    responseData["total"] = signals.size();

    APIHandlers::sendSuccessResponse(request, responseData, "信号列表获取成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleCreateSignal(AsyncWebServerRequest *request, uint8_t *data, 
                                      size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();
    
    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    // 解析JSON请求体
    JsonDocument doc;
    if (!APIHandlers::parseJsonBody(data, len, doc)) {
        APIHandlers::sendErrorResponse(request, "JSON解析失败", 400);
        return;
    }

    SignalStorage* storage = APIHandlers::getSignalStorage();
    if (!storage) {
        APIHandlers::sendErrorResponse(request, "信号存储未初始化", 500);
        return;
    }
    
    // 创建信号数据
    SignalData signal = SignalData::fromJson(doc);
    
    // 验证信号数据
    if (!signal.isValid()) {
        sendErrorResponse(request, "信号数据无效", 400);
        return;
    }
    
    // 保存信号
    if (storage->saveSignal(signal)) {
        JsonDocument responseData = signal.toJson();
        sendSuccessResponse(request, responseData, "信号创建成功", startTime);
        
        // 发送WebSocket事件
        SystemManager* systemManager = getSystemManager();
        if (systemManager) {
            JsonDocument eventData = signal.toJson();
            systemManager->sendSystemEvent("signal_created", eventData);
        }
    } else {
        sendErrorResponse(request, "信号保存失败", 500);
    }
    
    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleUpdateSignal(AsyncWebServerRequest *request, uint8_t *data,
                                      size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (!requestMiddleware(request)) {
        return;
    }

    // 获取信号ID
    String signalId = request->pathArg(0);
    if (signalId.isEmpty()) {
        sendErrorResponse(request, "信号ID缺失", 400);
        return;
    }

    // 解析JSON请求体
    JsonDocument doc;
    if (!parseJsonBody(data, len, doc)) {
        sendErrorResponse(request, "JSON解析失败", 400);
        return;
    }

    SignalStorage* storage = getSignalStorage();
    if (!storage) {
        sendErrorResponse(request, "信号存储未初始化", 500);
        return;
    }

    // 检查信号是否存在
    SignalData existingSignal = storage->getSignal(signalId);
    if (existingSignal.id.isEmpty()) {
        sendErrorResponse(request, "信号不存在", 404);
        return;
    }

    // 更新信号数据
    SignalData updatedSignal = SignalData::fromJson(doc);
    updatedSignal.id = signalId;  // 确保ID不变

    // 验证信号数据
    if (!updatedSignal.isValid()) {
        sendErrorResponse(request, "信号数据无效", 400);
        return;
    }

    // 保存更新的信号
    if (storage->updateSignal(updatedSignal)) {
        JsonDocument responseData = updatedSignal.toJson();
        sendSuccessResponse(request, responseData, "信号更新成功", startTime);

        // 发送WebSocket事件
        SystemManager* systemManager = getSystemManager();
        if (systemManager) {
            JsonDocument eventData = updatedSignal.toJson();
            systemManager->sendSystemEvent("signal_updated", eventData);
        }
    } else {
        sendErrorResponse(request, "信号更新失败", 500);
    }

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleDeleteSignal(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!requestMiddleware(request)) {
        return;
    }

    // 获取信号ID
    String signalId = request->pathArg(0);
    if (signalId.isEmpty()) {
        sendErrorResponse(request, "信号ID缺失", 400);
        return;
    }

    SignalStorage* storage = getSignalStorage();
    if (!storage) {
        sendErrorResponse(request, "信号存储未初始化", 500);
        return;
    }

    // 检查信号是否存在
    SignalData existingSignal = storage->getSignal(signalId);
    if (existingSignal.id.isEmpty()) {
        sendErrorResponse(request, "信号不存在", 404);
        return;
    }

    // 删除信号
    if (storage->deleteSignal(signalId)) {
        JsonDocument responseData;
        responseData["deleted_id"] = signalId;
        sendSuccessResponse(request, responseData, "信号删除成功", startTime);

        // 发送WebSocket事件
        SystemManager* systemManager = getSystemManager();
        if (systemManager) {
            JsonDocument eventData;
            eventData["signal_id"] = signalId;
            systemManager->sendSystemEvent("signal_deleted", eventData);
        }
    } else {
        sendErrorResponse(request, "信号删除失败", 500);
    }

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleClearSignals(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!requestMiddleware(request)) {
        return;
    }

    SignalStorage* storage = getSignalStorage();
    if (!storage) {
        sendErrorResponse(request, "信号存储未初始化", 500);
        return;
    }

    // 获取清空前的信号数量
    uint32_t signalCount = storage->getSignalCount();

    // 清空所有信号
    if (storage->clearAllSignals()) {
        JsonDocument responseData;
        responseData["cleared_count"] = signalCount;
        sendSuccessResponse(request, responseData, "所有信号已清空", startTime);

        // 发送WebSocket事件
        SystemManager* systemManager = getSystemManager();
        if (systemManager) {
            JsonDocument eventData;
            eventData["cleared_count"] = signalCount;
            systemManager->sendSystemEvent("signals_cleared", eventData);
        }
    } else {
        sendErrorResponse(request, "信号清空失败", 500);
    }

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleLearningControl(AsyncWebServerRequest *request, uint8_t *data,
                                         size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (!requestMiddleware(request)) {
        return;
    }

    // 解析JSON请求体
    JsonDocument doc;
    if (!parseJsonBody(data, len, doc)) {
        sendErrorResponse(request, "JSON解析失败", 400);
        return;
    }

    IRSignalManager* irManager = getIRManager();
    if (!irManager) {
        sendErrorResponse(request, "红外管理器未初始化", 500);
        return;
    }

    String command = doc["command"].as<String>();

    if (command == "start") {
        // 开始学习
        uint32_t timeout = doc["timeout"].as<uint32_t>();
        if (timeout == 0) timeout = IR_LEARNING_TIMEOUT;

        if (irManager->startLearning(timeout)) {
            JsonDocument responseData;
            responseData["learning_started"] = true;
            responseData["timeout"] = timeout;
            sendSuccessResponse(request, responseData, "信号学习已开始", startTime);
        } else {
            sendErrorResponse(request, "信号学习启动失败", 500);
        }

    } else if (command == "stop") {
        // 停止学习
        if (irManager->stopLearning()) {
            JsonDocument responseData;
            responseData["learning_stopped"] = true;
            sendSuccessResponse(request, responseData, "信号学习已停止", startTime);
        } else {
            sendErrorResponse(request, "信号学习停止失败", 500);
        }

    } else if (command == "status") {
        // 获取学习状态
        JsonDocument responseData = irManager->getLearningStatus();
        sendSuccessResponse(request, responseData, "学习状态获取成功", startTime);

    } else {
        sendErrorResponse(request, "未知的学习命令", 400);
    }

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleLearningDetect(AsyncWebServerRequest *request, uint8_t *data,
                                        size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (!requestMiddleware(request)) {
        return;
    }

    // 解析JSON请求体
    JsonDocument doc;
    if (!parseJsonBody(data, len, doc)) {
        sendErrorResponse(request, "JSON解析失败", 400);
        return;
    }

    IRSignalManager* irManager = getIRManager();
    if (!irManager) {
        sendErrorResponse(request, "红外管理器未初始化", 500);
        return;
    }

    // 检查是否正在学习
    if (!irManager->isLearning()) {
        sendErrorResponse(request, "当前未处于学习状态", 400);
        return;
    }

    uint32_t timeout = doc["timeout"].as<uint32_t>();
    if (timeout == 0) timeout = 30000; // 默认30秒

    // 检查是否有信号被检测到
    // 注意：这里应该是非阻塞检查，实际的信号检测在IRSignalManager的loop中进行
    JsonDocument responseData;
    responseData["detection_active"] = true;
    responseData["timeout"] = timeout;
    responseData["learning_state"] = irManager->isLearning();

    sendSuccessResponse(request, responseData, "信号检测请求已处理", startTime);

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);
}

// ==================== 辅助方法实现 ====================

void HTTPAPIServer::sendSuccessResponse(AsyncWebServerRequest *request,
                                       const JsonDocument& data,
                                       const String& message,
                                       uint32_t requestStartTime) {

    JsonDocument responseDoc;
    responseDoc["success"] = true;
    responseDoc["message"] = message.isEmpty() ? "操作成功" : message;
    responseDoc["data"] = data;
    responseDoc["timestamp"] = getCurrentUnixMs();

    // 添加响应时间字段 - 符合前端架构期望
    uint32_t responseTime = millis() - requestStartTime;
    responseDoc["responseTime"] = responseTime;

    String response;
    serializeJson(responseDoc, response);
    request->send(200, "application/json", response);
}

void HTTPAPIServer::sendErrorResponse(AsyncWebServerRequest *request,
                                     const String& error,
                                     int code,
                                     const String& message) {
    JsonDocument responseDoc;
    responseDoc["success"] = false;
    responseDoc["error"] = error;
    responseDoc["message"] = message.isEmpty() ? "操作失败" : message;
    responseDoc["timestamp"] = getCurrentUnixMs();

    String response;
    serializeJson(responseDoc, response);
    request->send(code, "application/json", response);
}

bool HTTPAPIServer::parseJsonBody(uint8_t *data, size_t len, JsonDocument& doc) {
    if (!data || len == 0) {
        return false;
    }

    DeserializationError error = deserializeJson(doc, (char*)data, len);
    return error == DeserializationError::Ok;
}

bool HTTPAPIServer::requestMiddleware(AsyncWebServerRequest *request) {
    // 基础的请求验证
    if (!request) {
        return false;
    }

    // 检查Content-Type (对于POST/PUT请求)
    if (request->method() == HTTP_POST || request->method() == HTTP_PUT) {
        if (!request->hasHeader("Content-Type")) {
            return false;
        }

        String contentType = request->header("Content-Type");
        if (!contentType.startsWith("application/json")) {
            return false;
        }
    }

    return true;
}

void HTTPAPIServer::logRequest(AsyncWebServerRequest *request, int responseCode, uint32_t processingTime) {
    if (!request) return;

    String method;
    switch (request->method()) {
        case HTTP_GET: method = "GET"; break;
        case HTTP_POST: method = "POST"; break;
        case HTTP_PUT: method = "PUT"; break;
        case HTTP_DELETE: method = "DELETE"; break;
        default: method = "UNKNOWN"; break;
    }

    Serial.printf("[HTTP] %s %s - %d (%ums)\n",
                  method.c_str(),
                  request->url().c_str(),
                  responseCode,
                  processingTime);
}

SystemManager* HTTPAPIServer::getSystemManager() {
    return &SystemManager::getInstance();
}

IRSignalManager* HTTPAPIServer::getIRManager() {
    return SystemManager::getInstance().getIRManager();
}

SignalStorage* HTTPAPIServer::getSignalStorage() {
    return SystemManager::getInstance().getSignalStorage();
}

TimerManagerBackend* HTTPAPIServer::getTimerManagerBackend() {
    return TimerManagerBackend::getInstance();
}

// ==================== 定时器API处理器实现 ====================

void HTTPAPIServer::handleGetTimerTasks(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!requestMiddleware(request)) {
        return;
    }

    TimerManagerBackend* timerManager = getTimerManagerBackend();
    if (!timerManager) {
        sendErrorResponse(request, "定时器管理器未初始化", 500);
        return;
    }

    // 获取所有任务
    std::vector<TimerTask> tasks = timerManager->getAllTasks();

    JsonDocument responseData;
    JsonArray tasksArray = responseData["tasks"].to<JsonArray>();

    for (const auto& task : tasks) {
        JsonDocument taskDoc = task.toJson();
        tasksArray.add(taskDoc);
    }

    responseData["total"] = tasks.size();
    responseData["active"] = timerManager->getActiveTaskCount();

    sendSuccessResponse(request, responseData, "定时任务列表获取成功", startTime);

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleCreateTimerTask(AsyncWebServerRequest *request, uint8_t *data,
                                         size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (!requestMiddleware(request)) {
        return;
    }

    // 解析JSON请求体
    JsonDocument doc;
    if (!parseJsonBody(data, len, doc)) {
        sendErrorResponse(request, "JSON解析失败", 400);
        return;
    }

    TimerManagerBackend* timerManager = getTimerManagerBackend();
    if (!timerManager) {
        sendErrorResponse(request, "定时器管理器未初始化", 500);
        return;
    }

    // 创建任务数据
    TimerTask task = TimerTask::fromJson(doc);

    // 创建任务
    String taskId = timerManager->createTask(task);

    if (taskId.isEmpty()) {
        sendErrorResponse(request, "创建定时任务失败", 400);
        return;
    }

    JsonDocument responseData;
    responseData["taskId"] = taskId;
    responseData["task"] = task.toJson();

    sendSuccessResponse(request, responseData, "定时任务创建成功", startTime);

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleUpdateTimerTask(AsyncWebServerRequest *request, uint8_t *data,
                                         size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (!requestMiddleware(request)) {
        return;
    }

    // 获取任务ID
    String taskId = request->pathArg(0);
    if (taskId.isEmpty()) {
        sendErrorResponse(request, "任务ID不能为空", 400);
        return;
    }

    // 解析JSON请求体
    JsonDocument doc;
    if (!parseJsonBody(data, len, doc)) {
        sendErrorResponse(request, "JSON解析失败", 400);
        return;
    }

    TimerManagerBackend* timerManager = getTimerManagerBackend();
    if (!timerManager) {
        sendErrorResponse(request, "定时器管理器未初始化", 500);
        return;
    }

    // 更新任务数据
    TimerTask task = TimerTask::fromJson(doc);

    if (!timerManager->updateTask(taskId, task)) {
        sendErrorResponse(request, "更新定时任务失败", 400);
        return;
    }

    // 获取更新后的任务
    TimerTask* updatedTask = timerManager->getTask(taskId);
    JsonDocument responseData;
    if (updatedTask) {
        responseData["task"] = updatedTask->toJson();
    }

    sendSuccessResponse(request, responseData, "定时任务更新成功", startTime);

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleDeleteTimerTask(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!requestMiddleware(request)) {
        return;
    }

    // 获取任务ID
    String taskId = request->pathArg(0);
    if (taskId.isEmpty()) {
        sendErrorResponse(request, "任务ID不能为空", 400);
        return;
    }

    TimerManagerBackend* timerManager = getTimerManagerBackend();
    if (!timerManager) {
        sendErrorResponse(request, "定时器管理器未初始化", 500);
        return;
    }

    if (!timerManager->deleteTask(taskId)) {
        sendErrorResponse(request, "删除定时任务失败", 404);
        return;
    }

    JsonDocument responseData;
    responseData["deletedTaskId"] = taskId;

    sendSuccessResponse(request, responseData, "定时任务删除成功", startTime);

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleTimerEnable(AsyncWebServerRequest *request, uint8_t *data,
                                     size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (!requestMiddleware(request)) {
        return;
    }

    // 解析JSON请求体
    JsonDocument doc;
    if (!parseJsonBody(data, len, doc)) {
        sendErrorResponse(request, "JSON解析失败", 400);
        return;
    }

    TimerManagerBackend* timerManager = getTimerManagerBackend();
    if (!timerManager) {
        sendErrorResponse(request, "定时器管理器未初始化", 500);
        return;
    }

    bool enabled = doc["enabled"].as<bool>();

    if (!timerManager->setMasterEnabled(enabled)) {
        sendErrorResponse(request, "设置定时器状态失败", 500);
        return;
    }

    JsonDocument responseData;
    responseData["enabled"] = enabled;
    responseData["status"] = timerManager->getSystemStatus().toJson();

    sendSuccessResponse(request, responseData, enabled ? "定时器已启用" : "定时器已禁用", startTime);

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleGetTimerStatus(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!requestMiddleware(request)) {
        return;
    }

    TimerManagerBackend* timerManager = getTimerManagerBackend();
    if (!timerManager) {
        sendErrorResponse(request, "定时器管理器未初始化", 500);
        return;
    }

    TimerSystemStatus status = timerManager->getSystemStatus();
    TimerConfig config = timerManager->getConfig();

    JsonDocument responseData;
    responseData["status"] = status.toJson();
    responseData["config"] = config.toJson();

    sendSuccessResponse(request, responseData, "定时器状态获取成功", startTime);

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleExecuteTimerTask(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!requestMiddleware(request)) {
        return;
    }

    // 获取任务ID
    String taskId = request->pathArg(0);
    if (taskId.isEmpty()) {
        sendErrorResponse(request, "任务ID不能为空", 400);
        return;
    }

    TimerManagerBackend* timerManager = getTimerManagerBackend();
    if (!timerManager) {
        sendErrorResponse(request, "定时器管理器未初始化", 500);
        return;
    }

    if (!timerManager->executeTask(taskId)) {
        sendErrorResponse(request, "执行定时任务失败", 404);
        return;
    }

    JsonDocument responseData;
    responseData["executedTaskId"] = taskId;
    responseData["executionTime"] = getCurrentUnixMs();

    sendSuccessResponse(request, responseData, "定时任务执行成功", startTime);

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleTimerTaskEnable(AsyncWebServerRequest *request, uint8_t *data,
                                         size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (!requestMiddleware(request)) {
        return;
    }

    // 获取任务ID
    String taskId = request->pathArg(0);
    if (taskId.isEmpty()) {
        sendErrorResponse(request, "任务ID不能为空", 400);
        return;
    }

    // 解析JSON请求体
    JsonDocument doc;
    if (!parseJsonBody(data, len, doc)) {
        sendErrorResponse(request, "JSON解析失败", 400);
        return;
    }

    TimerManagerBackend* timerManager = getTimerManagerBackend();
    if (!timerManager) {
        sendErrorResponse(request, "定时器管理器未初始化", 500);
        return;
    }

    bool enabled = doc["enabled"].as<bool>();

    if (!timerManager->enableTask(taskId, enabled)) {
        sendErrorResponse(request, "设置任务状态失败", 404);
        return;
    }

    // 获取更新后的任务
    TimerTask* task = timerManager->getTask(taskId);
    JsonDocument responseData;
    responseData["taskId"] = taskId;
    responseData["enabled"] = enabled;
    if (task) {
        responseData["task"] = task->toJson();
    }

    sendSuccessResponse(request, responseData, enabled ? "任务已启用" : "任务已禁用", startTime);

    uint32_t processingTime = millis() - startTime;
    logRequest(request, 200, processingTime);
}

// ==================== 新增系统监控API ====================

void HTTPAPIServer::handleGetMemoryStats(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    JsonDocument memoryDoc;

    // 内存使用率统计
    uint32_t totalHeap = ESP.getHeapSize();
    uint32_t freeHeap = ESP.getFreeHeap();
    uint32_t usedHeap = totalHeap - freeHeap;
    float heapUsagePercent = ((float)usedHeap / totalHeap) * 100;

    memoryDoc["memory"]["total_bytes"] = totalHeap;
    memoryDoc["memory"]["used_bytes"] = usedHeap;
    memoryDoc["memory"]["free_bytes"] = freeHeap;
    memoryDoc["memory"]["usage_percent"] = round(heapUsagePercent * 100) / 100; // 保留2位小数
    memoryDoc["memory"]["min_free_bytes"] = ESP.getMinFreeHeap();
    memoryDoc["memory"]["max_alloc_bytes"] = ESP.getMaxAllocHeap();

    // PSRAM使用率（如果可用）
    uint32_t totalPsram = ESP.getPsramSize();
    if (totalPsram > 0) {
        uint32_t freePsram = ESP.getFreePsram();
        uint32_t usedPsram = totalPsram - freePsram;
        float psramUsagePercent = ((float)usedPsram / totalPsram) * 100;

        memoryDoc["psram"]["total_bytes"] = totalPsram;
        memoryDoc["psram"]["used_bytes"] = usedPsram;
        memoryDoc["psram"]["free_bytes"] = freePsram;
        memoryDoc["psram"]["usage_percent"] = round(psramUsagePercent * 100) / 100;
    } else {
        memoryDoc["psram"]["available"] = false;
        memoryDoc["psram"]["message"] = "PSRAM未启用或不可用";
    }

    // 内存状态评估
    String memoryStatus = "正常";
    if (heapUsagePercent > 90) {
        memoryStatus = "严重不足";
    } else if (heapUsagePercent > 80) {
        memoryStatus = "不足";
    } else if (heapUsagePercent > 70) {
        memoryStatus = "偏高";
    }

    memoryDoc["memory"]["status"] = memoryStatus;
    memoryDoc["timestamp"] = getCurrentUnixMs();

    sendSuccessResponse(request, memoryDoc, "内存使用率获取成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleGetPerformanceStats(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    SystemManager* systemManager = APIHandlers::getSystemManager();
    if (!systemManager) {
        APIHandlers::sendErrorResponse(request, "系统管理器未初始化", 500);
        return;
    }

    JsonDocument perfDoc;

    // CPU使用率统计
    uint32_t currentTime = millis();
    static uint32_t lastCpuCheck = 0;
    static uint32_t lastIdleTime = 0;
    static float cpuUsagePercent = 0.0;

    // 简单的CPU使用率估算（基于系统负载）
    if (currentTime - lastCpuCheck > 1000) { // 每秒更新一次
        uint32_t currentIdleTime = currentTime - startTime; // 处理请求的时间作为非空闲时间
        uint32_t totalTime = currentTime - lastCpuCheck;
        uint32_t busyTime = totalTime - currentIdleTime;
        cpuUsagePercent = ((float)busyTime / totalTime) * 100;

        lastCpuCheck = currentTime;
        lastIdleTime = currentIdleTime;
    }

    perfDoc["cpu"]["frequency_mhz"] = ESP.getCpuFreqMHz();
    perfDoc["cpu"]["usage_percent"] = round(cpuUsagePercent * 100) / 100;
    perfDoc["cpu"]["temperature_celsius"] = round(temperatureRead() * 100) / 100;
    perfDoc["cpu"]["uptime_seconds"] = millis() / 1000;

    // CPU状态评估
    String cpuStatus = "正常";
    if (cpuUsagePercent > 90) {
        cpuStatus = "过载";
    } else if (cpuUsagePercent > 80) {
        cpuStatus = "繁忙";
    } else if (cpuUsagePercent > 60) {
        cpuStatus = "活跃";
    } else {
        cpuStatus = "空闲";
    }
    perfDoc["cpu"]["status"] = cpuStatus;

    // 温度状态评估
    float temperature = temperatureRead();
    String tempStatus = "正常";
    if (temperature > 80) {
        tempStatus = "过热";
    } else if (temperature > 70) {
        tempStatus = "偏高";
    } else if (temperature > 60) {
        tempStatus = "温暖";
    }
    perfDoc["cpu"]["temperature_status"] = tempStatus;

    // 系统负载指标
    perfDoc["system"]["active_tasks"] = 0; // TODO: 获取活跃任务数
    perfDoc["system"]["free_stack"] = uxTaskGetStackHighWaterMark(NULL);
    perfDoc["system"]["heap_fragmentation"] = 100 - ((float)ESP.getMaxAllocHeap() / ESP.getFreeHeap() * 100);

    perfDoc["timestamp"] = getCurrentUnixMs();

    sendSuccessResponse(request, perfDoc, "CPU性能统计获取成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleGetDiskStats(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    JsonDocument diskDoc;

    // Flash存储统计
    uint32_t totalFlash = ESP.getFlashChipSize();
    uint32_t usedFlash = ESP.getSketchSize(); // 固件占用的Flash空间
    uint32_t freeFlash = totalFlash - usedFlash;
    float flashUsagePercent = ((float)usedFlash / totalFlash) * 100;

    diskDoc["flash"]["total_bytes"] = totalFlash;
    diskDoc["flash"]["used_bytes"] = usedFlash;
    diskDoc["flash"]["free_bytes"] = freeFlash;
    diskDoc["flash"]["usage_percent"] = round(flashUsagePercent * 100) / 100;
    diskDoc["flash"]["speed_mhz"] = ESP.getFlashChipSpeed() / 1000000;

    // Flash状态评估
    String flashStatus = "正常";
    if (flashUsagePercent > 90) {
        flashStatus = "严重不足";
    } else if (flashUsagePercent > 80) {
        flashStatus = "不足";
    } else if (flashUsagePercent > 70) {
        flashStatus = "偏高";
    }
    diskDoc["flash"]["status"] = flashStatus;

    // 文件系统统计（LittleFS）
    if (LittleFS.totalBytes() > 0) {
        uint32_t totalFS = LittleFS.totalBytes();
        uint32_t usedFS = LittleFS.usedBytes();
        uint32_t freeFS = totalFS - usedFS;
        float fsUsagePercent = ((float)usedFS / totalFS) * 100;

        diskDoc["filesystem"]["total_bytes"] = totalFS;
        diskDoc["filesystem"]["used_bytes"] = usedFS;
        diskDoc["filesystem"]["free_bytes"] = freeFS;
        diskDoc["filesystem"]["usage_percent"] = round(fsUsagePercent * 100) / 100;
        diskDoc["filesystem"]["type"] = "LittleFS";

        // 文件系统状态评估
        String fsStatus = "正常";
        if (fsUsagePercent > 90) {
            fsStatus = "严重不足";
        } else if (fsUsagePercent > 80) {
            fsStatus = "不足";
        } else if (fsUsagePercent > 70) {
            fsStatus = "偏高";
        }
        diskDoc["filesystem"]["status"] = fsStatus;

        // 文件统计
        diskDoc["filesystem"]["files_count"] = 0; // TODO: 实现文件计数
        diskDoc["filesystem"]["directories_count"] = 0; // TODO: 实现目录计数
    } else {
        diskDoc["filesystem"]["available"] = false;
        diskDoc["filesystem"]["message"] = "文件系统未挂载";
    }

    // 存储健康度评估
    String overallStatus = "正常";
    float maxUsage = max(flashUsagePercent,
                        LittleFS.totalBytes() > 0 ?
                        ((float)LittleFS.usedBytes() / LittleFS.totalBytes()) * 100 : 0);

    if (maxUsage > 90) {
        overallStatus = "存储空间严重不足";
    } else if (maxUsage > 80) {
        overallStatus = "存储空间不足";
    } else if (maxUsage > 70) {
        overallStatus = "存储空间偏高";
    } else {
        overallStatus = "存储空间充足";
    }

    diskDoc["overall"]["status"] = overallStatus;
    diskDoc["overall"]["max_usage_percent"] = round(maxUsage * 100) / 100;
    diskDoc["timestamp"] = getCurrentUnixMs();

    sendSuccessResponse(request, diskDoc, "磁盘使用率获取成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleGetTaskStatus(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    SystemManager* systemManager = APIHandlers::getSystemManager();
    if (!systemManager) {
        APIHandlers::sendErrorResponse(request, "系统管理器未初始化", 500);
        return;
    }

    // 获取任务状态
    JsonDocument taskDoc = systemManager->getTaskStatus();
    taskDoc["timestamp"] = getCurrentUnixMs();

    sendSuccessResponse(request, taskDoc, "任务状态获取成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

// ==================== 信号分组管理API实现 ====================

void HTTPAPIServer::handleGetSignalGroups(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    // TODO: 实现信号分组存储和管理
    JsonDocument groupsDoc;
    JsonArray groups = groupsDoc["groups"].to<JsonArray>();

    // 示例分组数据
    JsonObject defaultGroup = groups.add<JsonObject>();
    defaultGroup["id"] = "default";
    defaultGroup["name"] = "默认分组";
    defaultGroup["description"] = "系统默认信号分组";
    defaultGroup["color"] = "#007bff";
    defaultGroup["icon"] = "folder";
    defaultGroup["created_time"] = millis();
    defaultGroup["modified_time"] = millis();
    defaultGroup["is_active"] = true;
    defaultGroup["signal_ids"] = JsonArray();

    sendSuccessResponse(request, groupsDoc, "信号分组列表获取成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleCreateSignalGroup(AsyncWebServerRequest *request, uint8_t *data,
                                           size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    JsonDocument doc;
    if (!APIHandlers::parseJsonBody(data, len, doc)) {
        APIHandlers::sendErrorResponse(request, "JSON解析失败", 400);
        return;
    }

    // 验证必需字段
    if (!doc["name"].is<String>() || doc["name"].as<String>().isEmpty()) {
        APIHandlers::sendErrorResponse(request, "分组名称不能为空", 400);
        return;
    }

    // 创建新分组
    SignalGroup group;
    group.id = "group_" + String(millis());
    group.name = doc["name"].as<String>();
    group.description = doc["description"].as<String>();
    group.color = doc["color"].as<String>();
    group.icon = doc["icon"].as<String>();
    group.createdTime = millis();
    group.modifiedTime = millis();
    group.isActive = true;

    if (doc["signal_ids"].is<JsonArray>()) {
        JsonArray signals = doc["signal_ids"].as<JsonArray>();
        for (JsonVariant signal : signals) {
            group.signalIds.push_back(signal.as<String>());
        }
    }

    // TODO: 保存到存储系统
    JsonDocument responseData = group.toJson();
    sendSuccessResponse(request, responseData, "信号分组创建成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 201, processingTime);
}

void HTTPAPIServer::handleUpdateSignalGroup(AsyncWebServerRequest *request, uint8_t *data,
                                           size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    // 从URL中提取分组ID
    String url = request->url();
    int lastSlash = url.lastIndexOf('/');
    String groupId = (lastSlash >= 0) ? url.substring(lastSlash + 1) : "";

    if (groupId.isEmpty()) {
        APIHandlers::sendErrorResponse(request, "分组ID不能为空", 400);
        return;
    }

    JsonDocument doc;
    if (!APIHandlers::parseJsonBody(data, len, doc)) {
        APIHandlers::sendErrorResponse(request, "JSON解析失败", 400);
        return;
    }

    // TODO: 从存储系统加载现有分组
    // TODO: 更新分组信息
    // TODO: 保存到存储系统

    JsonDocument responseData;
    responseData["id"] = groupId;
    responseData["message"] = "分组更新成功";
    responseData["modified_time"] = millis();

    sendSuccessResponse(request, responseData, "信号分组更新成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleDeleteSignalGroup(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    // 从URL中提取分组ID
    String url = request->url();
    int lastSlash = url.lastIndexOf('/');
    String groupId = (lastSlash >= 0) ? url.substring(lastSlash + 1) : "";

    if (groupId.isEmpty()) {
        APIHandlers::sendErrorResponse(request, "分组ID不能为空", 400);
        return;
    }

    // TODO: 从存储系统删除分组
    // TODO: 检查分组是否存在
    // TODO: 检查分组是否可以删除（如是否为默认分组）

    JsonDocument responseData;
    responseData["id"] = groupId;
    responseData["message"] = "分组删除成功";

    sendSuccessResponse(request, responseData, "信号分组删除成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

// ==================== 用户配置管理API实现 ====================

void HTTPAPIServer::handleGetUserConfig(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    SystemManager* systemManager = APIHandlers::getSystemManager();
    if (!systemManager) {
        APIHandlers::sendErrorResponse(request, "系统管理器未初始化", 500);
        return;
    }

    JsonDocument configDoc;

    // 系统配置
    configDoc["system"]["device_name"] = "ESP32-IR-Control";
    configDoc["system"]["language"] = "zh-CN";
    configDoc["system"]["timezone"] = "Asia/Shanghai";
    configDoc["system"]["auto_save"] = true;
    configDoc["system"]["debug_mode"] = false;

    // 界面配置
    configDoc["ui"]["theme"] = "light";
    configDoc["ui"]["show_advanced"] = false;
    configDoc["ui"]["auto_refresh"] = true;
    configDoc["ui"]["refresh_interval"] = 5000;

    // 红外配置
    configDoc["ir"]["default_frequency"] = 38000;
    configDoc["ir"]["default_duty_cycle"] = 33;
    configDoc["ir"]["learning_timeout"] = 30000;
    configDoc["ir"]["emit_repeat"] = 1;

    // 定时器配置
    configDoc["timer"]["enabled"] = true;
    configDoc["timer"]["check_interval"] = 60000;
    configDoc["timer"]["max_tasks"] = 50;

    // 网络配置
    configDoc["network"]["ap_enabled"] = true;
    configDoc["network"]["ap_ssid"] = "ESP32-IR-Control";
    configDoc["network"]["websocket_enabled"] = true;
    configDoc["network"]["websocket_port"] = 8001;

    sendSuccessResponse(request, configDoc, "用户配置获取成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleUpdateUserConfig(AsyncWebServerRequest *request, uint8_t *data,
                                          size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    JsonDocument doc;
    if (!APIHandlers::parseJsonBody(data, len, doc)) {
        APIHandlers::sendErrorResponse(request, "JSON解析失败", 400);
        return;
    }

    // TODO: 验证配置参数
    // TODO: 保存配置到文件系统
    // TODO: 应用新配置到系统组件

    JsonDocument responseData;
    responseData["message"] = "配置更新成功";
    responseData["updated_time"] = millis();
    responseData["restart_required"] = false;

    sendSuccessResponse(request, responseData, "用户配置更新成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleResetUserConfig(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    // TODO: 重置所有配置为默认值
    // TODO: 删除配置文件
    // TODO: 重新初始化系统组件

    JsonDocument responseData;
    responseData["message"] = "配置已重置为默认值";
    responseData["reset_time"] = millis();
    responseData["restart_required"] = true;

    sendSuccessResponse(request, responseData, "用户配置重置成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

// ==================== 系统备份恢复API实现 ====================

void HTTPAPIServer::handleExportBackup(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    SystemManager* systemManager = APIHandlers::getSystemManager();
    if (!systemManager) {
        APIHandlers::sendErrorResponse(request, "系统管理器未初始化", 500);
        return;
    }

    JsonDocument backupDoc;

    // 备份元信息
    backupDoc["backup_info"]["version"] = "1.0.0";
    backupDoc["backup_info"]["created_time"] = millis();
    backupDoc["backup_info"]["device_id"] = WiFi.macAddress();
    backupDoc["backup_info"]["firmware_version"] = FIRMWARE_VERSION;

    // 备份信号数据
    if (systemManager->getSignalStorage()) {
        // TODO: 导出所有信号数据
        backupDoc["signals"] = JsonArray();
    }

    // 备份定时任务
    if (systemManager->getTimerManager()) {
        // TODO: 导出所有定时任务
        backupDoc["timer_tasks"] = JsonArray();
    }

    // 备份用户配置
    // TODO: 导出用户配置
    backupDoc["user_config"] = JsonObject();

    // 备份信号分组
    // TODO: 导出信号分组
    backupDoc["signal_groups"] = JsonArray();

    // 设置下载响应头
    String filename = "esp32_backup_" + String(millis()) + ".json";
    request->send(200, "application/json", backupDoc.as<String>());

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleImportBackup(AsyncWebServerRequest *request, uint8_t *data,
                                      size_t len, size_t index, size_t total) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    JsonDocument doc;
    if (!APIHandlers::parseJsonBody(data, len, doc)) {
        APIHandlers::sendErrorResponse(request, "备份文件格式错误", 400);
        return;
    }

    // 验证备份文件格式
    if (!doc["backup_info"].is<JsonObject>()) {
        APIHandlers::sendErrorResponse(request, "无效的备份文件", 400);
        return;
    }

    SystemManager* systemManager = APIHandlers::getSystemManager();
    if (!systemManager) {
        APIHandlers::sendErrorResponse(request, "系统管理器未初始化", 500);
        return;
    }

    uint32_t importedSignals = 0;
    uint32_t importedTasks = 0;
    uint32_t importedGroups = 0;

    // 导入信号数据
    if (doc["signals"].is<JsonArray>()) {
        // TODO: 导入信号数据
        JsonArray signals = doc["signals"].as<JsonArray>();
        importedSignals = signals.size();
    }

    // 导入定时任务
    if (doc["timer_tasks"].is<JsonArray>()) {
        // TODO: 导入定时任务
        JsonArray tasks = doc["timer_tasks"].as<JsonArray>();
        importedTasks = tasks.size();
    }

    // 导入信号分组
    if (doc["signal_groups"].is<JsonArray>()) {
        // TODO: 导入信号分组
        JsonArray groups = doc["signal_groups"].as<JsonArray>();
        importedGroups = groups.size();
    }

    // 导入用户配置
    if (doc["user_config"].is<JsonObject>()) {
        // TODO: 导入用户配置
    }

    JsonDocument responseData;
    responseData["message"] = "备份导入成功";
    responseData["imported_signals"] = importedSignals;
    responseData["imported_tasks"] = importedTasks;
    responseData["imported_groups"] = importedGroups;
    responseData["import_time"] = millis();
    responseData["restart_required"] = true;

    sendSuccessResponse(request, responseData, "系统备份导入成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

// ==================== 固件更新API实现 ====================

void HTTPAPIServer::handleGetFirmwareInfo(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    JsonDocument firmwareDoc;

    // 当前固件信息
    firmwareDoc["current"]["version"] = FIRMWARE_VERSION;
    firmwareDoc["current"]["build_date"] = __DATE__ " " __TIME__;
    firmwareDoc["current"]["chip_model"] = ESP.getChipModel();
    firmwareDoc["current"]["chip_revision"] = ESP.getChipRevision();
    firmwareDoc["current"]["flash_size"] = ESP.getFlashChipSize();
    firmwareDoc["current"]["free_space"] = ESP.getFreeSketchSpace();
    firmwareDoc["current"]["sketch_size"] = ESP.getSketchSize();

    // 更新状态
    firmwareDoc["update"]["available"] = false;
    firmwareDoc["update"]["latest_version"] = FIRMWARE_VERSION;
    firmwareDoc["update"]["download_url"] = "";
    firmwareDoc["update"]["release_notes"] = "";
    firmwareDoc["update"]["last_check"] = 0;

    // 系统状态
    firmwareDoc["system"]["uptime"] = millis() / 1000;
    firmwareDoc["system"]["free_heap"] = ESP.getFreeHeap();
    firmwareDoc["system"]["cpu_freq"] = ESP.getCpuFreqMHz();

    sendSuccessResponse(request, firmwareDoc, "固件信息获取成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleFirmwareUpload(AsyncWebServerRequest *request, const String& filename,
                                        size_t index, uint8_t *data, size_t len, bool final) {
    static bool updateStarted = false;

    if (!index) {
        Serial.printf("🔄 开始固件更新: %s\n", filename.c_str());

        // 开始OTA更新
        if (!Update.begin(UPDATE_SIZE_UNKNOWN)) {
            Update.printError(Serial);
            request->send(500, "application/json", "{\"error\":\"OTA更新初始化失败\"}");
            return;
        }
        updateStarted = true;
    }

    if (updateStarted) {
        // 写入固件数据
        if (Update.write(data, len) != len) {
            Update.printError(Serial);
            request->send(500, "application/json", "{\"error\":\"固件写入失败\"}");
            return;
        }
    }

    if (final) {
        if (Update.end(true)) {
            Serial.println("✅ 固件更新成功，准备重启");
            request->send(200, "application/json",
                         "{\"success\":true,\"message\":\"固件更新成功，系统将在3秒后重启\"}");

            // 延迟重启，让响应发送完成
            delay(3000);
            ESP.restart();
        } else {
            Update.printError(Serial);
            request->send(500, "application/json", "{\"error\":\"固件更新失败\"}");
        }
        updateStarted = false;
    }
}

void HTTPAPIServer::handleCheckFirmwareUpdate(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    // TODO: 实现在线固件更新检查
    // 可以连接到GitHub Releases或自定义更新服务器

    JsonDocument updateDoc;
    updateDoc["available"] = false;
    updateDoc["current_version"] = FIRMWARE_VERSION;
    updateDoc["latest_version"] = FIRMWARE_VERSION;
    updateDoc["check_time"] = millis();
    updateDoc["message"] = "当前已是最新版本";

    sendSuccessResponse(request, updateDoc, "固件更新检查完成", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleSystemRestart(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    JsonDocument responseData;
    responseData["message"] = "系统将在3秒后重启";
    responseData["restart_time"] = millis() + 3000;

    sendSuccessResponse(request, responseData, "系统重启指令已接收", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);

    // 延迟重启，让响应发送完成
    delay(3000);
    ESP.restart();
}

// ==================== 错误日志管理API实现 ====================

void HTTPAPIServer::handleGetSystemLogs(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    // 获取查询参数
    String level = request->hasParam("level") ? request->getParam("level")->value() : "all";
    int limit = request->hasParam("limit") ? request->getParam("limit")->value().toInt() : 100;
    int offset = request->hasParam("offset") ? request->getParam("offset")->value().toInt() : 0;

    JsonDocument logsDoc;
    JsonArray logs = logsDoc["logs"].to<JsonArray>();

    // TODO: 从日志文件或内存缓冲区读取日志
    // 这里提供示例日志数据
    for (int i = 0; i < 5; i++) {
        JsonObject logEntry = logs.add<JsonObject>();
        logEntry["timestamp"] = millis() - (i * 60000);
        logEntry["level"] = (i % 3 == 0) ? "ERROR" : (i % 3 == 1) ? "WARN" : "INFO";
        logEntry["component"] = (i % 2 == 0) ? "WiFiManager" : "IRManager";
        logEntry["message"] = "示例日志消息 " + String(i);
        logEntry["details"] = "详细错误信息...";
    }

    logsDoc["total"] = 5;
    logsDoc["limit"] = limit;
    logsDoc["offset"] = offset;
    logsDoc["level_filter"] = level;

    sendSuccessResponse(request, logsDoc, "系统日志获取成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleClearSystemLogs(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    // TODO: 清空日志文件或内存缓冲区
    // 可以选择性清空不同级别的日志

    JsonDocument responseData;
    responseData["message"] = "系统日志已清空";
    responseData["cleared_time"] = millis();
    responseData["cleared_count"] = 0; // TODO: 返回实际清空的日志数量

    sendSuccessResponse(request, responseData, "系统日志清空成功", startTime);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}

void HTTPAPIServer::handleDownloadLogs(AsyncWebServerRequest *request) {
    uint32_t startTime = millis();

    if (!APIHandlers::requestMiddleware(request)) {
        return;
    }

    // TODO: 生成日志文件内容
    String logContent = "ESP32-IR-Control System Logs\n";
    logContent += "Generated: " + String(millis()) + "\n";
    logContent += "Device: " + WiFi.macAddress() + "\n";
    logContent += "Firmware: " + String(FIRMWARE_VERSION) + "\n";
    logContent += "========================================\n\n";

    // TODO: 添加实际日志内容
    logContent += "[INFO] System started successfully\n";
    logContent += "[WARN] WiFi connection timeout\n";
    logContent += "[ERROR] Signal learning failed\n";

    // 设置下载响应头
    String filename = "esp32_logs_" + String(millis()) + ".txt";
    AsyncWebServerResponse *response = request->beginResponse(200, "text/plain", logContent);
    response->addHeader("Content-Disposition", "attachment; filename=" + filename);
    request->send(response);

    uint32_t processingTime = millis() - startTime;
    APIHandlers::logRequest(request, 200, processingTime);
}
