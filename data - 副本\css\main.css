/* R1智能红外控制系统 - 主样式文件 */

/* CSS变量定义 */
:root {
  /* 主色调 */
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-light: #dbeafe;
  
  /* 辅助色 */
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #06b6d4;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-dark: #1e293b;
  
  /* 文字色 */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --text-white: #ffffff;
  
  /* 边框色 */
  --border-color: #e2e8f0;
  --border-hover: #cbd5e1;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  /* 圆角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* 间距 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  /* 动画 */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: var(--font-family);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  overflow-x: hidden;
}

/* 主容器 */
.r1-system {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 系统头部 */
.system-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.system-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo {
  font-size: 1.75rem;
}

.system-version {
  background: var(--primary-light);
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--success-color);
  /* 状态指示器动画 - 必要的视觉反馈 */
  animation: pulse 2s infinite;
}

.status-indicator.connecting {
  background: var(--warning-color);
}

.status-indicator.error {
  background: var(--error-color);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.system-time {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.settings-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.settings-btn:hover {
  background: var(--bg-secondary);
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* 左侧边栏 */
.sidebar {
  width: 380px; /* 全屏模式下增加宽度以确保标签栏文字+数字能够一字排开 */
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* 状态显示区域 - 替换原快速操作区域 */
.status-display-area {
  flex: 1;
  min-height: 0;
}

.status-display-area h3 {
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-color);
}

/* 系统监控区域 - 替换原系统概览区域 */
.system-monitor-area {
  flex: 1;
  min-height: 0;
}

.system-monitor-area h3 {
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-color);
}

.quick-actions {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.quick-actions h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.quick-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border: none;
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-bottom: var(--spacing-sm);
}

.quick-btn:last-child {
  margin-bottom: 0;
}

.quick-btn:hover {
  background: var(--bg-tertiary);
  transform: translateY(-1px);
}



.quick-btn.learn-btn {
  background: var(--primary-color);
  color: var(--text-white);
}

.quick-btn.learn-btn:hover {
  background: var(--primary-hover);
}

/* ==================== 统一按钮样式系统 ==================== */

/* 基础按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--button-bg);
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  white-space: nowrap;
}

.btn:hover:not(:disabled) {
  background: var(--button-hover-bg);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 按钮尺寸变体 */
.btn.small {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.75rem;
  gap: 4px;
}

.btn.large {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1rem;
  gap: var(--spacing-sm);
}

/* 按钮颜色变体 */
.btn.primary {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn.primary:hover:not(:disabled) {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.btn.secondary:hover:not(:disabled) {
  background: var(--bg-tertiary);
  border-color: var(--primary-color);
}

.btn.success {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.btn.success:hover:not(:disabled) {
  background: #16a085;
  border-color: #16a085;
}

.btn.warning {
  background: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
}

.btn.warning:hover:not(:disabled) {
  background: #d68910;
  border-color: #d68910;
}

.btn.danger {
  background: var(--error-color);
  color: white;
  border-color: var(--error-color);
}

.btn.danger:hover:not(:disabled) {
  background: #c0392b;
  border-color: #c0392b;
}

.btn.info {
  background: var(--info-color);
  color: white;
  border-color: var(--info-color);
}

.btn.info:hover:not(:disabled) {
  background: #2980b9;
  border-color: #2980b9;
}

/* 按钮状态变体 */
.btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn.outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn.outline:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
}

.btn.ghost {
  background: transparent;
  border-color: transparent;
  color: var(--text-secondary);
}

.btn.ghost:hover:not(:disabled) {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* 按钮图标和文本 */
.btn-icon {
  font-size: 1rem;
  line-height: 1;
}

.btn.small .btn-icon {
  font-size: 0.875rem;
}

.btn.large .btn-icon {
  font-size: 1.125rem;
}

.btn-text {
  flex: 1;
  text-align: center;
}

/* 按钮组 */
.btn-group {
  display: inline-flex;
  border-radius: var(--radius-md);
  overflow: hidden;
}

.btn-group .btn {
  border-radius: 0;
  border-right-width: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--radius-md);
  border-bottom-left-radius: var(--radius-md);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  border-right-width: 1px;
}

.btn-group .btn:only-child {
  border-radius: var(--radius-md);
  border-right-width: 1px;
}

/* ==================== 响应式按钮设计 ==================== */

/* 移动端按钮优化 */
@media (max-width: 768px) {
  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
  }

  .btn.small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
  }

  .btn.large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
  }

  /* 移动端按钮组 */
  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    border-radius: var(--radius-md);
    border-right-width: 1px;
    border-bottom-width: 0;
  }

  .btn-group .btn:first-child {
    border-radius: var(--radius-md) var(--radius-md) 0 0;
  }

  .btn-group .btn:last-child {
    border-radius: 0 0 var(--radius-md) var(--radius-md);
    border-bottom-width: 1px;
  }

  .btn-group .btn:only-child {
    border-radius: var(--radius-md);
    border-bottom-width: 1px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: 44px; /* iOS推荐的最小触摸目标 */
    padding: var(--spacing-sm) var(--spacing-lg);
  }

  .btn.small {
    min-height: 36px;
    padding: var(--spacing-xs) var(--spacing-md);
  }

  .btn:hover {
    transform: none; /* 移除hover动画，避免触摸设备的问题 */
  }
}

/* 状态概览 */
.status-overview {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.status-overview h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.status-cards {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.status-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.status-card:hover {
  background: var(--bg-tertiary);
  transform: translateY(-1px);
}

.card-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-light);
  border-radius: var(--radius-lg);
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.card-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

/* 模块容器 */
.modules-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

/* 模块导航标签 */
.module-tabs {
  display: flex;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  overflow-x: auto;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  background: none;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  border-bottom: 2px solid transparent;
}

.tab-btn:hover {
  color: var(--text-primary);
  background: var(--bg-tertiary);
}

.tab-btn.active {
  color: var(--primary-color);
  background: var(--bg-primary);
  border-bottom-color: var(--primary-color);
}

.tab-icon {
  font-size: 1rem;
}

.tab-text {
  font-weight: 500;
}

/* 模块内容 */
.module-content {
  flex: 1;
  position: relative;
}

.module-panel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: var(--spacing-xl);
  opacity: 0;
  visibility: hidden;
  /* 优化：使用更快的过渡，只对opacity和visibility进行动画 */
  transition: opacity var(--transition-fast), visibility var(--transition-fast);
  overflow-y: auto;
}

.module-panel.active {
  opacity: 1;
  visibility: visible;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.module-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.module-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.action-btn:hover {
  border-color: var(--border-hover);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.action-btn.primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-white);
}

.action-btn.primary:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .sidebar {
    width: 100%;
    flex-direction: row;
    gap: var(--spacing-md);
  }

  .quick-actions,
  .status-overview {
    flex: 1;
  }

  .status-cards {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .status-card {
    flex: 1;
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .system-header {
    padding: var(--spacing-md);
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .header-left,
  .header-right {
    width: 100%;
    justify-content: center;
  }

  .main-content {
    padding: var(--spacing-md);
  }

  .sidebar {
    flex-direction: column;
  }

  .module-tabs {
    flex-wrap: wrap;
  }

  .tab-btn {
    flex: 1;
    justify-content: center;
  }

  .module-panel {
    padding: var(--spacing-lg);
  }

  .module-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .module-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
}


