/**
 * 信号虚拟列表 - 专门为信号管理优化的虚拟滚动列表
 */
class SignalVirtualList extends VirtualScrollList {
  constructor(container, signalManager, options = {}) {
    super(container, {
      itemHeight: options.itemHeight || 80,
      bufferSize: options.bufferSize || 10,
      ...options
    });
    
    this.signalManager = signalManager;
    this.viewMode = 'grid'; // 'grid' | 'list'
    this.isMultiSelectMode = false;
    this.selectedSignals = new Set();
  }

  /**
   * 设置视图模式
   */
  setViewMode(mode) {
    this.viewMode = mode;
    this.itemHeight = mode === 'list' ? 60 : 120;
    this.render();
  }

  /**
   * 设置多选模式
   */
  setMultiSelectMode(enabled) {
    this.isMultiSelectMode = enabled;
    this.render();
  }

  /**
   * 获取项目HTML - 重写父类方法
   */
  getItemHTML(signal, index) {
    if (this.viewMode === 'list') {
      return this.getListItemHTML(signal, index);
    } else {
      return this.getGridItemHTML(signal, index);
    }
  }

  /**
   * 获取列表视图HTML
   */
  getListItemHTML(signal, index) {
    const isSelected = this.selectedSignals.has(signal.id);
    const selectionClass = isSelected ? 'selected' : '';
    const multiSelectClass = this.isMultiSelectMode ? 'multiselect-mode' : '';

    return `
      <div class="signal-list-item ${selectionClass} ${multiSelectClass}" data-signal-id="${signal.id}">
        ${this.isMultiSelectMode ? `
          <div class="signal-checkbox">
            <input type="checkbox" ${isSelected ? 'checked' : ''} 
                   data-action="toggle-selection-checkbox" data-signal-id="${signal.id}">
          </div>
        ` : ''}
        
        <div class="signal-info">
          <div class="signal-name">${this.escapeHtml(signal.name)}</div>
          <div class="signal-details">
            <span class="signal-type">${signal.type || '未知'}</span>
            <span class="signal-protocol">${signal.protocol || 'N/A'}</span>
            <span class="signal-code">${signal.signalCode || 'N/A'}</span>
          </div>
        </div>
        
        <div class="signal-actions">
          <button class="action-btn small" data-action="send-signal" data-signal-id="${signal.id}" title="发射信号">
            📡
          </button>
          <button class="action-btn small" data-action="edit-signal" data-signal-id="${signal.id}" title="编辑信号">
            ✏️
          </button>
          <button class="action-btn small danger" data-action="delete-signal" data-signal-id="${signal.id}" title="删除信号">
            🗑️
          </button>
        </div>
      </div>
    `;
  }

  /**
   * 获取网格视图HTML
   */
  getGridItemHTML(signal, index) {
    const isSelected = this.selectedSignals.has(signal.id);
    const selectionClass = isSelected ? 'selected' : '';
    const multiSelectClass = this.isMultiSelectMode ? 'multiselect-mode' : '';

    return `
      <div class="signal-card ${selectionClass} ${multiSelectClass}" data-signal-id="${signal.id}">
        ${this.isMultiSelectMode ? `
          <div class="signal-checkbox">
            <input type="checkbox" ${isSelected ? 'checked' : ''} 
                   data-action="toggle-selection-checkbox" data-signal-id="${signal.id}">
          </div>
        ` : ''}
        
        <div class="signal-header">
          <div class="signal-name" title="${this.escapeHtml(signal.name)}">
            ${this.truncateText(signal.name, 20)}
          </div>
          <div class="signal-type-badge ${signal.type || 'unknown'}">
            ${this.getTypeIcon(signal.type)}
          </div>
        </div>
        
        <div class="signal-body">
          <div class="signal-details">
            <div class="detail-item">
              <span class="label">协议:</span>
              <span class="value">${signal.protocol || 'N/A'}</span>
            </div>
            <div class="detail-item">
              <span class="label">代码:</span>
              <span class="value">${this.truncateText(signal.signalCode || 'N/A', 12)}</span>
            </div>
          </div>
        </div>
        
        <div class="signal-footer">
          <div class="signal-stats">
            <span class="sent-count" title="发射次数">${signal.sentCount || 0}次</span>
          </div>
          <div class="signal-actions">
            <button class="action-btn primary small" data-action="send-signal" data-signal-id="${signal.id}" title="发射信号">
              📡
            </button>
            <button class="action-btn secondary small" data-action="edit-signal" data-signal-id="${signal.id}" title="编辑信号">
              ✏️
            </button>
            <button class="action-btn danger small" data-action="delete-signal" data-signal-id="${signal.id}" title="删除信号">
              🗑️
            </button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 获取类型图标
   */
  getTypeIcon(type) {
    const icons = {
      'tv': '📺',
      'ac': '❄️',
      'fan': '🌀',
      'light': '💡',
      'audio': '🔊',
      'other': '📱'
    };
    return icons[type] || '📱';
  }

  /**
   * 截断文本
   */
  truncateText(text, maxLength) {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  /**
   * 转义HTML
   */
  escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 更新选中状态
   */
  updateSelection(signalId, selected) {
    if (selected) {
      this.selectedSignals.add(signalId);
    } else {
      this.selectedSignals.delete(signalId);
    }
    
    // 只重新渲染受影响的项目
    const index = this.data.findIndex(signal => signal.id === signalId);
    if (index !== -1 && index >= this.visibleStart && index < this.visibleEnd) {
      this.updateItem(index, this.data[index]);
    }
  }

  /**
   * 全选
   */
  selectAll() {
    this.data.forEach(signal => {
      this.selectedSignals.add(signal.id);
    });
    this.render();
  }

  /**
   * 全不选
   */
  selectNone() {
    this.selectedSignals.clear();
    this.render();
  }

  /**
   * 获取选中的信号
   */
  getSelectedSignals() {
    return this.data.filter(signal => this.selectedSignals.has(signal.id));
  }

  /**
   * 获取选中数量
   */
  getSelectedCount() {
    return this.selectedSignals.size;
  }

  /**
   * 过滤信号
   */
  filterSignals(keyword, type, sortBy) {
    let filteredData = [...this.data];

    // 关键词过滤
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase();
      filteredData = filteredData.filter(signal => 
        signal.name?.toLowerCase().includes(lowerKeyword) ||
        signal.type?.toLowerCase().includes(lowerKeyword) ||
        signal.signalCode?.toLowerCase().includes(lowerKeyword)
      );
    }

    // 类型过滤
    if (type) {
      filteredData = filteredData.filter(signal => signal.type === type);
    }

    // 排序
    switch (sortBy) {
      case 'name':
        filteredData.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
        break;
      case 'created':
        filteredData.sort((a, b) => (b.created || 0) - (a.created || 0));
        break;
      case 'used':
        filteredData.sort((a, b) => (b.sentCount || 0) - (a.sentCount || 0));
        break;
    }

    return filteredData;
  }

  /**
   * 应用过滤器
   */
  applyFilter(keyword = '', type = '', sortBy = 'name') {
    const filteredData = this.filterSignals(keyword, type, sortBy);
    this.setData(filteredData);
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    const baseStats = super.getPerformanceStats();
    return {
      ...baseStats,
      selectedCount: this.selectedSignals.size,
      viewMode: this.viewMode,
      multiSelectMode: this.isMultiSelectMode
    };
  }

  /**
   * 销毁虚拟列表
   */
  destroy() {
    this.selectedSignals.clear();
    super.destroy();
  }
}

// 导出类
window.SignalVirtualList = SignalVirtualList;
