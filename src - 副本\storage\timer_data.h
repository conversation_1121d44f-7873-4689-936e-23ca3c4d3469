#pragma once

#include <Arduino.h>
#include <ArduinoJson.h>
#include <vector>
#include "config/constants.h"

/**
 * @brief 定时任务数据结构
 */
struct TimerTask {
    String id;                    // 任务ID
    String name;                  // 任务名称
    String startTime;             // 开始时间 (HH:MM格式)
    String endTime;               // 结束时间 (HH:MM格式)
    std::vector<String> signalIds; // 信号ID列表
    bool isDaily;                 // 是否每日重复
    uint32_t intervalMinutes;     // 执行间隔（分钟）
    bool isEnabled;               // 是否启用
    uint64_t nextExecutionTime;   // 下次执行时间（Unix毫秒时间戳）
    uint64_t lastExecutionTime;   // 上次执行时间（Unix毫秒时间戳）
    uint32_t executionCount;      // 执行次数
    uint64_t createdTime;         // 创建时间（Unix毫秒时间戳）
    uint64_t updatedTime;         // 更新时间（Unix毫秒时间戳）

    /**
     * @brief 默认构造函数
     */
    TimerTask() 
        : isDaily(true)
        , intervalMinutes(60)
        , isEnabled(false)
        , nextExecutionTime(0)
        , lastExecutionTime(0)
        , executionCount(0)
        , createdTime(0)
        , updatedTime(0)
    {}

    /**
     * @brief 验证任务数据是否有效
     */
    bool isValid() const {
        return !id.isEmpty() && 
               !name.isEmpty() && 
               !startTime.isEmpty() && 
               !endTime.isEmpty() &&
               intervalMinutes > 0 &&
               !signalIds.empty();
    }

    /**
     * @brief 转换为JSON
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        
        doc["id"] = id;
        doc["name"] = name;
        doc["startTime"] = startTime;
        doc["endTime"] = endTime;
        doc["isDaily"] = isDaily;
        doc["intervalMinutes"] = intervalMinutes;
        doc["isEnabled"] = isEnabled;
        doc["nextExecutionTime"] = nextExecutionTime;
        doc["lastExecutionTime"] = lastExecutionTime;
        doc["executionCount"] = executionCount;
        doc["createdTime"] = createdTime;
        doc["updatedTime"] = updatedTime;
        
        // 信号ID数组 - ArduinoJson 7.x语法
        JsonArray signalArray = doc["signalIds"].to<JsonArray>();
        for (const String& signalId : signalIds) {
            signalArray.add(signalId);
        }
        
        return doc;
    }

    /**
     * @brief 从JSON创建任务
     */
    static TimerTask fromJson(const JsonDocument& doc) {
        TimerTask task;
        
        task.id = doc["id"].as<String>();
        task.name = doc["name"].as<String>();
        task.startTime = doc["startTime"].as<String>();
        task.endTime = doc["endTime"].as<String>();
        task.isDaily = doc["isDaily"].as<bool>();
        task.intervalMinutes = doc["intervalMinutes"].as<uint32_t>();
        task.isEnabled = doc["isEnabled"].as<bool>();
        task.nextExecutionTime = doc["nextExecutionTime"].as<uint64_t>();
        task.lastExecutionTime = doc["lastExecutionTime"].as<uint64_t>();
        task.executionCount = doc["executionCount"].as<uint32_t>();
        task.createdTime = doc["createdTime"].as<uint64_t>();
        task.updatedTime = doc["updatedTime"].as<uint64_t>();
        
        // 解析信号ID数组 - ArduinoJson 7.x兼容语法
        if (doc["signalIds"].is<JsonArray>()) {
            JsonArrayConst signalArray = doc["signalIds"];
            for (JsonVariantConst signalId : signalArray) {
                task.signalIds.push_back(signalId.as<String>());
            }
        }
        
        return task;
    }

    /**
     * @brief 生成任务ID
     */
    static String generateId() {
        return "timer_" + String(random(10000000, 99999999));
    }

    /**
     * @brief 计算下次执行时间
     */
    void calculateNextExecutionTime();

    /**
     * @brief 检查是否到了执行时间
     */
    bool isDue() const;

    /**
     * @brief 更新执行统计
     */
    void updateExecutionStats();
};

/**
 * @brief 定时器系统状态
 */
struct TimerSystemStatus {
    bool isEnabled;               // 定时器系统是否启用
    uint32_t totalTasks;          // 总任务数
    uint32_t activeTasks;         // 活跃任务数
    uint32_t executedTasks;       // 已执行任务数
    uint64_t nextTaskTime;        // 下个任务执行时间
    String nextTaskName;          // 下个任务名称
    uint64_t lastExecutionTime;   // 上次执行时间
    uint32_t totalExecutions;     // 总执行次数

    /**
     * @brief 转换为JSON
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        
        doc["isEnabled"] = isEnabled;
        doc["totalTasks"] = totalTasks;
        doc["activeTasks"] = activeTasks;
        doc["executedTasks"] = executedTasks;
        doc["nextTaskTime"] = nextTaskTime;
        doc["nextTaskName"] = nextTaskName;
        doc["lastExecutionTime"] = lastExecutionTime;
        doc["totalExecutions"] = totalExecutions;
        
        return doc;
    }
};

/**
 * @brief 定时器配置
 */
struct TimerConfig {
    bool masterEnabled;           // 主开关
    uint32_t checkInterval;       // 检查间隔（毫秒）
    uint32_t maxTasks;           // 最大任务数
    bool autoCleanup;            // 自动清理过期任务
    uint32_t cleanupDays;        // 清理天数

    TimerConfig() 
        : masterEnabled(false)
        , checkInterval(60000)    // 1分钟检查一次
        , maxTasks(50)
        , autoCleanup(true)
        , cleanupDays(30)
    {}

    /**
     * @brief 转换为JSON
     */
    JsonDocument toJson() const {
        JsonDocument doc;
        
        doc["masterEnabled"] = masterEnabled;
        doc["checkInterval"] = checkInterval;
        doc["maxTasks"] = maxTasks;
        doc["autoCleanup"] = autoCleanup;
        doc["cleanupDays"] = cleanupDays;
        
        return doc;
    }

    /**
     * @brief 从JSON更新配置
     */
    void updateFromJson(const JsonDocument& doc) {
        if (doc["masterEnabled"].is<bool>()) {
            masterEnabled = doc["masterEnabled"].as<bool>();
        }
        if (doc["checkInterval"].is<uint32_t>()) {
            checkInterval = doc["checkInterval"].as<uint32_t>();
        }
        if (doc["maxTasks"].is<uint32_t>()) {
            maxTasks = doc["maxTasks"].as<uint32_t>();
        }
        if (doc["autoCleanup"].is<bool>()) {
            autoCleanup = doc["autoCleanup"].as<bool>();
        }
        if (doc["cleanupDays"].is<uint32_t>()) {
            cleanupDays = doc["cleanupDays"].as<uint32_t>();
        }
    }
};
