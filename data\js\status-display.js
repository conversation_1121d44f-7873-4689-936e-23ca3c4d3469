/**
 * R1系统 - 状态显示模块
 * 实现系统状态监控、数据可视化等功能
 */

class StatusDisplay extends BaseModule {
  constructor(eventBus, esp32) {
    super(eventBus, esp32, 'StatusDisplay');

    // 状态显示特有属性
    this.systemStats = {
      totalSignals: 0,
      activeTimers: 0,
      runningTasks: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      networkStatus: 'disconnected',
      uptime: 0,
      lastUpdate: Date.now()
    };

    this.charts = new Map();
    this.updateInterval = null;
    this.refreshRate = 30000; // 🔧 改为30秒更新一次，大幅减少性能开销
    this.currentTaskSignals = null; // 缓存当前任务的信号列表

    // 延迟清空定时器
    this.clearTaskTimer = null;

    // 使用DOM更新管理器
    this.domUpdater = window.DOMUpdateManager;
  }

  async setupEventListeners() {
    // 监听系统状态变化
    this.eventBus.on('system.stats.updated', (data) => {
      this.updateSystemStats(data);
    });

    this.eventBus.on('esp32.connected', () => {
      this.systemStats.networkStatus = 'connected';
      this.updateNetworkStatus();
    });

    this.eventBus.on('esp32.disconnected', () => {
      this.systemStats.networkStatus = 'disconnected';
      this.updateNetworkStatus();
    });

    this.eventBus.on('signal.added', (data) => {
      this.systemStats.totalSignals++;
      this.updateSignalCount();
      // 同时更新学习计数
      this.updateLearnedSignalCount();
    });

    this.eventBus.on('signal.deleted', (data) => {
      this.systemStats.totalSignals = Math.max(0, this.systemStats.totalSignals - 1);
      this.updateSignalCount();
      // 同时更新学习计数
      this.updateLearnedSignalCount();
    });

    // 监听系统模块初始化完成事件
    this.eventBus.on('system.modules.initialized', () => {
      console.log('StatusDisplay: 收到系统模块初始化完成事件，开始自动更新');
      this.startAutoUpdate();
    });

    // ==================== 控制模块状态监听 - 核心显示内容 ====================
    // 控制模块是所有任务的实际执行者，显示模块应该主要显示控制模块的状态

    this.eventBus.on('control.emit.started', (data) => {
      // 取消之前的延迟清空定时器（如果有任务恢复）
      if (this.clearTaskTimer) {
        clearTimeout(this.clearTaskTimer);
        this.clearTaskTimer = null;
      }

      // ✅ 根源修复：现在BaseModule.emitEvent不再包装数据，直接使用
      const task = data.task;
      const taskInfo = {
        name: task?.name || `${task?.type || '未知'}任务`,
        type: task?.type || 'unknown',
        status: 'executing',
        totalSignals: task?.signals?.length || 0,
        currentIndex: task?.currentSignalIndex || 0,
        // 显示当前信号
        currentSignal: task?.signals?.[task?.currentSignalIndex || 0]?.name || '准备发射...'
      };

      console.log('📊 StatusDisplay: 解析后的任务信息:', taskInfo);

      this.updateCurrentTask(taskInfo);
      this.updateSystemStatus('executing', '执行中');

      // 显示当前信号
      const currentSignalIndex = task?.currentSignalIndex || 0;
      if (task?.signals?.[currentSignalIndex]) {
        this.updateCurrentSignal(task.signals[currentSignalIndex]);
      }

      // 缓存当前任务的信号列表
      this.currentTaskSignals = task?.signals || null;

      // 更新待发射信号列表
      this.updatePendingSignals(0, task?.signals?.length || 0, task?.signals);
    });

    this.eventBus.on('control.emit.progress', (data) => {
      console.log('📊 StatusDisplay: 收到 control.emit.progress 事件，原始数据:', data);

      // ✅ 简化：直接使用数据，显示发射中状态
      const task = data.task;
      const signal = data.currentSignal;

      console.log('📊 StatusDisplay: 解析后的进度信息:', {
        taskName: task?.name,
        currentIndex: task?.currentSignalIndex,
        totalSignals: task?.totalSignals,
        signalName: signal?.name,
        signalId: signal?.id,
        signalType: signal?.type,
        isLoopMode: task?.isLoopMode,
        progress: `${task?.currentSignalIndex}/${task?.totalSignals}`,
        rawEventData: data
      });

      this.updateCurrentTask({
        currentIndex: task?.currentSignalIndex || 0,
        totalSignals: task?.totalSignals || 0,
        currentSignal: signal?.name || signal?.signalCode || '发射中...'
      });

      // 更新待发射信号列表，使用缓存的信号数据
      this.updatePendingSignals(task?.currentSignalIndex - 1, task?.totalSignals || 0, this.currentTaskSignals);
      this.updateCurrentSignal(signal);
    });

    this.eventBus.on('control.emit.completed', (data) => {
      const task = data.task;

      console.log('🔍 [UI_UPDATE] 收到任务完成事件，准备更新UI', {
        taskId: task?.id,
        taskName: task?.name,
        taskType: task?.type,
        timestamp: Date.now()
      });

      this.updateCurrentTask({
        status: 'completed',
        completedAt: Date.now(),
        duration: task?.duration
      });
      this.updateSystemStatus('idle', '待机');

      console.log('🔍 [UI_UPDATE] 任务完成UI更新完成，3秒后清空显示', {
        timestamp: Date.now()
      });

      // 清除之前的延迟清空定时器
      if (this.clearTaskTimer) {
        clearTimeout(this.clearTaskTimer);
        this.clearTaskTimer = null;
      }

      // 3秒后清空任务显示
      this.clearTaskTimer = setTimeout(() => {
        console.log('🔍 [UI_UPDATE] 执行延迟清空任务显示', {
          timestamp: Date.now()
        });
        this.clearCurrentTask();
        // 恢复系统状态到初始化状态
        this.updateSystemStatus('idle', '待机');
        this.clearTaskTimer = null;
      }, 3000);
    });

    this.eventBus.on('control.emit.paused', (data) => {
      console.log('📊 StatusDisplay: 收到任务暂停事件:', data);

      // 无论是什么原因暂停，都统一显示"暂停中"，简洁明了
      console.log('📊 StatusDisplay: 更新任务状态为暂停');
      this.updateCurrentTask({ status: 'paused' });

      console.log('📊 StatusDisplay: 更新系统状态为暂停中');
      this.updateSystemStatus('paused', '暂停中');

      // 更新当前信号状态为暂停
      console.log('📊 StatusDisplay: 更新当前信号状态为暂停中');
      this.updateCurrentSignalState('paused', '暂停中');

      // 在日志中区分暂停原因，但UI显示保持一致
      if (data.pausedForLearning) {
        console.log('📊 StatusDisplay: 因学习模式暂停发射任务');
      } else {
        console.log('📊 StatusDisplay: 因其他原因暂停发射任务');
      }
    });

    this.eventBus.on('control.emit.resumed', (data) => {
      console.log('📊 StatusDisplay: 收到任务恢复事件:', data);

      // 通过事件系统检查是否在学习模式 - 符合架构标准
      let isLearning = false;
      this.emitEvent('signal.learning.status.request', {
        callback: (data) => {
          isLearning = data?.isLearning || false;
        }
      });

      this.updateCurrentTask({ status: 'executing' });

      // 更新当前信号状态为发射中
      this.updateCurrentSignalState('transmitting', '发射中');

      if (isLearning) {
        console.log('📊 StatusDisplay: 学习模式中，保持学习状态显示');
        // 不更新系统状态，保持学习状态显示
      } else {
        console.log('📊 StatusDisplay: 非学习模式，显示执行状态');
        this.updateSystemStatus('executing', '执行中');
      }
    });

    this.eventBus.on('control.emit.stopped', (data) => {
      this.updateCurrentTask({ status: 'stopped' });
      this.updateSystemStatus('idle', '待机');
      window.UnifiedTimerManager.addTimer(
        'status_reset_after_stop',
        () => {
          this.clearCurrentTask();
          // 恢复系统状态到初始化状态
          this.updateSystemStatus('idle', '待机');
        },
        2000,
        false
      );
    });

    // ==================== 定时模块状态监听 - 辅助信息 ====================

    this.eventBus.on('timer.task.queue.updated', (data) => {
      this.updateTimerInfo({
        activeTasksCount: data.activeTasksCount,
        nextTask: data.nextTask
      });
    });

    this.eventBus.on('timer.task.execution.request', (data) => {
      // 定时器触发时显示即将执行的任务信息
      this.updateUpcomingTask({
        name: data.task?.name,
        type: 'timer',
        scheduledTime: data.requestTime
      });
    });

    // ==================== 信号管理模块状态监听 - 学习状态 ====================

    this.eventBus.on('signal.learning.started', (data) => {
      console.log('📊 StatusDisplay: 收到学习开始事件');

      // 通过事件系统检查是否有任务被暂停 - 符合架构标准
      let hasPausedTasks = false;
      this.emitEvent('control.paused-tasks.status.request', {
        callback: (data) => {
          hasPausedTasks = data?.hasPausedTasks || false;
        }
      });

      if (hasPausedTasks) {
        console.log('📊 StatusDisplay: 有任务被暂停，保持暂停状态显示');
        // 不更新系统状态，保持"暂停中"显示

        // 更新当前信号状态为暂停中（全局状态管理）
        this.updateCurrentSignalState('paused', '暂停中');
      } else {
        console.log('📊 StatusDisplay: 无暂停任务，显示学习状态');
        this.updateSystemStatus('learning', '学习中');

        // 无暂停任务时，当前信号状态显示为学习中
        this.updateCurrentSignalState('learning', '学习中');
      }

      this.updateLearningStatus('active');
      // 立即更新学习信号计数
      this.updateLearnedSignalCount();
    });

    this.eventBus.on('signal.learning.stopped', (data) => {
      console.log('📊 StatusDisplay: 收到学习停止事件');

      // 通过事件系统检查是否有任务正在执行 - 符合架构标准
      let isTaskExecuting = false;
      this.emitEvent('control.task.status.request', {
        callback: (data) => {
          isTaskExecuting = data?.isTaskExecuting || false;
        }
      });

      if (isTaskExecuting) {
        console.log('📊 StatusDisplay: 学习停止，有任务正在执行，恢复执行状态');
        this.updateSystemStatus('executing', '执行中');

        // 恢复当前信号状态为发射中（全局状态管理）
        this.updateCurrentSignalState('transmitting', '发射中');
      } else {
        console.log('📊 StatusDisplay: 学习停止，无任务执行，显示待机状态');
        this.updateSystemStatus('idle', '待机');

        // 无任务时，当前信号状态为待机
        this.updateCurrentSignalState('idle', '待机');
        this.clearCurrentTask();
        // 确保系统状态恢复到初始化状态
        this.updateSystemStatus('idle', '待机');
      }

      this.updateLearningStatus('idle');
    });

    this.eventBus.on('signal.detected', (data) => {
      console.log('📊 StatusDisplay: 收到信号检测事件:', data);
      this.updateLearningStatus('detected', data.signalData?.name);
      // 立即更新学习信号计数
      this.updateLearnedSignalCount();
    });

    // ✅ 已合并到第47行的signal.added事件监听器中，避免重复注册

    // ==================== 统一的发射状态监听 ====================
    // 所有发射操作（信号管理模块、控制模块、定时模块）都通过控制模块的统一事件进行状态更新
  }

  async setupUI() {
    this.initSidebarDisplay();
    // 不在初始化时立即开始自动更新，等待所有模块初始化完成
  }

  async loadModuleData() {
    // 不在初始化时立即刷新统计数据，等待所有模块初始化完成
    console.log('StatusDisplay 数据加载完成，等待其他模块初始化');
  }

  /**
   * 初始化状态显示区域 - 专注任务状态显示
   */
  initSidebarDisplay() {
    const container = $('#statusDisplayArea');
    if (!container) return;

    // 修改标题区域，添加状态指示器到右侧
    const titleArea = container.parentElement.querySelector('h3');
    if (titleArea) {
      titleArea.innerHTML = `
        📊 状态显示
        <div id="systemStatusIndicator" style="
          position: absolute;
          right: 0;
          bottom: var(--spacing-sm);
          display: flex;
          align-items: baseline;
          white-space: nowrap;
        ">
          <span id="systemStatusDot" style="
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--text-secondary);
            transition: background-color 0.2s ease;
            margin-right: 8px;
            vertical-align: baseline;
            flex-shrink: 0;
          "></span>
          <span id="systemStatusText" style="
            font-size: 0.75rem;
            font-weight: 400;
            color: var(--text-secondary);
            white-space: nowrap;
            line-height: 1;
            vertical-align: baseline;
          ">待机</span>
        </div>
      `;

      // 设置h3为相对定位，让指示器可以绝对定位
      titleArea.style.position = 'relative';
      titleArea.style.paddingRight = '80px'; // 给指示器留出空间，会根据文字长度动态调整
    }

    container.innerHTML = `
      <div class="status-display-content">

        <!-- 当前任务状态 - 合并后的完整任务信息 -->
        <div class="current-task-status">
          <h4>📋 当前任务</h4>
          <div class="task-info">
            <div class="task-name" id="currentTaskName">无任务</div>
            <div class="task-details" id="currentTaskDetails">系统待机中</div>
          </div>

          <!-- 当前信号信息 -->
          <div class="current-signal-info">
            <div class="signal-label">当前信号:</div>
            <div class="signal-content">
              <span class="signal-name" id="currentSignalName">无信号</span>
              <span class="signal-type" id="currentSignalType"></span>
              <span class="signal-state" id="currentSignalState">待机</span>
            </div>
          </div>

          <!-- 进度条作为分割线 -->
          <div class="task-progress">
            <div class="progress-bar">
              <div class="progress-fill" id="taskProgressFill" style="width: 0%"></div>
            </div>
            <div class="progress-text">
              <span id="taskProgressCurrent">0</span> / <span id="taskProgressTotal">0</span>
            </div>
          </div>

          <!-- 待发射列表 -->
          <div class="pending-signals" id="pendingSignalsList">
            <div class="pending-label">待发射:</div>
            <div class="pending-list" id="pendingSignalsContent">无</div>
          </div>
        </div>

        <!-- 学习状态 -->
        <div class="learning-status">
          <h4>🎓 学习状态</h4>
          <div class="learning-info">
            <div class="learning-state" id="learningState">未学习</div>
            <div class="learning-count">
              <span class="learned-label">已学习:</span>
              <span class="learned-count" id="learnedSignalCount">0</span>
            </div>
          </div>
        </div>
      </div>
    `;
  }





  /**
   * 移除自动更新 - 改为事件驱动更新
   */
  startAutoUpdate() {


    // 完全移除定时器，避免无意义的循环
    // 状态变化时通过事件系统触发更新，而不是定时轮询

    // 只执行一次初始更新
    this.refreshSystemStats();
  }

  /**
   * 停止自动更新
   */
  stopAutoUpdate() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  /**
   * 刷新系统统计数据 - 符合架构标准
   */
  async refreshSystemStats() {
    try {
      // 检查系统是否已经初始化
      if (!window.R1System || !window.R1System.isInitialized) {
        return;
      }

      // 获取内存使用情况
      if (performance.memory) {
        const memoryUsed = performance.memory.usedJSHeapSize;
        const memoryLimit = performance.memory.jsHeapSizeLimit;
        this.systemStats.memoryUsage = Math.round((memoryUsed / memoryLimit) * 100);
      }

      // 通过事件系统获取运行时间 - 符合架构标准
      this.emitEvent('system.uptime.request', {
        callback: (data) => {
          this.systemStats.uptime = data?.uptime || 0;
        }
      });

      this.updateAllDisplays();

    } catch (error) {
      this.handleError(error, '系统状态刷新');
    }
  }

  /**
   * 更新所有显示
   */
  updateAllDisplays() {
    this.updateSystemStatusDisplay();
    this.updateCurrentTaskDisplay(); // 现在包含了信号和队列信息
    this.updateLearningStatusDisplay();
  }

  /**
   * 更新系统状态显示
   */
  updateSystemStatusDisplay() {
    const systemStatusDot = $('#systemStatusDot');
    const systemStatusText = $('#systemStatusText');
    const systemStatusIndicator = $('#systemStatusIndicator');

    if (systemStatusDot && systemStatusText && systemStatusIndicator) {
      // 通过事件系统检查系统初始化状态 - 符合架构标准
      let isSystemInitialized = false;
      this.emitEvent('system.initialization.status.request', {
        callback: (data) => {
          isSystemInitialized = data?.isInitialized || false;
        }
      });

      if (!isSystemInitialized) {
        return;
      }

      // 通过事件系统获取模块状态 - 符合架构标准
      let statusText = '待机';
      let statusClass = 'status-idle';

      // 发布事件请求状态信息
      this.emitEvent('status.request.system-state', {
        callback: (systemState) => {
          if (systemState?.isLearning) {
            statusText = '学习中';
            statusClass = 'status-learning';
          } else if (systemState?.isExecuting) {
            statusText = '发射中';
            statusClass = 'status-transmitting';
          } else if (systemState?.isPaused) {
            statusText = '已暂停';
            statusClass = 'status-paused';
          }
        }
      });

      // 设置状态点和文字的颜色、动画
      let dotColor = 'var(--text-secondary)';
      let textColor = 'var(--text-secondary)';
      let animation = 'none';

      switch(statusClass) {
        case 'status-learning':
          dotColor = 'var(--info-color)';
          textColor = 'var(--info-color)';
          animation = 'pulse 1s infinite';
          break;
        case 'status-transmitting':
          dotColor = 'var(--primary-color)';
          textColor = 'var(--primary-color)';
          animation = 'pulse 1s infinite';
          break;
        case 'status-paused':
          dotColor = 'var(--warning-color)';
          textColor = 'var(--warning-color)';
          animation = 'pulse 1.5s infinite';
          break;
        default: // status-idle 待机状态
          dotColor = 'var(--text-secondary)';
          textColor = 'var(--text-secondary)';
          animation = 'none'; // 灰色不闪动
      }

      systemStatusDot.style.background = dotColor;
      systemStatusDot.style.animation = animation;
      systemStatusText.style.color = textColor;
      systemStatusText.textContent = statusText;

      // 动态调整h3的右边距，根据指示器的实际宽度
      // 指示器宽度 = 8px(点) + 文字大小(间隔) + 文字宽度
      const titleArea = systemStatusIndicator.parentElement;
      if (titleArea) {
        // 直接计算宽度，移除不必要的定时器
        const indicatorWidth = systemStatusIndicator.offsetWidth;
        titleArea.style.paddingRight = `${indicatorWidth + 10}px`; // 额外10px边距
      }
    }
  }

  /**
   * 更新当前任务显示 - 基于事件驱动的状态更新
   */
  updateCurrentTaskDisplay() {
    // 这个方法现在主要通过事件驱动更新，保留用于兼容性
    // 实际的状态更新通过 updateCurrentTask() 等新方法处理
    console.log('StatusDisplay: updateCurrentTaskDisplay 被调用，现在主要通过事件驱动更新');
  }



  /**
   * 更新学习状态显示
   */
  updateLearningStatusDisplay() {
    // 通过事件系统检查系统初始化状态 - 符合架构标准
    let isSystemInitialized = false;
    this.emitEvent('system.initialization.status.request', {
      callback: (data) => {
        isSystemInitialized = data?.isInitialized || false;
      }
    });

    if (!isSystemInitialized) {
      return;
    }

    const learningState = $('#learningState');
    const learnedCount = $('#learnedSignalCount');

    // 通过事件系统获取学习状态
    this.requestLearningStatusViaEvent();

    // 通过事件系统获取学习状态 - 符合架构标准
    let isLearning = false;
    let totalSignals = 0;

    this.emitEvent('signal.learning.status.request', {
      callback: (data) => {
        isLearning = data?.isLearning || false;
      }
    });

    this.emitEvent('signal.request.count', {
      callback: (count) => {
        totalSignals = count || 0;
      }
    });

    if (learningState) {
      if (isLearning) {
        learningState.textContent = '学习中...';
        learningState.className = 'learning-state active';
      } else {
        learningState.textContent = '未学习';
        learningState.className = 'learning-state idle';
      }
    }

    if (learnedCount) {
      learnedCount.textContent = totalSignals;
    }
  }

  /**
   * 格式化运行时间
   */
  formatUptime(uptime) {
    const hours = Math.floor(uptime / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((uptime % (1000 * 60)) / 1000);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }



  /**
   * 更新进度条
   */
  updateProgressBars() {
    // 更新内存使用率
    const memoryBar = $('#memoryProgressBar');
    const memoryText = $('#memoryUsageText');
    if (memoryBar && memoryText) {
      memoryBar.style.width = `${this.systemStats.memoryUsage}%`;
      memoryText.textContent = `${this.systemStats.memoryUsage}%`;

      // 根据使用率设置颜色
      if (this.systemStats.memoryUsage > 80) {
        memoryBar.className = 'progress-fill danger';
      } else if (this.systemStats.memoryUsage > 60) {
        memoryBar.className = 'progress-fill warning';
      } else {
        memoryBar.className = 'progress-fill success';
      }
    }

    // 更新CPU使用率（模拟数据）
    const cpuBar = $('#cpuProgressBar');
    const cpuText = $('#cpuUsageText');
    if (cpuBar && cpuText) {
      this.systemStats.cpuUsage = Math.random() * 30 + 10; // 模拟10-40%的CPU使用率
      cpuBar.style.width = `${this.systemStats.cpuUsage.toFixed(1)}%`;
      cpuText.textContent = `${this.systemStats.cpuUsage.toFixed(1)}%`;

      if (this.systemStats.cpuUsage > 80) {
        cpuBar.className = 'progress-fill danger';
      } else if (this.systemStats.cpuUsage > 60) {
        cpuBar.className = 'progress-fill warning';
      } else {
        cpuBar.className = 'progress-fill success';
      }
    }
  }





  /**
   * 更新系统统计数据
   */
  updateSystemStats(data) {
    Object.assign(this.systemStats, data);
    this.updateAllDisplays();
  }

  /**
   * 更新网络状态
   */
  updateNetworkStatus() {
    // 网络状态不再显示
  }

  /**
   * 更新信号数量
   */
  updateSignalCount() {
    this.updateCurrentTaskDisplay(); // 现在包含了信号信息
    this.updateLearningStatusDisplay();
  }

  /**
   * 更新当前任务信息 - 基于控制模块状态
   */
  updateCurrentTask(taskInfo) {
    // 批量文本更新
    const textUpdates = [];

    if (taskInfo.name) {
      textUpdates.push({
        elementId: 'currentTaskName',
        text: taskInfo.name
      });
    }

    if (taskInfo.status) {
      const statusText = {
        'executing': '正在执行...',
        'paused': '已暂停',
        'completed': '执行完成',
        'stopped': '已停止',
        'failed': '执行失败'
      };
      textUpdates.push({
        elementId: 'currentTaskDetails',
        text: statusText[taskInfo.status] || taskInfo.status
      });
    }

    // 更新进度条
    if (typeof taskInfo.currentIndex === 'number' && typeof taskInfo.totalSignals === 'number') {
      const percentage = taskInfo.totalSignals > 0 ?
        Math.round((taskInfo.currentIndex / taskInfo.totalSignals) * 100) : 0;

      // 进度条样式更新
      this.domUpdater.scheduleUpdate('taskProgressFill', () => {
        const progressFill = $('#taskProgressFill');
        if (progressFill) progressFill.style.width = `${percentage}%`;
      });

      // 进度数字更新
      textUpdates.push(
        { elementId: 'taskProgressCurrent', text: taskInfo.currentIndex.toString() },
        { elementId: 'taskProgressTotal', text: taskInfo.totalSignals.toString() }
      );
    }

    // 批量执行文本更新
    if (textUpdates.length > 0) {
      this.domUpdater.batchTextUpdate(textUpdates);
    }
  }

  /**
   * 更新当前信号信息
   */
  updateCurrentSignal(signal) {
    const signalName = $('#currentSignalName');
    const signalType = $('#currentSignalType');
    const signalState = $('#currentSignalState');

    if (signal && signalName) {
      // 截断过长的信号名称，保留前20个字符
      const fullName = signal.name || '未知信号';
      const displayName = fullName.length > 20 ? fullName.substring(0, 20) + '...' : fullName;
      signalName.textContent = displayName;
      // 设置完整名称作为title，鼠标悬停时显示
      signalName.title = fullName;
    }
    if (signal && signalType) {
      signalType.textContent = signal.type ? `(${signal.type})` : '';
    }
    if (signalState) {
      signalState.textContent = '发射中';
      signalState.className = 'signal-state transmitting';
    }
  }

  /**
   * 更新当前信号状态
   */
  updateCurrentSignalState(status, text) {
    const signalState = $('#currentSignalState');

    if (signalState) {
      signalState.textContent = text;
      signalState.className = `signal-state ${status}`;
      console.log(`📊 StatusDisplay: 信号状态已更新为: ${text}`);
    }
  }

  /**
   * 更新待发射信号显示 - 显示接下来的2个信号
   */
  updatePendingSignals(currentIndex, totalSignals, allSignals = null) {
    const pendingSignalsContent = $('#pendingSignalsContent');

    if (!pendingSignalsContent) return;

    // 如果没有信号数据，显示无
    if (!allSignals || totalSignals === 0) {
      pendingSignalsContent.textContent = '无';
      return;
    }

    // 获取接下来的2个待发射信号
    const nextSignals = [];
    for (let i = currentIndex + 1; i < Math.min(currentIndex + 3, totalSignals); i++) {
      if (allSignals[i]) {
        // 截断信号名称，最多显示12个字符
        const signalName = allSignals[i].name || `信号${i + 1}`;
        const displayName = signalName.length > 12 ? signalName.substring(0, 12) + '...' : signalName;
        nextSignals.push(displayName);
      }
    }

    if (nextSignals.length > 0) {
      pendingSignalsContent.textContent = nextSignals.join('、');
      // 设置title显示完整信息
      const fullNames = [];
      for (let i = currentIndex + 1; i < Math.min(currentIndex + 3, totalSignals); i++) {
        if (allSignals[i]) {
          fullNames.push(allSignals[i].name || `信号${i + 1}`);
        }
      }
      pendingSignalsContent.title = fullNames.join('、');
    } else {
      pendingSignalsContent.textContent = '无';
      pendingSignalsContent.title = '';
    }
  }

  /**
   * 更新系统状态指示器
   */
  updateSystemStatus(status, text) {
    console.log(`📊 StatusDisplay: updateSystemStatus 被调用 - status: ${status}, text: ${text}`);

    // 设置状态点和文字的颜色、动画
    let dotColor = 'var(--text-secondary)';
    let textColor = 'var(--text-secondary)';
    let animation = 'none';

    switch(status) {
      case 'learning':
        dotColor = 'var(--info-color)';
        textColor = 'var(--info-color)';
        animation = 'pulse 1s infinite';
        break;
      case 'executing':
        dotColor = 'var(--primary-color)';
        textColor = 'var(--primary-color)';
        animation = 'pulse 1s infinite';
        break;
      case 'paused':
        dotColor = 'var(--warning-color)';
        textColor = 'var(--warning-color)';
        animation = 'pulse 1.5s infinite';
        break;
      default: // idle
        dotColor = 'var(--text-secondary)';
        textColor = 'var(--text-secondary)';
        animation = 'none';
    }

    // 批量样式更新
    this.domUpdater.batchStyleUpdate([
      {
        elementId: 'systemStatusDot',
        styles: {
          background: dotColor,
          animation: animation
        }
      },
      {
        elementId: 'systemStatusText',
        styles: {
          color: textColor
        }
      }
    ]);

    // 文本更新
    this.domUpdater.batchTextUpdate([
      {
        elementId: 'systemStatusText',
        text: text
      }
    ]);

    console.log(`📊 StatusDisplay: 状态更新完成 - 显示文本: ${text}, 颜色: ${textColor}`);
  }

  /**
   * 更新定时器信息
   */
  updateTimerInfo(timerInfo) {
    // 可以在界面上显示定时器相关信息
    console.log('定时器状态更新:', timerInfo);
  }

  /**
   * 更新即将执行的任务
   */
  updateUpcomingTask(taskInfo) {
    // 可以显示即将执行的定时任务信息
    console.log('即将执行任务:', taskInfo);
  }

  /**
   * 检查是否有任务正在执行 - 通过事件系统查询
   */
  isTaskExecuting() {
    // 通过事件系统检查控制模块任务状态 - 符合架构标准
    let isExecuting = false;
    this.emitEvent('control.task.status.request', {
      callback: (data) => {
        isExecuting = data?.isTaskExecuting || false;
      }
    });
    return isExecuting;
  }

  /**
   * 更新学习状态
   */
  updateLearningStatus(status, signalName = null) {
    const learningState = $('#learningState');

    if (learningState) {
      switch(status) {
        case 'active':
          learningState.textContent = '学习中...';
          learningState.className = 'learning-state active';
          break;
        case 'detected':
          learningState.textContent = `检测到: ${signalName || '新信号'}`;
          learningState.className = 'learning-state detected';
          // 3秒后恢复到学习中状态
          setTimeout(() => {
            if (learningState.className === 'learning-state detected') {
              learningState.textContent = '学习中...';
              learningState.className = 'learning-state active';
            }
          }, 3000);
          break;
        default: // idle
          learningState.textContent = '未学习';
          learningState.className = 'learning-state idle';
      }
    }
  }

  /**
   * 更新学习信号计数
   */
  updateLearnedSignalCount() {
    const learnedCount = $('#learnedSignalCount');
    if (learnedCount) {
      // 通过事件系统获取信号数量
      this.emitEvent('signal.request.count', {
        callback: (count) => {
          learnedCount.textContent = count || 0;
        }
      });
    }
  }

  /**
   * 清空当前任务显示 - 恢复到完全的初始化状态
   */
  clearCurrentTask() {
    // 恢复任务信息到初始化状态
    const taskName = $('#currentTaskName');
    const taskDetails = $('#currentTaskDetails');
    const progressFill = $('#taskProgressFill');
    const progressCurrent = $('#taskProgressCurrent');
    const progressTotal = $('#taskProgressTotal');

    if (taskName) taskName.textContent = '无任务';
    if (taskDetails) taskDetails.textContent = '系统待机中'; // 恢复到初始化状态
    if (progressFill) progressFill.style.width = '0%';
    if (progressCurrent) progressCurrent.textContent = '0';
    if (progressTotal) progressTotal.textContent = '0';

    // 恢复信号信息到初始化状态
    const signalName = $('#currentSignalName');
    const signalType = $('#currentSignalType');
    const signalState = $('#currentSignalState');
    const pendingSignalsContent = $('#pendingSignalsContent');

    if (signalName) signalName.textContent = '无信号';
    if (signalType) signalType.textContent = '';
    if (signalState) {
      signalState.textContent = '待机';
      signalState.className = 'signal-state idle';
    }
    if (pendingSignalsContent) {
      pendingSignalsContent.textContent = '无';
      pendingSignalsContent.title = '';
    }

    // 清空缓存的信号列表
    this.currentTaskSignals = null;

    console.log('📊 StatusDisplay: 任务显示已完全清空到初始化状态');
  }

  /**
   * 刷新模块数据
   */
  async refresh() {
    await this.refreshSystemStats();
    this.handleSuccess('状态显示已刷新', '数据刷新');
  }

  /**
   * 通过事件系统请求统计数据
   */
  requestStatsViaEvent() {
    this.emitEvent('status.request.stats', {
      callback: (stats) => {
        if (stats) {
          Object.assign(this.systemStats, stats);
        }
      }
    });
  }

  /**
   * 通过事件系统请求模块状态
   */
  requestModuleStatusViaEvent() {
    this.emitEvent('status.request.module-status', {
      callback: (status) => {
        if (status) {
          this.updateSystemStatusWithData(status);
        }
      }
    });
  }

  /**
   * 通过事件系统请求任务状态
   */
  requestTaskStatusViaEvent() {
    this.emitEvent('status.request.task-status', {
      callback: (taskStatus) => {
        if (taskStatus) {
          this.updateCurrentTaskWithData(taskStatus);
        }
      }
    });
  }

  /**
   * 通过事件系统请求学习状态
   */
  requestLearningStatusViaEvent() {
    this.emitEvent('status.request.learning-status', {
      callback: (learningStatus) => {
        if (learningStatus) {
          this.updateLearningStatusWithData(learningStatus);
        }
      }
    });
  }

  /**
   * 销毁模块 - 清理所有资源
   */
  destroy() {
    try {
      this.stopAutoUpdate();

      // 清理事件监听器
      this.eventBus.off('control.emit.progress');
      this.eventBus.off('control.emit.complete');
      this.eventBus.off('control.emit.error');
      this.eventBus.off('control.task.status.changed');
      this.eventBus.off('timer.task.status.changed');
      this.eventBus.off('signal.learning.status.changed');

      console.log('StatusDisplay: 模块已销毁');
    } catch (error) {
      console.error('StatusDisplay: 销毁模块失败:', error);
    }
  }
}

window.StatusDisplay = StatusDisplay;
