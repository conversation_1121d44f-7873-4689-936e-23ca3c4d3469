#ifndef TASK_SCHEDULER_H
#define TASK_SCHEDULER_H

/**
 * @file task_scheduler.h
 * @brief 任务调度器 - 管理定时任务和批量操作
 * @details 支持优先级调度、任务队列管理、定时执行
 * @version 1.0.0
 * @date 2025-01-07
 */

#include <Arduino.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/queue.h>
#include <freertos/timers.h>
#include <vector>
#include <queue>
#include <functional>

#include "config/config.h"
#include "config/constants.h"
#include "storage/data_structures.h"

/**
 * @brief 任务调度器 - 单例模式
 * @details 管理系统中的所有定时任务和批量操作
 */
class TaskScheduler {
public:
    // 任务状态枚举
    enum TaskStatus {
        PENDING = 0,
        RUNNING = 1,
        PAUSED = 2,
        COMPLETED = 3,
        FAILED = 4,
        CANCELLED = 5
    };

    // 任务类型枚举
    enum TaskType {
        SIGNAL_EMIT = 0,
        SIGNAL_LEARNING = 1,
        BATCH_OPERATION = 2,
        TIMER_TASK = 3,
        SYSTEM_MAINTENANCE = 4
    };

    // 任务结构
    struct ScheduledTask {
        String id;
        String name;
        TaskType type;
        uint8_t priority;           // 1-4，数值越大优先级越高
        TaskStatus status;
        uint64_t createTime;
        uint64_t scheduleTime;      // 计划执行时间
        uint64_t startTime;
        uint64_t endTime;
        uint32_t timeout;           // 超时时间(ms)
        uint32_t retryCount;        // 重试次数
        uint32_t maxRetries;        // 最大重试次数
        JsonDocument taskData;      // 任务数据
        std::function<bool()> executor;  // 任务执行函数
        std::function<void(bool)> callback;  // 完成回调
        
        ScheduledTask() : priority(1), status(PENDING), createTime(0), 
                         scheduleTime(0), startTime(0), endTime(0), 
                         timeout(30000), retryCount(0), maxRetries(3) {}
    };

private:
    static TaskScheduler* instance;

    // 任务队列 (按优先级排序)
    std::priority_queue<ScheduledTask*, std::vector<ScheduledTask*>, 
                       std::function<bool(ScheduledTask*, ScheduledTask*)>> taskQueue;
    
    // 运行中的任务
    std::vector<ScheduledTask*> runningTasks;
    
    // 已完成的任务历史
    std::vector<ScheduledTask*> taskHistory;
    
    // 定时器任务
    std::vector<ScheduledTask*> timerTasks;
    
    // 同步原语
    SemaphoreHandle_t schedulerMutex;
    QueueHandle_t taskEventQueue;
    
    // 调度器状态
    bool isRunning;
    uint32_t maxConcurrentTasks;
    uint32_t totalTasksExecuted;
    uint32_t totalTasksFailed;
    
    // 任务执行线程
    TaskHandle_t schedulerTaskHandle;
    
    // 私有构造函数
    TaskScheduler();
    
    // 禁用拷贝构造和赋值
    TaskScheduler(const TaskScheduler&) = delete;
    TaskScheduler& operator=(const TaskScheduler&) = delete;

public:
    /**
     * @brief 获取任务调度器实例
     * @return TaskScheduler& 调度器引用
     */
    static TaskScheduler& getInstance();
    
    /**
     * @brief 析构函数
     */
    ~TaskScheduler();
    
    // ==================== 生命周期管理 ====================
    
    /**
     * @brief 初始化任务调度器
     * @return bool 初始化是否成功
     */
    bool begin();
    
    /**
     * @brief 停止任务调度器
     */
    void stop();
    
    /**
     * @brief 调度器主循环
     */
    void loop();
    
    // ==================== 任务管理 ====================
    
    /**
     * @brief 添加即时任务
     * @param name 任务名称
     * @param type 任务类型
     * @param priority 优先级 (1-4)
     * @param executor 执行函数
     * @param callback 完成回调
     * @param timeout 超时时间(ms)
     * @return String 任务ID
     */
    String addTask(const String& name, TaskType type, uint8_t priority,
                   std::function<bool()> executor, 
                   std::function<void(bool)> callback = nullptr,
                   uint32_t timeout = 30000);
    
    /**
     * @brief 添加定时任务
     * @param name 任务名称
     * @param type 任务类型
     * @param priority 优先级
     * @param scheduleTime 计划执行时间
     * @param executor 执行函数
     * @param callback 完成回调
     * @return String 任务ID
     */
    String addTimerTask(const String& name, TaskType type, uint8_t priority,
                        uint64_t scheduleTime, std::function<bool()> executor,
                        std::function<void(bool)> callback = nullptr);
    
    /**
     * @brief 添加信号发射任务
     * @param signalIds 信号ID列表
     * @param priority 优先级
     * @param isLoopMode 是否循环模式
     * @param emitRate 发射速率
     * @param intervalRate 间隔速率
     * @return String 任务ID
     */
    String addSignalEmitTask(const std::vector<String>& signalIds, 
                            uint8_t priority = 2,
                            bool isLoopMode = false,
                            uint32_t emitRate = 1000,
                            uint32_t intervalRate = 500);
    
    /**
     * @brief 添加批量操作任务
     * @param operations 操作列表
     * @param priority 优先级
     * @return String 任务ID
     */
    String addBatchTask(const JsonArray& operations, uint8_t priority = 1);
    
    /**
     * @brief 取消任务
     * @param taskId 任务ID
     * @return bool 取消是否成功
     */
    bool cancelTask(const String& taskId);
    
    /**
     * @brief 暂停任务
     * @param taskId 任务ID
     * @return bool 暂停是否成功
     */
    bool pauseTask(const String& taskId);
    
    /**
     * @brief 恢复任务
     * @param taskId 任务ID
     * @return bool 恢复是否成功
     */
    bool resumeTask(const String& taskId);
    
    /**
     * @brief 获取任务状态
     * @param taskId 任务ID
     * @return TaskData 任务数据
     */
    TaskData getTaskStatus(const String& taskId) const;
    
    /**
     * @brief 获取所有任务列表
     * @return std::vector<TaskData> 任务列表
     */
    std::vector<TaskData> getAllTasks() const;
    
    /**
     * @brief 获取运行中的任务
     * @return std::vector<TaskData> 运行中的任务列表
     */
    std::vector<TaskData> getRunningTasks() const;
    
    /**
     * @brief 清空已完成的任务历史
     */
    void clearTaskHistory();
    
    // ==================== 优先级管理 ====================
    
    /**
     * @brief 暂停所有低优先级任务
     * @param minPriority 最低优先级阈值
     * @return std::vector<String> 被暂停的任务ID列表
     */
    std::vector<String> pauseLowPriorityTasks(uint8_t minPriority);
    
    /**
     * @brief 恢复被暂停的任务
     * @param taskIds 任务ID列表
     */
    void resumePausedTasks(const std::vector<String>& taskIds);
    
    /**
     * @brief 设置任务优先级
     * @param taskId 任务ID
     * @param priority 新优先级
     * @return bool 设置是否成功
     */
    bool setTaskPriority(const String& taskId, uint8_t priority);
    
    // ==================== 状态查询 ====================
    
    /**
     * @brief 获取调度器统计信息
     * @return JsonDocument 统计信息
     */
    JsonDocument getSchedulerStats() const;
    
    /**
     * @brief 检查是否有运行中的任务
     * @return bool 是否有运行中的任务
     */
    bool hasRunningTasks() const { return !runningTasks.empty(); }
    
    /**
     * @brief 获取队列中的任务数量
     * @return uint32_t 队列任务数量
     */
    uint32_t getQueueSize() const { return taskQueue.size(); }
    
    /**
     * @brief 获取运行中的任务数量
     * @return uint32_t 运行中任务数量
     */
    uint32_t getRunningTaskCount() const { return runningTasks.size(); }
    
    // ==================== 事件处理 ====================
    
    /**
     * @brief 发送任务事件
     * @param eventType 事件类型
     * @param taskId 任务ID
     * @param eventData 事件数据
     */
    void sendTaskEvent(const String& eventType, const String& taskId, 
                      const JsonDocument& eventData = JsonDocument());

private:
    // ==================== 私有方法 ====================
    
    /**
     * @brief 执行下一个任务
     */
    void executeNextTask();
    
    /**
     * @brief 检查定时任务
     */
    void checkTimerTasks();
    
    /**
     * @brief 清理已完成的任务
     */
    void cleanupCompletedTasks();
    
    /**
     * @brief 处理任务超时
     */
    void handleTaskTimeouts();
    
    /**
     * @brief 重试失败的任务
     */
    void retryFailedTasks();
    
    /**
     * @brief 生成任务ID
     * @return String 唯一任务ID
     */
    String generateTaskId();
    
    /**
     * @brief 转换内部任务为TaskData
     * @param task 内部任务
     * @return TaskData 任务数据
     */
    TaskData convertToTaskData(const ScheduledTask* task) const;
    
    /**
     * @brief 查找任务
     * @param taskId 任务ID
     * @return ScheduledTask* 任务指针，未找到返回nullptr
     */
    ScheduledTask* findTask(const String& taskId);
    
    /**
     * @brief 调度器任务函数
     * @param parameter 参数
     */
    static void schedulerTask(void* parameter);
    
    /**
     * @brief 任务优先级比较函数
     * @param a 任务A
     * @param b 任务B
     * @return bool A的优先级是否低于B
     */
    static bool taskPriorityCompare(ScheduledTask* a, ScheduledTask* b);
};

#endif // TASK_SCHEDULER_H
