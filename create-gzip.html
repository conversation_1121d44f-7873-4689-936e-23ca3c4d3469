<!DOCTYPE html>
<html>
<head>
    <title>创建Gzip压缩文件</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .file-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .compress-btn { background: #007bff; color: white; border: none; padding: 5px 10px; cursor: pointer; }
        .result { margin-top: 10px; font-weight: bold; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>前端文件Gzip压缩工具</h1>
    <p>选择文件并点击压缩按钮来创建.gz版本</p>
    
    <input type="file" id="fileInput" multiple accept=".html,.css,.js,.json">
    <button onclick="compressFiles()">压缩选中文件</button>
    
    <div id="results"></div>

    <script>
        async function compressFile(file) {
            try {
                // 读取文件内容
                const arrayBuffer = await file.arrayBuffer();
                const uint8Array = new Uint8Array(arrayBuffer);
                
                // 使用CompressionStream进行gzip压缩
                const compressionStream = new CompressionStream('gzip');
                const writer = compressionStream.writable.getWriter();
                const reader = compressionStream.readable.getReader();
                
                // 写入数据
                writer.write(uint8Array);
                writer.close();
                
                // 读取压缩结果
                const chunks = [];
                let done = false;
                while (!done) {
                    const { value, done: readerDone } = await reader.read();
                    done = readerDone;
                    if (value) {
                        chunks.push(value);
                    }
                }
                
                // 合并chunks
                const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
                const result = new Uint8Array(totalLength);
                let offset = 0;
                for (const chunk of chunks) {
                    result.set(chunk, offset);
                    offset += chunk.length;
                }
                
                // 创建下载链接
                const blob = new Blob([result], { type: 'application/gzip' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = file.name + '.gz';
                a.click();
                URL.revokeObjectURL(url);
                
                const originalSize = uint8Array.length;
                const compressedSize = result.length;
                const ratio = ((1 - compressedSize / originalSize) * 100).toFixed(1);
                
                return {
                    success: true,
                    originalSize,
                    compressedSize,
                    ratio,
                    filename: file.name
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    filename: file.name
                };
            }
        }
        
        async function compressFiles() {
            const fileInput = document.getElementById('fileInput');
            const results = document.getElementById('results');
            
            if (!fileInput.files.length) {
                alert('请选择文件');
                return;
            }
            
            results.innerHTML = '<p>正在压缩文件...</p>';
            
            const resultItems = [];
            
            for (const file of fileInput.files) {
                const result = await compressFile(file);
                
                if (result.success) {
                    resultItems.push(`
                        <div class="file-item">
                            <div class="success">✅ ${result.filename}</div>
                            <div>原始大小: ${result.originalSize.toLocaleString()} bytes</div>
                            <div>压缩大小: ${result.compressedSize.toLocaleString()} bytes</div>
                            <div>压缩率: ${result.ratio}%</div>
                        </div>
                    `);
                } else {
                    resultItems.push(`
                        <div class="file-item">
                            <div class="error">❌ ${result.filename}</div>
                            <div>错误: ${result.error}</div>
                        </div>
                    `);
                }
            }
            
            results.innerHTML = resultItems.join('');
        }
        
        // 检查浏览器支持
        if (!window.CompressionStream) {
            document.body.innerHTML = '<h1>错误</h1><p>您的浏览器不支持CompressionStream API。请使用Chrome 80+或其他现代浏览器。</p>';
        }
    </script>
</body>
</html>
