/**
 * 虚拟滚动列表 - 高性能大列表渲染
 */
class VirtualScrollList {
  constructor(container, options = {}) {
    this.container = container;
    this.itemHeight = options.itemHeight || 60;
    this.bufferSize = options.bufferSize || 5;
    this.data = [];
    this.visibleStart = 0;
    this.visibleEnd = 0;
    this.scrollTop = 0;
    this.containerHeight = 0;
    this.totalHeight = 0;
    this.renderCache = new Map();
    this.performance = {
      renderedItems: 0,
      cacheHits: 0,
      cacheMisses: 0
    };

    this.init();
  }

  /**
   * 初始化虚拟滚动
   */
  init() {
    this.container.style.position = 'relative';
    this.container.style.overflow = 'auto';
    
    // 创建滚动容器
    this.scrollContainer = document.createElement('div');
    this.scrollContainer.style.position = 'absolute';
    this.scrollContainer.style.top = '0';
    this.scrollContainer.style.left = '0';
    this.scrollContainer.style.right = '0';
    
    // 创建内容容器
    this.contentContainer = document.createElement('div');
    this.contentContainer.style.position = 'relative';
    
    this.scrollContainer.appendChild(this.contentContainer);
    this.container.appendChild(this.scrollContainer);

    // 绑定滚动事件
    this.container.addEventListener('scroll', this.handleScroll.bind(this));
    
    // 监听容器大小变化
    if (window.ResizeObserver) {
      this.resizeObserver = new ResizeObserver(() => {
        this.updateContainerHeight();
        this.render();
      });
      this.resizeObserver.observe(this.container);
    }

    this.updateContainerHeight();
  }

  /**
   * 设置数据
   */
  setData(data) {
    this.data = data;
    this.totalHeight = data.length * this.itemHeight;
    this.scrollContainer.style.height = this.totalHeight + 'px';
    this.clearCache();
    this.render();
  }

  /**
   * 处理滚动事件
   */
  handleScroll() {
    const newScrollTop = this.container.scrollTop;
    if (Math.abs(newScrollTop - this.scrollTop) < this.itemHeight / 2) {
      return; // 避免频繁重渲染
    }
    
    this.scrollTop = newScrollTop;
    this.render();
  }

  /**
   * 更新容器高度
   */
  updateContainerHeight() {
    this.containerHeight = this.container.clientHeight;
  }

  /**
   * 计算可见范围
   */
  calculateVisibleRange() {
    const startIndex = Math.floor(this.scrollTop / this.itemHeight);
    const endIndex = Math.min(
      this.data.length,
      startIndex + Math.ceil(this.containerHeight / this.itemHeight)
    );

    // 添加缓冲区
    this.visibleStart = Math.max(0, startIndex - this.bufferSize);
    this.visibleEnd = Math.min(this.data.length, endIndex + this.bufferSize);
  }

  /**
   * 渲染可见项
   */
  render() {
    if (this.data.length === 0) {
      this.contentContainer.innerHTML = '';
      return;
    }

    this.calculateVisibleRange();
    
    const fragment = document.createDocumentFragment();
    const itemsToRender = [];

    // 收集需要渲染的项
    for (let i = this.visibleStart; i < this.visibleEnd; i++) {
      const item = this.data[i];
      if (item) {
        itemsToRender.push({ index: i, item });
      }
    }

    // 批量渲染
    for (const { index, item } of itemsToRender) {
      const element = this.renderItem(item, index);
      if (element) {
        element.style.position = 'absolute';
        element.style.top = (index * this.itemHeight) + 'px';
        element.style.left = '0';
        element.style.right = '0';
        element.style.height = this.itemHeight + 'px';
        fragment.appendChild(element);
      }
    }

    // 更新DOM
    this.contentContainer.innerHTML = '';
    this.contentContainer.appendChild(fragment);
    
    this.performance.renderedItems += itemsToRender.length;
  }

  /**
   * 渲染单个项目（可被子类重写）
   */
  renderItem(item, index) {
    const cacheKey = this.getCacheKey(item, index);
    
    // 检查缓存
    if (this.renderCache.has(cacheKey)) {
      this.performance.cacheHits++;
      return this.renderCache.get(cacheKey).cloneNode(true);
    }

    this.performance.cacheMisses++;
    
    // 创建新元素
    const element = document.createElement('div');
    element.className = 'virtual-list-item';
    element.innerHTML = this.getItemHTML(item, index);
    
    // 缓存元素
    if (this.renderCache.size < 1000) { // 限制缓存大小
      this.renderCache.set(cacheKey, element.cloneNode(true));
    }
    
    return element;
  }

  /**
   * 获取项目HTML（需要子类实现）
   */
  getItemHTML(item, index) {
    return `<div>Item ${index}: ${JSON.stringify(item)}</div>`;
  }

  /**
   * 获取缓存键
   */
  getCacheKey(item, index) {
    return `${index}_${item.id || JSON.stringify(item)}`;
  }

  /**
   * 滚动到指定项目
   */
  scrollToItem(index) {
    if (index < 0 || index >= this.data.length) return;
    
    const targetScrollTop = index * this.itemHeight;
    this.container.scrollTop = targetScrollTop;
  }

  /**
   * 滚动到顶部
   */
  scrollToTop() {
    this.container.scrollTop = 0;
  }

  /**
   * 滚动到底部
   */
  scrollToBottom() {
    this.container.scrollTop = this.totalHeight;
  }

  /**
   * 获取当前可见的项目索引范围
   */
  getVisibleRange() {
    return {
      start: this.visibleStart,
      end: this.visibleEnd,
      total: this.data.length
    };
  }

  /**
   * 更新单个项目
   */
  updateItem(index, newItem) {
    if (index >= 0 && index < this.data.length) {
      this.data[index] = newItem;
      
      // 清除相关缓存
      const oldCacheKey = this.getCacheKey(this.data[index], index);
      this.renderCache.delete(oldCacheKey);
      
      // 如果项目在可见范围内，重新渲染
      if (index >= this.visibleStart && index < this.visibleEnd) {
        this.render();
      }
    }
  }

  /**
   * 添加项目
   */
  addItem(item, index = -1) {
    if (index === -1 || index >= this.data.length) {
      this.data.push(item);
    } else {
      this.data.splice(index, 0, item);
    }
    
    this.totalHeight = this.data.length * this.itemHeight;
    this.scrollContainer.style.height = this.totalHeight + 'px';
    this.clearCache(); // 索引变化，清除缓存
    this.render();
  }

  /**
   * 删除项目
   */
  removeItem(index) {
    if (index >= 0 && index < this.data.length) {
      this.data.splice(index, 1);
      this.totalHeight = this.data.length * this.itemHeight;
      this.scrollContainer.style.height = this.totalHeight + 'px';
      this.clearCache(); // 索引变化，清除缓存
      this.render();
    }
  }

  /**
   * 清除渲染缓存
   */
  clearCache() {
    this.renderCache.clear();
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return {
      ...this.performance,
      cacheSize: this.renderCache.size,
      hitRate: this.performance.cacheHits + this.performance.cacheMisses > 0
        ? (this.performance.cacheHits / (this.performance.cacheHits + this.performance.cacheMisses) * 100).toFixed(1) + '%'
        : '0%',
      visibleItems: this.visibleEnd - this.visibleStart,
      totalItems: this.data.length
    };
  }

  /**
   * 销毁虚拟滚动
   */
  destroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    
    this.container.removeEventListener('scroll', this.handleScroll);
    this.clearCache();
    
    if (this.scrollContainer && this.scrollContainer.parentNode) {
      this.scrollContainer.parentNode.removeChild(this.scrollContainer);
    }
  }
}

// 导出类
window.VirtualScrollList = VirtualScrollList;
