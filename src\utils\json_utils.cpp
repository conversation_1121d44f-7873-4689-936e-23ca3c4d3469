#include "json_utils.h"

// ==================== JSON验证 ====================

bool JsonUtils::isValidJson(const String& jsonString) {
    JsonDocument doc;
    DeserializationError error = deserializeJson(doc, jsonString);
    return error == DeserializationError::Ok;
}

bool JsonUtils::hasRequiredFields(const JsonDocument& doc, const std::vector<String>& requiredFields) {
    for (const String& field : requiredFields) {
        if (!hasField(doc, field)) {
            return false;
        }
    }
    return true;
}

bool JsonUtils::validateFieldType(const JsonDocument& doc, const String& fieldName, const String& expectedType) {
    JsonVariantConst value = getNestedValue(doc, fieldName);
    
    if (value.isNull()) {
        return false;
    }
    
    if (expectedType == "string") {
        return value.is<String>();
    } else if (expectedType == "number") {
        return value.is<int>() || value.is<float>() || value.is<double>();
    } else if (expectedType == "boolean") {
        return value.is<bool>();
    } else if (expectedType == "array") {
        return value.is<JsonArray>();
    } else if (expectedType == "object") {
        return value.is<JsonObject>();
    }
    
    return false;
}

// ==================== JSON转换 ====================

bool JsonUtils::stringToJson(const String& jsonString, JsonDocument& doc) {
    DeserializationError error = deserializeJson(doc, jsonString);
    return error == DeserializationError::Ok;
}

String JsonUtils::jsonToString(const JsonDocument& doc, bool prettyPrint) {
    String result;
    
    if (prettyPrint) {
        serializeJsonPretty(doc, result);
    } else {
        serializeJson(doc, result);
    }
    
    return result;
}

bool JsonUtils::mergeJsonObjects(JsonDocument& target, const JsonDocument& source, bool overwrite) {
    if (!source.is<JsonObject>() || !target.is<JsonObject>()) {
        return false;
    }
    
    JsonObject targetObj = target.as<JsonObject>();
    JsonObjectConst sourceObj = source.as<JsonObjectConst>();
    
    for (JsonPairConst pair : sourceObj) {
        const char* key = pair.key().c_str();
        JsonVariantConst value = pair.value();
        
        if (!targetObj[key].is<JsonVariant>() || overwrite) {
            targetObj[key] = value;
        }
    }
    
    return true;
}

// ==================== JSON查询 ====================

JsonVariantConst JsonUtils::getNestedValue(const JsonDocument& doc, const String& path) {
    std::vector<String> pathComponents = splitPath(path);
    JsonVariantConst current = doc.as<JsonVariantConst>();
    
    for (const String& component : pathComponents) {
        if (current.is<JsonObject>()) {
            current = current[component];
        } else {
            return JsonVariantConst();  // 返回null
        }
    }
    
    return current;
}

bool JsonUtils::setNestedValue(JsonDocument& doc, const String& path, const JsonVariant& value) {
    std::vector<String> pathComponents = splitPath(path);
    
    if (pathComponents.empty()) {
        return false;
    }
    
    JsonObject current = doc.as<JsonObject>();
    
    // 导航到最后一级的父对象
    for (size_t i = 0; i < pathComponents.size() - 1; i++) {
        const String& component = pathComponents[i];
        
        if (!current[component].is<JsonVariant>()) {
            current[component].to<JsonObject>();
        }
        
        JsonVariant currentVariant = current[component];
        if (!currentVariant.is<JsonObject>()) {
            return false;
        }
        current = currentVariant.as<JsonObject>();
    }
    
    // 设置最终值
    current[pathComponents.back()] = value;
    return true;
}

bool JsonUtils::hasField(const JsonDocument& doc, const String& path) {
    JsonVariantConst value = getNestedValue(doc, path);
    return !value.isNull();
}

// ==================== JSON数组操作 ====================

bool JsonUtils::addToArray(JsonArray& array, const JsonVariant& value) {
    return array.add(value);
}

bool JsonUtils::removeFromArray(JsonArray& array, size_t index) {
    if (index >= array.size()) {
        return false;
    }
    
    array.remove(index);
    return true;
}

int JsonUtils::findInArray(const JsonArray& array, const JsonVariant& value) {
    for (size_t i = 0; i < array.size(); i++) {
        if (array[i] == value) {
            return static_cast<int>(i);
        }
    }
    return -1;
}

// ==================== JSON过滤和排序 ====================

JsonDocument JsonUtils::filterFields(const JsonDocument& source, const std::vector<String>& allowedFields) {
    JsonDocument result;
    JsonObject resultObj = result.to<JsonObject>();
    JsonObjectConst sourceObj = source.as<JsonObjectConst>();
    
    for (const String& field : allowedFields) {
        if (sourceObj[field].is<JsonVariant>()) {
            resultObj[field] = sourceObj[field];
        }
    }
    
    return result;
}

JsonDocument JsonUtils::excludeFields(const JsonDocument& source, const std::vector<String>& excludedFields) {
    JsonDocument result;
    JsonObject resultObj = result.to<JsonObject>();
    JsonObjectConst sourceObj = source.as<JsonObjectConst>();
    
    for (JsonPairConst pair : sourceObj) {
        const char* key = pair.key().c_str();
        bool shouldExclude = false;
        
        for (const String& excludedField : excludedFields) {
            if (excludedField == key) {
                shouldExclude = true;
                break;
            }
        }
        
        if (!shouldExclude) {
            resultObj[key] = pair.value();
        }
    }
    
    return result;
}

// ==================== JSON格式化 ====================

String JsonUtils::compactJson(const String& jsonString) {
    JsonDocument doc;
    if (stringToJson(jsonString, doc)) {
        return jsonToString(doc, false);
    }
    return jsonString;
}

String JsonUtils::formatJson(const String& jsonString, int indent) {
    JsonDocument doc;
    if (stringToJson(jsonString, doc)) {
        return jsonToString(doc, true);
    }
    return jsonString;
}

// ==================== JSON统计 ====================

size_t JsonUtils::calculateSize(const JsonDocument& doc) {
    return measureJson(doc);
}

size_t JsonUtils::countFields(const JsonDocument& doc, bool recursive) {
    if (recursive) {
        return countFieldsRecursive(doc.as<JsonVariantConst>());
    } else {
        if (doc.is<JsonObject>()) {
            return doc.as<JsonObjectConst>().size();
        }
        return 0;
    }
}

size_t JsonUtils::getDepth(const JsonDocument& doc) {
    return calculateDepthRecursive(doc.as<JsonVariantConst>(), 0);
}

// ==================== JSON安全性 ====================

String JsonUtils::sanitizeJsonString(const String& jsonString) {
    String sanitized = jsonString;
    
    // 移除潜在的危险字符
    sanitized.replace("\\", "\\\\");
    sanitized.replace("\"", "\\\"");
    sanitized.replace("\n", "\\n");
    sanitized.replace("\r", "\\r");
    sanitized.replace("\t", "\\t");
    
    return sanitized;
}

bool JsonUtils::validateLength(const String& jsonString, size_t maxLength) {
    return jsonString.length() <= maxLength;
}

bool JsonUtils::validateDepth(const JsonDocument& doc, size_t maxDepth) {
    return getDepth(doc) <= maxDepth;
}

// ==================== 错误处理 ====================

String JsonUtils::getErrorMessage(DeserializationError error) {
    switch (error.code()) {
        case DeserializationError::Ok:
            return "成功";
        case DeserializationError::InvalidInput:
            return "输入无效";
        case DeserializationError::NoMemory:
            return "内存不足";
        case DeserializationError::TooDeep:
            return "JSON嵌套过深";
        default:
            return "未知错误: " + String(error.c_str());
    }
}

JsonDocument JsonUtils::createErrorResponse(const String& errorCode, 
                                          const String& errorMessage, 
                                          const String& details) {
    JsonDocument doc;
    doc["success"] = false;
    doc["error"]["code"] = errorCode;
    doc["error"]["message"] = errorMessage;
    if (!details.isEmpty()) {
        doc["error"]["details"] = details;
    }
    doc["timestamp"] = millis();
    
    return doc;
}

JsonDocument JsonUtils::createSuccessResponse(const JsonDocument& data, 
                                            const String& message) {
    JsonDocument doc;
    doc["success"] = true;
    doc["data"] = data;
    if (!message.isEmpty()) {
        doc["message"] = message;
    }
    doc["timestamp"] = millis();
    
    return doc;
}

// ==================== 私有辅助方法 ====================

size_t JsonUtils::calculateDepthRecursive(const JsonVariantConst& value, size_t currentDepth) {
    size_t maxDepth = currentDepth;
    
    if (value.is<JsonObject>()) {
        JsonObjectConst obj = value.as<JsonObjectConst>();
        for (JsonPairConst pair : obj) {
            size_t childDepth = calculateDepthRecursive(pair.value(), currentDepth + 1);
            if (childDepth > maxDepth) {
                maxDepth = childDepth;
            }
        }
    } else if (value.is<JsonArray>()) {
        JsonArrayConst arr = value.as<JsonArrayConst>();
        for (JsonVariantConst element : arr) {
            size_t childDepth = calculateDepthRecursive(element, currentDepth + 1);
            if (childDepth > maxDepth) {
                maxDepth = childDepth;
            }
        }
    }
    
    return maxDepth;
}

size_t JsonUtils::countFieldsRecursive(const JsonVariantConst& value) {
    size_t count = 0;
    
    if (value.is<JsonObject>()) {
        JsonObjectConst obj = value.as<JsonObjectConst>();
        count += obj.size();

        for (JsonPairConst pair : obj) {
            count += countFieldsRecursive(pair.value());
        }
    } else if (value.is<JsonArray>()) {
        JsonArrayConst arr = value.as<JsonArrayConst>();
        for (JsonVariantConst element : arr) {
            count += countFieldsRecursive(element);
        }
    }
    
    return count;
}

std::vector<String> JsonUtils::splitPath(const String& path) {
    std::vector<String> components;
    String current = "";
    
    for (int i = 0; i < path.length(); i++) {
        char c = path.charAt(i);
        if (c == '.') {
            if (!current.isEmpty()) {
                components.push_back(current);
                current = "";
            }
        } else {
            current += c;
        }
    }
    
    if (!current.isEmpty()) {
        components.push_back(current);
    }
    
    return components;
}
